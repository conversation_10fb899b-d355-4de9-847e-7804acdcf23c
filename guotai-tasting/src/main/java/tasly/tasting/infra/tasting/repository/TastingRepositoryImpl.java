package tasly.tasting.infra.tasting.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import tasly.tasting.client.common.exception.AppException;
import tasly.tasting.domain.BaseEntity;
import tasly.tasting.domain.tasting.TastingEntity;
import tasly.tasting.domain.tasting.enums.TastingApprovalStatusEnum;
import tasly.tasting.domain.tasting.enums.TastingConfirmStatusEnum;
import tasly.tasting.domain.tasting.enums.TastingExecutionStatusEnum;
import tasly.tasting.domain.tasting.repository.TastingRepository;
import tasly.tasting.infra.tasting.repository.mapper.TastingMapper;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Repository
public class TastingRepositoryImpl implements TastingRepository {

    @Resource
    private TastingMapper tastingMapper;

    @Override
    public Page<TastingEntity> pageWithPermission(Integer pageNum, Integer pageSize, TastingEntity queryParam, String currentPersonCode) {
        LambdaQueryWrapper<TastingEntity> queryWrapper = Wrappers.lambdaQuery(TastingEntity.class);
        queryWrapper.orderByDesc(BaseEntity::getCreateTime, BaseEntity::getId);
        queryWrapper.eq(TastingEntity::getApprovalStatus, TastingApprovalStatusEnum.APPROVED.name());
        if (StringUtils.isNoneBlank(queryParam.getExecutionStatus())) {
            queryWrapper.eq(TastingEntity::getExecutionStatus, queryParam.getExecutionStatus());
        }
        if (StringUtils.isNoneBlank(queryParam.getTypeCode())) {
            queryWrapper.eq(TastingEntity::getTypeCode, queryParam.getTypeCode());
        }
        queryWrapper.and(p -> p.eq(TastingEntity::getResponsiblePersonCode, currentPersonCode)
                .or()
                .inSql(TastingEntity::getId, "select tasting_id from tst_tst_tasting_execute_person where dr = 0 and execute_person_code = '" + currentPersonCode + "'")
                .or()
                .inSql(TastingEntity::getId, "select tasting_id from tst_tst_tasting_assign_relation where dr = 0 and assign_person_code = '" + currentPersonCode + "'")

        );
        return tastingMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);
    }

    @Override
    public TastingEntity getById(Long id) {
        return tastingMapper.selectById(id);
    }

    @Override
    public List<TastingEntity> typeList(String currentPersonCode) {
        LambdaQueryWrapper<TastingEntity> queryWrapper = Wrappers.lambdaQuery(TastingEntity.class)
                .select(TastingEntity::getTypeCode, TastingEntity::getTypeName)
                .groupBy(TastingEntity::getTypeCode, TastingEntity::getTypeName);
        queryWrapper.and(p -> p.eq(TastingEntity::getResponsiblePersonCode, currentPersonCode)
                .or()
                .inSql(TastingEntity::getId, "select tasting_id from tst_tst_tasting_execute_person where dr = 0 and execute_person_code = '" + currentPersonCode + "'")
                .or()
                .inSql(TastingEntity::getId, "select tasting_id from tst_tst_tasting_assign_relation where dr = 0 and assign_person_code = '" + currentPersonCode + "'")

        );
        return tastingMapper.selectList(queryWrapper);
    }

    @Override
    public int confirm(Long id) {
        return tastingMapper.update(new TastingEntity(), Wrappers.lambdaUpdate(TastingEntity.class)
                .set(TastingEntity::getConfirmStatus, TastingConfirmStatusEnum.CONFIRMED.name())
                .eq(TastingEntity::getId, id)
                .eq(TastingEntity::getApprovalStatus, TastingApprovalStatusEnum.APPROVED.name())
                .eq(TastingEntity::getExecutionStatus, TastingExecutionStatusEnum.RUNNING.name())
        );
    }

    @Override
    public int submit(Long id) {
        return tastingMapper.update(new TastingEntity(), Wrappers.lambdaUpdate(TastingEntity.class)
                .set(TastingEntity::getExecutionStatus, TastingExecutionStatusEnum.FINISHED.name())
                .eq(TastingEntity::getId, id)
                .eq(TastingEntity::getExecutionStatus, TastingExecutionStatusEnum.RUNNING.name())
        );
    }

    @Override
    public int save(TastingEntity tastingEntity) {
        TastingEntity databaseRecord = tastingMapper.selectOne(
                Wrappers.lambdaQuery(TastingEntity.class)
                        .eq(TastingEntity::getActId, tastingEntity.getActId())
        );
        if (Objects.isNull(databaseRecord)) {
            return tastingMapper.insert(tastingEntity);
        } else {
            throw new AppException("品鉴会已存在");
            /*
            return tastingMapper.update(tastingEntity, Wrappers.lambdaUpdate(TastingEntity.class)
                    .eq(TastingEntity::getId, databaseRecord.getId())
                    .eq(TastingEntity::getExecutionStatus, TastingExecutionStatusEnum.RUNNING.name())
            );
             */
        }
    }

    @Override
    public int abandon(String actId) {
        return tastingMapper.update(new TastingEntity(), Wrappers.lambdaUpdate(TastingEntity.class)
                .set(TastingEntity::getApprovalStatus, TastingApprovalStatusEnum.ABANDONED.name())
                .eq(TastingEntity::getActId, actId)
        );
    }

    @Override
    public TastingEntity queryTastingByActid(String actId) {
        return tastingMapper.selectOne(
                Wrappers.lambdaQuery(TastingEntity.class)
                .eq(TastingEntity::getActId,actId));
    }

    @Override
    public int saveCostControl(TastingEntity tastingEntity) {
        TastingEntity databaseRecord = tastingMapper.selectOne(
                Wrappers.lambdaQuery(TastingEntity.class)
                        .eq(TastingEntity::getActId, tastingEntity.getActId())
        );
        if (Objects.isNull(databaseRecord)) {
            return tastingMapper.insert(tastingEntity);
        } else {
            return tastingMapper.updateById(tastingEntity);
        }
    }

    @Override
    public List<TastingEntity> queryTastingForCostControl(Long tastingId) {
        return tastingMapper.queryTastingForCostControl(tastingId);
    }
}
