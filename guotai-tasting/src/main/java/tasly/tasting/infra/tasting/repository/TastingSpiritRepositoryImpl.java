package tasly.tasting.infra.tasting.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;
import tasly.tasting.domain.tasting.TastingSpiritEntity;
import tasly.tasting.domain.tasting.repository.TastingSpiritRepository;
import tasly.tasting.infra.tasting.repository.mapper.TastingSpiritMapper;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/6 17:09
 */
@Repository
public class TastingSpiritRepositoryImpl implements TastingSpiritRepository {

    @Resource
    private TastingSpiritMapper tastingSpiritMapper;

    @Override
    public void deleteByTaskId(Long taskId) {
        tastingSpiritMapper.delete(
                Wrappers.lambdaQuery(TastingSpiritEntity.class)
                        .eq(TastingSpiritEntity::getTaskId, taskId)
        );
    }

    @Override
    public void insertBatch(List<TastingSpiritEntity> tastingSpiritEntityList) {
        tastingSpiritEntityList.forEach(e -> tastingSpiritMapper.insert(e));
    }

    @Override
    public List<TastingSpiritEntity> getByTaskId(Long taskId) {
        return tastingSpiritMapper.selectList(
                Wrappers.lambdaQuery(TastingSpiritEntity.class)
                        .eq(TastingSpiritEntity::getTaskId, taskId)
        );
    }
}
