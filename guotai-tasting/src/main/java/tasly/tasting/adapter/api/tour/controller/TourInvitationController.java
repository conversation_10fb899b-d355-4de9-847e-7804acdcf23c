package tasly.tasting.adapter.api.tour.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tasly.tasting.adapter.common.RestResponse;
import tasly.tasting.client.tour.api.TourInvitationService;
import tasly.tasting.client.tour.dto.response.TourInvitationResponseDTO;
import tasly.tasting.domain.tasting.enums.InvitationTypeEnum;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/v1/tour/invitation")
@Tag(name = "返厂游邀请函")
public class TourInvitationController {

    @Resource
    private TourInvitationService tourInvitationService;

    @GetMapping("/query")
    @Operation(summary = "获取返厂游邀请函", description = "获取返厂游邀请函")
    public RestResponse<TourInvitationResponseDTO> queryTastingInvitation(String tourId, String width){
        return RestResponse.success(tourInvitationService.queryTourInvitation(tourId,width, InvitationTypeEnum.TOUR_INVITE.name()));
    }

    @GetMapping("/sign")
    @Operation(summary = "获取返厂游签到二维码", description = "获取返厂游签到二维码")
    public RestResponse<TourInvitationResponseDTO> queryTastingSign(String tourId,String width){
        return RestResponse.success(tourInvitationService.queryTourInvitation(tourId,width, InvitationTypeEnum.TOUR_SIGN.name()));
    }
}
