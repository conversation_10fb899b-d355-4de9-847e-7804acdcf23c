package tasly.tasting.adapter.api.tasting.controller;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tasly.tasting.adapter.common.RestResponse;
import tasly.tasting.client.tasting.api.SignupRecordAppService;
import tasly.tasting.client.tour.api.TourQuestionnaireAppService;
import tasly.tasting.client.tasting.api.query.TastingAppQueryService;
import tasly.tasting.client.tour.api.query.TourQuestionnaireAppQueryService;
import tasly.tasting.client.tasting.dto.request.SignupRecordSaveReqDTO;
import tasly.tasting.client.tour.dto.request.TourQuestionnaireReqDTO;
import tasly.tasting.client.tour.dto.response.TourQuestionnaireRespDTO;
import tasly.tasting.client.tasting.dto.response.TastingTocResponseDTO;
import tasly.tasting.domain.tasting.enums.SignupRecordTypeEnum;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13 16:34
 */
@Slf4j
@RestController
@RequestMapping("/v1/toc")
@Tag(name = "提供品鉴会C端调用服务")
public class TastingTocController {

    @Resource
    private SignupRecordAppService signupRecordAppService;
    @Resource
    private TastingAppQueryService tastingAppQueryService;

    /**
     * 品鉴会报名
     * @param reqDTO
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "品鉴会报名", description = "品鉴会报名")
    public RestResponse saveSignupRecord(@RequestBody SignupRecordSaveReqDTO reqDTO){
        log.info("品鉴会报名数据同步接口入参：{}", JSONUtil.toJsonStr(reqDTO));
        reqDTO.setType(SignupRecordTypeEnum.ENTER.getValue());
        signupRecordAppService.saveSignupRecord(reqDTO);
        return RestResponse.success();
    }

    /**
     * 品鉴会签到
     * @param reqDTO
     * @return
     */
    @PostMapping("/signup")
    @Operation(summary = "品鉴会签到", description = "品鉴会签到")
    public RestResponse signupRecord(@RequestBody SignupRecordSaveReqDTO reqDTO){
        log.info("品鉴会签到数据同步接口入参：{}", JSONUtil.toJsonStr(reqDTO));
        reqDTO.setType(SignupRecordTypeEnum.SIGNUP.getValue());
        signupRecordAppService.saveSignupRecord(reqDTO);
        return RestResponse.success();
    }

    /**
     * 根据id查询品鉴会详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据id查询品鉴会详情", description = "根据id查询品鉴会详情")
    public RestResponse<TastingTocResponseDTO> getById(@PathVariable("id") Long id) {
        log.info("查询品鉴会详情数据同步接口入参：id:{}", id);
        return RestResponse.success(tastingAppQueryService.getTastingById(id));
    }
}
