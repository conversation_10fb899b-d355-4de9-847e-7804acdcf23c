package tasly.tasting.adapter.api.tasting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tasly.tasting.adapter.common.RestResponse;
import tasly.tasting.client.tasting.api.TastingPrizeTaskAppService;
import tasly.tasting.client.tasting.api.query.TastingPrizeTaskAppQueryService;
import tasly.tasting.client.tasting.dto.request.TastingPrizeReqDTO;
import tasly.tasting.client.tasting.dto.request.TastingPrizeStatusReqDTO;
import tasly.tasting.client.tasting.dto.response.TastingPrizeRespDTO;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/19 10:29
 */
@Slf4j
@RestController
@RequestMapping("/v1/tastingPrize")
@Tag(name = "品鉴会奖品设置服务")
public class TastingPrizeTaskController {
    @Resource
    private TastingPrizeTaskAppService tastingPrizeTaskAppService;
    @Resource
    private TastingPrizeTaskAppQueryService tastingPrizeTaskAppQueryService;

    @GetMapping("/queryTastingPrize/{tastingId}")
    @Operation(summary = "查询品鉴会奖项", description = "查询品鉴会奖项")
    public RestResponse<TastingPrizeRespDTO> queryTastingPrize(@PathVariable("tastingId") Long tastingId){
        return RestResponse.success(tastingPrizeTaskAppQueryService.queryTastingPrize(tastingId));
    }

    @PostMapping("/updateTastingPrizeTaskStatus")
    @Operation(summary = "更新奖品配置状态", description = "更新奖品配置状态")
    public RestResponse updateTastingPrizeTaskStatus(@RequestBody TastingPrizeStatusReqDTO reqDTO){
        tastingPrizeTaskAppService.updateTastingPrizeTaskStatus(reqDTO);
        return RestResponse.success();
    }

    @GetMapping("/deleteTastingPrizeDetail/{id}")
    @Operation(summary = "删除奖项", description = "更新奖品配置状态")
    public RestResponse deleteTastingPrizeDetail(@PathVariable("id")Long id){
        tastingPrizeTaskAppService.deleteTastingPrizeDetail(id);
        return RestResponse.success();
    }
    @PostMapping("/saveTastingPrize")
    @Operation(summary = "保存奖品配置状态", description = "保存奖品配置状态")
    public RestResponse saveTastingPrize(@RequestBody TastingPrizeReqDTO reqDTO){
        tastingPrizeTaskAppService.saveTastingPrize(reqDTO);
        return RestResponse.success();
    }

    /**
     * 大屏抽奖配置主题，不需要登录B端
     */
    @PostMapping("/updateTastingPrizeTaskTheme")
    @Operation(summary = "更新奖品配置状态", description = "更新奖品配置状态")
    public RestResponse updateTastingPrizeTaskTheme(@RequestBody TastingPrizeStatusReqDTO reqDTO){
        tastingPrizeTaskAppService.updateTastingPrizeTaskTheme(reqDTO);
        return RestResponse.success();
    }

}
