package tasly.tasting.adapter.api.tour.controller;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import tasly.tasting.adapter.common.RestResponse;
import tasly.tasting.client.tour.api.TourQuestionnaireAppService;
import tasly.tasting.client.tour.api.TourSignupRecordService;
import tasly.tasting.client.tour.api.query.TourQueryService;
import tasly.tasting.client.tour.api.query.TourQuestionnaireAppQueryService;
import tasly.tasting.client.tour.dto.request.TourQuestionnaireReqDTO;
import tasly.tasting.client.tour.dto.request.TourSignUpDTO;
import tasly.tasting.client.tour.dto.request.TourSignupRecordSaveReqDTO;
import tasly.tasting.client.tour.dto.response.TourQuestionnaireRespDTO;
import tasly.tasting.domain.tasting.enums.SignupRecordTypeEnum;
import tasly.tasting.domain.tour.TourEntity;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/v1/toc/tour")
@Tag(name = "提供返厂游C端调用服务")
public class TourTocController {

    @Resource
    private TourSignupRecordService tourSignupRecordService;
    @Resource
    private TourQueryService tourQueryService;
    @Resource
    private TourQuestionnaireAppQueryService questionnaireAppQueryService;
    @Resource
    private TourQuestionnaireAppService questionnaireAppService;

    /**
     * 返厂游报名
     * @param reqDTO
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "返厂游报名", description = "返厂游报名")
    public RestResponse saveSignupRecord(@RequestBody TourSignUpDTO reqDTO){
        log.info("返厂游报名数据同步接口入参：{}", JSONUtil.toJsonStr(reqDTO));
        reqDTO.setType(SignupRecordTypeEnum.ENTER.getValue());
        tourSignupRecordService.saveSignupRecord(reqDTO);
        return RestResponse.success();
    }

    /**
     * 返厂游签到
     * @param reqDTO
     * @return
     */
    @PostMapping("/signup")
    @Operation(summary = "返厂游签到", description = "返厂游签到")
    public RestResponse signupRecord(@RequestBody TourSignupRecordSaveReqDTO reqDTO){
        log.info("返厂游签到数据同步接口入参：{}", JSONUtil.toJsonStr(reqDTO));
        reqDTO.setType(SignupRecordTypeEnum.SIGNUP.getValue());
        tourSignupRecordService.signIn(reqDTO);
        return RestResponse.success();
    }

    /**
     * 根据id查询返厂游详情
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据id查询返厂游详情", description = "根据id查询返厂游详情")
    public RestResponse<TourEntity> getById(@PathVariable("id") Long id) {
        log.info("查询返厂游详情数据同步接口入参：id:{}", id);
        return RestResponse.success(tourQueryService.getById(id));
    }

    /**
     * 问卷调查查询
     * @param openId
     * @param tourId
     * @return
     */
    @GetMapping("/queryQuestionnaire/{openId}/{tourId}")
    @Operation(summary = "问卷调查查询", description = "问卷调查查询")
    public RestResponse<TourQuestionnaireRespDTO> queryQuestionnaire(@PathVariable("openId")String openId, @PathVariable("tourId")String tourId){
        return RestResponse.success(questionnaireAppQueryService.queryQuestionnaire(openId,tourId));
    }

    /**
     * 问卷调查保存
     * @param questionnaireReqDTO
     * @return
     */
    @PostMapping("/saveQuestionnaire")
    @Operation(summary = "问卷调查保存", description = "问卷调查保存")
    public RestResponse saveQuestionnaire(@RequestBody TourQuestionnaireReqDTO questionnaireReqDTO){
        log.info("提交问卷调查数据：{}", JSONUtil.toJsonStr(questionnaireReqDTO));
        questionnaireAppService.saveQuestionnaire(questionnaireReqDTO);
        return RestResponse.success();
    }
}
