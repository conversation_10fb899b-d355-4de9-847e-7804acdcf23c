package tasly.tasting.client.tour.api.query;

import tasly.tasting.client.tasting.dto.response.PageRespDTO;
import tasly.tasting.client.tour.dto.request.TourSignupRecordPageQueryReqDTO;
import tasly.tasting.client.tour.dto.response.TourSignupRecordPageRespDTO;

public interface TourSignupRecordQueryService {
    /**
     * 分页查询报名列表
     * @param reqDTO
     * @return
     */
    PageRespDTO<TourSignupRecordPageRespDTO> page(TourSignupRecordPageQueryReqDTO reqDTO);

}
