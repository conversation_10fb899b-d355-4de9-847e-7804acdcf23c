package tasly.tasting.client.tasting.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "SignupRecordPageQueryReqDTO", description = "品鉴会报名列表分页查询reqDto")
@Data
public class SignupRecordPageQueryReqDTO extends PageReqDTO {

    /**
     * 品鉴会活动id
     */
    @Schema(name = "tastingId", description = "品鉴会活动id")
    private String tastingId;
    /**
     * 类型
     */
    @Schema(name = "type", description = "签到类型")
    private String type;
}
