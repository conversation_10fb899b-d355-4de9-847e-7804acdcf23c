package tasly.tasting.client.tasting.dto.response;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/10 10:11
 */
@Schema(name = "PhotographicTypeRespDTO", description = "品鉴会现场照片类型respDto")
@Data
public class PhotographicTypeRespDTO {

    @Schema(name = "tastingId", description = "所属品鉴会id")
    private Long tastingId;

    @Schema(name = "fieldKey", description = "fieldKey")
    private String fieldKey;

    @Schema(name = "fieldValue", description = "fieldValue")
    private String fieldValue;

    @Schema(name = "required", description = "是否必填 0-否 1-是")
    private Integer required;

    @Schema(name = "control_type", description = "文件类型 1-附件 2-图片")
    private Integer controlType;
}
