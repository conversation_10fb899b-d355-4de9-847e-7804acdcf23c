package tasly.tasting.client.tasting.api;

import tasly.tasting.client.tasting.dto.request.*;

public interface TastingAppService {
    /**
     * 品鉴会确认
     *
     * @param reqDTO 品鉴会确认reqDto
     */
    void confirm(TastingConfirmReqDTO reqDTO);

    /**
     * 品鉴会绑定指派关系
     *
     * @param reqDTO 品鉴会绑定指派关系reqDto
     */
    void bindAssignRelation(TastingAssignReqDTO reqDTO);

    /**
     * 品鉴会核报
     *
     * @param reqDTO 品鉴会核报reqDto
     */
    void submit(TastingSubmitReqDTO reqDTO);

    /**
     * 品鉴会数据同步
     *
     * @param reqDTO 品鉴会数据同步reqDto
     */
    void sync(TastingSyncReqDTO reqDTO);

    /**
     * 品鉴会取消
     *
     * @param reqDTO 品鉴会取消reqDto
     */
    void abandon(TastingSyncReqDTO reqDTO);

    /**
     * 品鉴会手动创建
     *
     * @param reqDTO 品鉴会数据同步reqDto
     */
    void insert(TastingInsertReqDTO reqDTO);

    /**
     * 同步费控信息
     * @param reqDTO
     */
    void costControlsync(TastingCostControlSyncReqDTO reqDTO);
}
