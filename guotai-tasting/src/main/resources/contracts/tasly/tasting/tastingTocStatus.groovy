package contracts.tasly.tasting

import org.springframework.cloud.contract.spec.Contract

[

        Contract.make {

            description "向C端同步品鉴会状态"

            request {
                method POST()
                url("/app/tasting/activity/sync")
            }
            response {
                status OK()
                body("{\n" +
                        "\t\"code\": 0,\n" +
                        "\t\"msg\": \"OK\",\n" +
                        "}")
                headers {
                    contentType(applicationJson())
                }
            }
        }
]
