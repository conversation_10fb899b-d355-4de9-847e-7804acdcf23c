package contracts.tasly.tasting

import org.springframework.cloud.contract.spec.Contract

[

        Contract.make {

            description "邀请函获取C端二维码"

            request {
                method GET()
                url("/app/tasting/qrCode?activityId=zcftest&shareUserId=uid&type=INVITE")
            }
            response {
                status OK()
                body("{\n" +
                        "\t\"code\": 0,\n" +
                        "\t\"msg\": \"OK\",\n" +
                        "\t\"url\": \"https://guofenclubnew.oss-cn-beijing.aliyuncs.com/gttour/pic/2023/J4zRB1abCbbY51c1af4b8073f458c512cabaee6fdfdd.png\"\n" +
                        "}")
                headers {
                    contentType(applicationJson())
                }
            }
        }
]
