spring:
  datasource:
    url: *******************************************************************************************************************************************************
    username: cloud_shop
    password: cloud_shop
    driverClassName: com.mysql.cj.jdbc.Driver
  redis:
    database: 0
    host: ************
    port: 6379
    password: guotai2020
    timeout: 10000

server:
  port: 8080
  servlet:
    context-path: /guotai-tasting

xuanwu:
  submit:
    #url: http://localhost:8181/tasting/submit
    url: http://**************:7000/api/teapi/dy-biz/1547017894355931233/1681488850008019041
  scan:
    #url: http://localhost:8181/api/teapi/dy-biz/1403256332726964315/1552854596869820515
    url: http://**************:7000/api/teapi/dy-biz/1403256332726964315/1552854596869820515
  status:
    url: http://**************:7000/api/teapi/dy-biz/1682204602625298531/1706148069277569121
  openapi:
    token:
      #url: http://localhost:8181/api/auth/openlogin
      url: http://**************:7000/api/auth/openlogin
      accountinfocode: 1733052964442083328
      opentypecode: guobiaoshi-data-server
      opentypesecret: gbs8888
      clienttypecode: 1

trace:
  manufactureQuery:
    url: https://gtsytest-linshi.tasly.com/enterprisewx/facade/tasting
    # url: http://localhost:8181/enterprisewx/facade/tasting/queryManufactureInfo
tasting:
  toc:
    url: http://*************:9999/mall-api-tasting
    #url: http://localhost:8181
    username: user
    password: Tasly123!

redisson:
  lock:
    address: ************:6379
    password: guotai2020


#oss
aliyunoss:
  folder: gttour
  order: cloud-shop-orderExcel
  key: LTAI5tDCGdkQ3TZbH183Knpu
  secret: ******************************
  bucket: guofenclubnew
  domain: oss-cn-beijing.aliyuncs.com
local:
  order:
    folder: orderFolder

# oss配置信息
aliyun:
  oss:
    accessId: LTAI5tDCGdkQ3TZbH183Knpu
    accessKey: ******************************
    endpoint: oss-cn-beijing.aliyuncs.com
    bucket: guofenclubnew
    ahost: guofenclubnew.oss-cn-beijing.aliyuncs.com
    expireTime: 30
    min: 0
    max: 10485760
    dir: gttour
    calbackUrl: https://cloudshopapi.baijiuyun.com/guotai-terminal-dev/oss/callback
    host: guofenclubnew.oss-cn-beijing.aliyuncs.com
stubrunner:
  ids: tasly:tasting:+:stubs:8181
  repositoryRoot: stubs://classpath:contracts/
  stubsMode: CLASSPATH
  generate-stubs: true

wx:
  app:
    appId: wx076635229dca85d8
    secret: 91f498a7584405ca6d308ca93e170f91

fromType:
  context: GUOBIAOSHI

costcontrol:
  submit:
    url:

invite:
  context: "{
                            \"sections\":[
                         {\"text\":\"尊敬的贵宾：\",\"style\":{\"align\":\"left\"}},
                         {\"text\":\"\",\"style\":{\"indent\":true}},

                {\"text\":\"国台奋进新征程，打造中国新名酒，国台酒诚邀您参与“举杯国台酒·共享大健康”品鉴会。\",\"style\":{\"indent\":true}},

                {\"text\":\"\",\"style\":{\"indent\":true}},
                {\"text\":\"\",\"style\":{\"indent\":true}},
                {\"text\":\"\",\"style\":{\"indent\":true}},
                {\"text\":\"凝神静气，心系于酒。\",\"style\":{\"indent\":true}},
                {\"text\":\"举杯齐眉，眼观其色。\",\"style\":{\"indent\":true}},
                {\"text\":\"勾头倾杯，鼻闻其香。\",\"style\":{\"indent\":true}},
                {\"text\":\"细品慢咽，口尝其味。\",\"style\":{\"indent\":true}},
                {\"text\":\"融色香味，得其风格。\",\"style\":{\"indent\":true}},
                {\"text\":\"\",\"style\":{\"indent\":true}},
                {\"text\":\"让我们一起感受国台美酒的品质，体验国台美酒的文化。\",\"style\":{\"align\":\"left\"}},
                {\"text\":\"\",\"style\":{\"indent\":true}},
                {\"text\":\"\",\"style\":{\"indent\":true}},
                {\"text\":\"贵州国台酒业集团股份有限公司\",\"style\":{\"align\":\"right\"}}
              ]
              }"

tour:
  context: "{
	\"sections\": [{
		\"text\": \"尊敬的贵宾：\",
		\"style\": {
			\"align\": \"left\"
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"国台奋进新征程，打造中国新名酒，国台酒诚邀您参与国台体验之旅活动。\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"让我们一起走进茅台镇，感受茅台镇独特水土，优质生态，探秘酱香传奇。\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"让我们一起走进国台酒，感受国台酒正宗酱香，智能酿造，享受文化之旅。\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"国台体验之旅即将启程，期待您的到来。\",
		\"style\": {
			\"align\": \"left\",
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"\",
		\"style\": {
			\"indent\": true
		}
	}, {
		\"text\": \"贵州国台酒业集团股份有限公司\",
		\"style\": {
			\"align\": \"right\"
		}
	}]
}"
