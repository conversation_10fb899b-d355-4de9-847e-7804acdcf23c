package com.intelliquor.cloud.shop.common.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 产品信息
 * <AUTHOR>
 * @date 2021.05.17
 */
@Data
public class ProductInfoModel implements Serializable {

    /**
     * id
     */
    private  String id;
    /**
     * 产品类型
     */
    private  String  productTypes;

    /**
     * 净含量 单位ml
     */
    private  Integer netContent;

    /**
     * 产品id
     */
    private  Integer productId;

    /**
     * 分类id
     */
    private  String  productCategoryId;

    /**
     * 分类编码
     */
    private  String productCategoryCode;

    /**
     * 分类名称
     */
    private String productCategoryName;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 箱瓶比例
     */
    private String bottleBoxRatio;

    /**
     * 库存类型名称
     */
    private String storeTypeName;

    /**
     * 净重
     */
    private String   netWeight;

    /**
     * 度数
     */
    private Integer proof;

    /**
     * 包装规格
     */
    private  String  productSpec;

    /**
     * 香型
     */
    private String aromaType;

    /**
     * 产品统一销售价
     */
    private Double suggestedRetailPrice;

    /**
     * 配比
     */
    private String mixtureRatio;


}
