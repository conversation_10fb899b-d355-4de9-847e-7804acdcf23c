package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.model.TerminalNoReward;
import com.intelliquor.cloud.shop.common.service.ITerminalNoRewardService;
import com.intelliquor.cloud.shop.common.dao.TerminalNoRewardMapper;
import org.springframework.stereotype.Service;

/**
* @description 针对表【t_terminal_no_reward(不发终端奖励的条件数据)】的数据库操作Service实现
* @createDate 2023-10-17 16:46:55
*/
@Service
public class TerminalNoRewardServiceImpl extends ServiceImpl<TerminalNoRewardMapper, TerminalNoReward>
    implements ITerminalNoRewardService {

}




