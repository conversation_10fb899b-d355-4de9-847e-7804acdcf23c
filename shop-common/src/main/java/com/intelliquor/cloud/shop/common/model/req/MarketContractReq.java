package com.intelliquor.cloud.shop.common.model.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: MAX
 * @CreateTime: 2024-04-22  10:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MarketContractReq extends PageInfoReq implements Serializable {
    private static final long serialVersionUID = 3839607907161706200L;

    /**
     * 合同编码列表
     */
    private List<String> contractCodeList;

    /**
     * 合同类型列表
     */
    private List<Integer> contractTypeList;

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 是否需要返回合同授权区域列表 0-不需要 1-需要
     */
    private Integer needAreaStatus = 0;
}
