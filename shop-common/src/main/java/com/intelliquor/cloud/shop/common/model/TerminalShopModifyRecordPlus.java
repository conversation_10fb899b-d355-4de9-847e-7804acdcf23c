package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 终端经销商信息变更记录表
 * @TableName t_terminal_shop_modify_record
 */
@TableName(value ="t_terminal_shop_modify_record")
@Data
public class TerminalShopModifyRecordPlus implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * t_member_shop | 采集终端表的主键id
     */
    private Integer memberShopId;

    /**
     * t_terminal_shop | 终端表的主键id
     */
    private Integer terminalShopId;

    /**
     * 变更类型 0.合同变更  1.经销商变更
     */
    private Integer modifyType;

    /**
     * 终端店铺名称
     */
    private String shopName;

    /**
     * 终端主编码
     */
    private String mainCode;

    /**
     * 终端副编码
     */
    private String deputyCode;

    /**
     * 新的终端副编码
     */
    private String newDeputyCode;

    /**
     * 状态 0- 审核不通过 1-审核中 2-审核通过
     */
    private Integer status;

    /**
     * 变更前经销商编码
     */
    private String oldDealerCode;

    /**
     * 变更前经销商名称
     */
    private String oldDealerName;

    /**
     * 变更前合同类型(0:国台主品合同 1:国台酱酒合同 2:常规渠道经销合同 3:国台酱酒经销合同 4:专卖店经销合同 5:数智体验中心经销合同 6:团购特约经销合同 7:电商平台经销合同)
     */
    private Integer oldContractType;

    /**
     * 变更前合同编码
     */
    private String oldContractCode;

    /**
     * 变更后经销商编码
     */
    private String newDealerCode;

    /**
     * 变更后经销商名称
     */
    private String newDealerName;

    /**
     * 变更后合同类型(0:国台主品合同 1:国台酱酒合同 2:常规渠道经销合同 3:国台酱酒经销合同 4:专卖店经销合同 5:数智体验中心经销合同 6:团购特约经销合同 7:电商平台经销合同)
     */
    private Integer newContractType;

    /**
     * 变更后合同编码
     */
    private String newContractCode;

    /**
     * 变更途径 0.终端管理后台 1.终端管理小程序
     */
    private Integer modifyWay;

    /**
     * 变更人
     */
    private String modifyUser;

    /**
     * 变更人手机号
     */
    private String modifyUserPhone;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否同步终端采集信息 0.未同步  1.已同步   2.同步失败
     */
    private Integer terminalSend;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
