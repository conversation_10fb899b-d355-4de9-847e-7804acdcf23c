package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.valid.RegisterGroupValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName("t_cloud_dealer_info")
public class DealerInfoCommonModel {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户微信openId
     */
    private String openId;

    /**
     * 客户类型 1-经销商 2-分销商
     */
    private Integer type;

    /**
     * 客户编码
     */
    private String dealerCode;

    /**
     * 客户名称
     */
    private String dealerName;

    /**
     * 经销商类型（客户类型为经销商时存在）
     */
    private Integer dealerType;

    /**
     * 经销商类型邀请码（客户类型为经销商时存在）
     */
    private String dealerInviteCode;

    /**
     * 终端店邀请码
     */
    private String shopInviteCode;

    /**
     * 上级客户 ID（当客户类型为分销商时存在）
     */
    private Integer superiorId;

    /**
     * 客户业务区域
     */
    private Integer dealerArea;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系电话
     */
    @NotBlank(message = "手机号必须指定", groups = {RegisterGroupValid.class})
    private String phone;

    /**
     * 地址
     */
    private String address;

    /**
     * 公司 ID
     */
    @NotNull(message = "商户id必须指定", groups = {RegisterGroupValid.class})
    private Integer companyId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 客户状态 0-停用 1-启用
     */
    private Integer status;

    /**
     * 删除状态 0-已删除 1-未删除
     */
    private Integer isDelete;

    /**
     * 微信名称
     */
    private String weChartName;

    /**
     * 头像图片路径
     */
    private String headImgUrl;

    /**
     * 经销商微信群图片路径
     */
    private String dealerWeChartGroupImgUrl;

    /**
     * 终端店微信群图片路径
     */
    private String shopWeChartGroupImgUrl;

    /**
     * 省
     */
    private String provinces;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    @ApiModelProperty(value = "街道")
    private String street;

    @ApiModelProperty(value = "国台经销商数据id")
    private String gtDealerId;

    @ApiModelProperty(value = "国台经销商数据json")
    private String gtDealerData;

    /**
     * 业务区域路径集合
     */
    private String dealerAreaList;

    /**
     * 账户余额
     */
    private BigDecimal accountBalance;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    @ApiModelProperty(value = "门店图片")
    private String storesImg;

    @ApiModelProperty(value = "合同图片")
    private String contractImg;

    @ApiModelProperty(value = "营业执照图片")
    private String licenseImg;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 分销商等级,经销商为0
     */
    private Integer level;

    /**
     * 账户积分
     * */
    private Integer score;

    /**
     * 消费者unionId
     * */
    private String unionId;

    /**
     * 账号类型(1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端)
     * */
    private Integer accountType;

    /**
     * 经销商的虚拟货款金额
     * */
    private String virtualAmount;

    @ApiModelProperty(value = "合伙人合同编码")
    private String contractCode;

    @ApiModelProperty(value = "合伙人合同类型 （国台主品合同 0 国台酱酒合同 1 常规渠道经销合同 2 国台酱酒金品合同 3 专卖店经销合同 4 数智体验中心经销合同 5 团购特约经销合同 6 电商平台经销合同 7 商超连锁经销合同 8 国台酱酒经销合同（电商） 9 专卖店专销产品合同 10 国台酱酒经销合同 11 葡萄酒合同 12 定制酒合同 13 封坛酒合同 14）")
    private Integer contractType;

    @ApiModelProperty(value = "合伙人合同名称")
    private String contractName;
}
