package com.intelliquor.cloud.shop.common.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.intelliquor.cloud.shop.common.dao.CallRequestLogDao;
import com.intelliquor.cloud.shop.common.enums.RequestStatusEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.CallRequestLogModel;
import com.intelliquor.cloud.shop.common.model.OrderRequestLogCommonModel;
import com.intelliquor.cloud.shop.common.model.RequestLog;
import com.intelliquor.cloud.shop.common.model.config.GtSynConfigCommonModel;
import com.intelliquor.cloud.shop.common.model.req.SuYuanScanOutSyncDTO;
import com.intelliquor.cloud.shop.common.model.resp.GtTokenCommonResp;
import com.intelliquor.cloud.shop.common.model.resp.SuyuanDeliveryInfo;
import com.intelliquor.cloud.shop.common.service.CallRequestLogService;
import com.intelliquor.cloud.shop.common.service.CallXWCommonService;
import com.intelliquor.cloud.shop.common.service.IRequestLogService;
import com.intelliquor.cloud.shop.common.service.OrderRequestLogCommonService;
import com.intelliquor.cloud.shop.common.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class CallXWServiceCommonImpl implements CallXWCommonService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${xuanwu.token.url:''}")
    private String getTokenUrl;

    @Value("${suyuan.queryDeliveryBoxNum.url:''}")
    private String queryDeliveryBoxNumUrl;

    /**
     * 向溯源同步分销商发货信息的地址
     * 目前只有shop-management模块配置了改属性，如果其他模块需要使用，请在对应的配置文件中进行配置
     */
    @Value("${xuanwu.sendScanOut.url:''}")
    private String sendScanOutUrl;

    @Value("${xuanwu.accountinfocode:''}")
    private String accountinfocode;

    @Value("${xuanwu.opentypecode:''}")
    private String opentypecode;

    @Value("${xuanwu.opentypesecret:''}")
    private String opentypesecret;

    @Value("${xuanwu.clienttypecode:''}")
    private String clienttypecode;

    @Value("${gtsyn.queryDeliveryInfoByStoreId:''}")
    private String queryDeliveryInfoByStoreIdUrl;

    @Autowired
    private GtSynConfigCommonModel gtSynConfigModel;
    @Autowired
    private IRequestLogService requestLogService;

    @Value("${gtsyn.checkBeforeShipUrl:''}")
    private String checkBeforeShipUrl;

    @Value("${banquet.bottle.recovery.url:''}")
    private String banquetBottleRecoveryUrl;

    @Value("${banquet.bottle.open.url:''}")
    private String banquetOpenBottleQueryUrl;

    private static final String APP_KEY = "zyapp";
    private static final String APP_KEY_SECRET = "guotai@yx123";
    private static final DateTimeFormatter TOKEN_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Resource
    private OrderRequestLogCommonService orderRequestLogCommonService;

    @Resource
    private CallRequestLogDao callRequestLogDao;

    @Resource
    private CallRequestLogService callRequestLogService;

    @Override
    public String getXwToken() {
        String token = "";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        ResponseEntity<String> responseEntity = null;
        JSONObject req = new JSONObject();
        req.put("accountinfocode", accountinfocode);
        req.put("opentypecode", opentypecode);
        req.put("opentypesecret", opentypesecret);
        req.put("clienttypecode", clienttypecode);
        HttpEntity<JSONObject> entity = new HttpEntity<>(req, headers);
        try {
            responseEntity = restTemplate.exchange(getTokenUrl, HttpMethod.POST, entity, String.class);
            log.info("获取中台的Token结果---{}---{}", getTokenUrl, JSON.toJSONString(responseEntity));
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                JSONObject resp = JSONObject.parseObject(body);
                if (resp != null && resp.getJSONObject("resp_data") != null) {
                    token = resp.getJSONObject("resp_data").getString("token");
                }
            } else {
                throw new BusinessException("获取中台的Token失败");
            }
        } catch (Exception e) {
            log.error("获取中台的Token失败", e);
            throw new BusinessException("获取中台的Token失败");
        }
        return token;
    }


    /**
     * 获取溯源token
     *
     * @param userPhone
     * @param agentId
     * @return
     */
    @Override
    public GtTokenCommonResp getSyToken(String userPhone, String agentId) {
        String accessTokenUrl = gtSynConfigModel.getAccessTokenUrl();
        try {
            Map<String, String> parameters = Maps.newHashMap();
            parameters.put("userPhone", userPhone);
            parameters.put("agentId", agentId);
            Map<String, String> header = this.getHeadParam();
            String result = HttpUtils.sendGet(accessTokenUrl, parameters, header);
            log.info("gettoken参数 url=={},param={},header={},result={}", accessTokenUrl, parameters, header, result);
            JSONObject resultJson = JSONObject.parseObject(result);
            JSONObject excData = resultJson.getJSONObject("excData");
            if (ObjectUtil.isEmpty(excData)) {
                if (ObjectUtil.isNotEmpty(resultJson.getString("description"))) {
                    log.error("调用国台接口获取token失败：" + resultJson.getString("description"));
                    throw new BusinessException(resultJson.getString("description"));
                } else {
                    log.error("调用国台接口获取token失败:excData为空。result=" + resultJson.toString());
                    throw new BusinessException("接口获取token失败");
                }
            } else {
                GtTokenCommonResp gtTokenResp = new GtTokenCommonResp();
                String excDataToken = excData.getString("token");
                String storeFlag = excData.getString("storeFlag");
                if (ObjectUtil.isEmpty(excDataToken)) {
                    log.error("调用国台接口获取token失败：token为空");
                    throw new BusinessException("token获取失败");
                }
                String excDataAppId = excData.getString("appId");
                if (ObjectUtil.isEmpty(excDataAppId)) {
                    log.error("调用国台接口获取token失败：appId为空");
                    throw new BusinessException("appId获取失败");
                }
                String excDataAgentId = excData.getString("agentId");
                if (ObjectUtil.isEmpty(excDataAgentId)) {
                    log.error("调用国台接口获取agentId失败：agentId为空");
                    throw new BusinessException("agentId获取失败");
                }
                gtTokenResp.setToken(excDataToken);
                gtTokenResp.setStoreFlag(storeFlag);
                return gtTokenResp;
            }
        } catch (BusinessException e) {
            log.error("调用国台接口获取token失败：{}", e.getMessage());
            String message = "该用户未绑定该经销商".equals(e.getMessage()) ? "您还未绑定溯源系统" : e.getMessage();
            throw new BusinessException(message);
        } catch (Exception e) {
            log.error("调用国台接口获取token失败：token为空", e);
            throw new BusinessException("token获取失败");
        }
    }

    /**
     * 从溯源获取信息
     *
     * @param url
     * @param method
     * @return
     */
    @Override
    public String getInfoFromSy(String url, HttpMethod method, Object param, CallRequestLogModel callRequestLogModel) {
        log.info("从溯源获取信息接口地址：{}，参数：{}", url, JSONObject.toJSONString(param));
        GtTokenCommonResp token = getSyToken("139999001001", "999001001");
        if (token == null || StringUtils.isBlank(token.getToken())) {
            throw new BusinessException("获取溯源接口Token失败");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("gt_token", token.getToken());
        JSONObject req = null;
        if(param != null){
            req = new JSONObject();
            req.put("params", param);
            callRequestLogModel.setRequestInfo(req.toJSONString());
        }
        // 添加调用溯源日志
        callRequestLogModel.setCreateTime(new Date());
        callRequestLogModel.setCallType(1);

        ResponseEntity<String> responseEntity = null;
        try {
            HttpEntity<JSONObject> entity = new HttpEntity<>(req, headers);
            callRequestLogModel.setRequestUrl(url);
            callRequestLogModel.setRequestStartTime(new Date());
            responseEntity = restTemplate.exchange(url, method, entity, String.class);
            callRequestLogModel.setRequestEndTime(new Date());
            log.info("获取溯源返回结果---{}", JSON.toJSONString(responseEntity));
            if(responseEntity == null){
                throw new BusinessException("从溯源获取数据，返回结果为空");
            }

            callRequestLogModel.setReponseHttpCode(responseEntity.getStatusCode().value() + "");
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                callRequestLogModel.setReponseInfo(body);
                //
                JSONObject resp = JSONObject.parseObject(body);
                if (resp.getInteger("excCode") != null){
                    callRequestLogModel.setReponseCode(resp.getInteger("excCode") + "");
                    if(resp.getInteger("excCode") != 200 && resp.getString("excMsg") != null) {
                        callRequestLogModel.setRemark(resp.getString("excMsg"));
                    }
                }
                return body;
            } else {
                throw new BusinessException("从溯源获取信息失败，HTTP状态码:"+responseEntity.getStatusCode());
            }
        }catch (BusinessException bus){
            log.error("从溯源获取信息失败",bus);
            callRequestLogModel.setRemark(bus.getMessage());
            throw new BusinessException(bus.getMessage());
        }catch (Exception e) {
            log.error("从溯源获取信息失败-网络问题",e);
            callRequestLogModel.setReponseHttpCode("208");
            callRequestLogModel.setRemark(e.getMessage());
            throw new BusinessException(e.getMessage());
        }finally {
            callRequestLogDao.insert(callRequestLogModel);
        }
    }

    @Override
    public String getPOSTInfoFromSy(String url, HttpMethod method, JSONObject param, CallRequestLogModel callRequestLogModel) {
        log.info("从溯源获取信息接口地址：{}，参数：{}", url, JSONObject.toJSONString(param));
        GtTokenCommonResp token = getSyToken("139999001001", "999001001");
        if (token == null || StringUtils.isBlank(token.getToken())) {
            throw new BusinessException("获取溯源接口Token失败");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("gt_token", token.getToken());
        ResponseEntity<String> responseEntity = null;

        // 添加调用溯源日志
        callRequestLogModel.setCreateTime(new Date());
        callRequestLogModel.setCallType(1);

        try {
            callRequestLogModel.setRequestUrl(url);
            if(param != null){
                callRequestLogModel.setRequestInfo(JSONObject.toJSONString(param));
            }
            HttpEntity<JSONObject> entity = new HttpEntity<>(param, headers);
            callRequestLogModel.setRequestStartTime(new Date());
            responseEntity = restTemplate.exchange(url, method, entity, String.class);
            callRequestLogModel.setRequestEndTime(new Date());
            log.info("获取溯源返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("从溯源获取数据，返回结果为空");
            }
            callRequestLogModel.setReponseHttpCode(responseEntity.getStatusCode().value() + "");
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                callRequestLogModel.setReponseInfo(body);
                JSONObject resp = JSONObject.parseObject(body);
                if (resp.getInteger("excCode") != null){
                    callRequestLogModel.setReponseCode(resp.getInteger("excCode") + "");
                    if(resp.getInteger("excCode") != 200 && resp.getString("excMsg") != null) {
                        callRequestLogModel.setRemark(resp.getString("excMsg"));
                    }
                }
                return body;
            } else {
                throw new BusinessException("从溯源获取信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("从溯源获取信息失败", bus);
            callRequestLogModel.setRemark(bus.getMessage());
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("从溯源获取信息失败-网络问题", e);
            //调用接口日志异常 自定义208
            callRequestLogModel.setReponseHttpCode("208");
            callRequestLogModel.setRemark(e.getMessage());
            throw new BusinessException("从溯源获取信息失败");
        }finally {
            callRequestLogDao.insert(callRequestLogModel);
        }
    }

    /**
     * 往溯源同步信息
     *
     * @param url
     * @param param 参数
     */
    @Override
    public String sendInfo2Sy(String url, HttpMethod method, Object param, CallRequestLogModel callRequestLogModel) {
        long ernterSystemTime = System.currentTimeMillis();
        log.info("向溯源同步信息接口地址：{}，参数：{}", url, JSONObject.toJSONString(param));
        GtTokenCommonResp token = getSyToken("139999001001", "999001001");
        if (token == null || StringUtils.isBlank(token.getToken())) {
            throw new BusinessException("获取溯源接口Token失败");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String tokenStr = token.getToken();
        headers.set("gt_token", tokenStr);

        // 添加调用溯源日志
        callRequestLogModel.setCreateTime(new Date());
        callRequestLogModel.setCallType(1);

        ResponseEntity<String> responseEntity = null;
        RequestLog requestLog = new RequestLog();
        //{"agentId":"","deliveryInfo":"222104679202","distributionId":"18492454","distributionMainCode":"18492454","latitude":"36.66645","longitude":"117.07641","mainCode":"111107242","mark":"按码收货","storeCode":"SH16856898787562799-2247","type":"1","userId":"***********"}
        requestLog.setReqName("扫码收货订单同步溯源");
        requestLog.setReqType(24);
        requestLog.setReqUrlPath(url);
        requestLog.setCreateDate(new Date());
        requestLog.setReqJson(JSONObject.toJSONString(param));
        requestLog.setResRtnJson(tokenStr);
        try {
            callRequestLogModel.setRequestUrl(url);
            if(param != null){
                callRequestLogModel.setRequestInfo(JSONObject.toJSONString(param));
            }
            HttpEntity<Object> entity = new HttpEntity<>(param, headers);
            callRequestLogModel.setRequestStartTime(new Date());
            responseEntity = restTemplate.exchange(url, method, entity, String.class);
            callRequestLogModel.setRequestEndTime(new Date());
            log.info("向溯源同步返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("向溯源同步信息失败，返回为空");
            }
            callRequestLogModel.setReponseHttpCode(responseEntity.getStatusCode().value() + "");
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                requestLog.setResJson(body);
                requestLog.setResCode("0");
                callRequestLogModel.setReponseInfo(body);
                JSONObject resp = JSONObject.parseObject(body);
                if (resp.getInteger("excCode") != null){
                    callRequestLogModel.setReponseCode(resp.getInteger("excCode") + "");
                    if(resp.getInteger("excCode") != 200 && resp.getString("excMsg") != null) {
                        callRequestLogModel.setRemark(resp.getString("excMsg"));
                    }
                }
                return body;
            } else {
                throw new BusinessException("向溯源同步信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("向溯源同步信息失败", bus);
            requestLog.setResCode("-1");
            requestLog.setResMsg(bus.getMessage());
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("向溯源同步信息失败-网络问题", e);
            requestLog.setResCode("-1");
            requestLog.setResMsg("向溯源同步信息失败");
            throw new BusinessException(e.getMessage());
        } finally {
            long outSystemTime = System.currentTimeMillis();
            String diffTime = DateUtils.longToHMS(outSystemTime - ernterSystemTime);
            requestLog.setReqTime(diffTime);
            requestLogService.insertLog(requestLog);
            callRequestLogDao.insert(callRequestLogModel);
        }
    }

    /**
     * 向溯源同步分销商发货信息
     * 目前只有shop-management模块配置了改属性，如果其他模块需要使用，请在对应的配置文件中进行配置xuanwu.sendScanOut.url参数
     *
     * @param suYuanScanOutSync
     * @return
     */
    @Override
    public String sendScanOut(SuYuanScanOutSyncDTO suYuanScanOutSync,OrderRequestLogCommonModel orderRequestLogModel, CallRequestLogModel callRequestLogModel) {
        suYuanScanOutSync.setTid(SnowFlake.getInstance().nextId());
        log.info("向溯源同步分销商发货信息接口地址：{}，参数：{}", sendScanOutUrl, JSONObject.toJSONString(suYuanScanOutSync));
        GtTokenCommonResp token = getSyToken("139999001001", "999001001");
        if (token == null || StringUtils.isBlank(token.getToken())) {
            orderRequestLogModel.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
            orderRequestLogModel.setRemark("获取溯源接口Token失败");
            throw new BusinessException("获取溯源接口Token失败");
        }

        // 添加调用溯源日志
        callRequestLogModel.setCreateTime(new Date());
        callRequestLogModel.setCallType(1);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("gt_token", token.getToken());
        ResponseEntity<String> responseEntity = null;
        try {
            callRequestLogModel.setRequestUrl(sendScanOutUrl);
            if(suYuanScanOutSync != null){
                callRequestLogModel.setRequestInfo(JSONObject.toJSONString(suYuanScanOutSync));
            }
            orderRequestLogModel.setRequestInfo(JSON.toJSONString(suYuanScanOutSync));
            HttpEntity<Object> entity = new HttpEntity<>(suYuanScanOutSync, headers);
            Date startTime = new Date();
            orderRequestLogModel.setRequestStartTime(startTime);
            callRequestLogModel.setRequestStartTime(startTime);
            responseEntity = restTemplate.exchange(sendScanOutUrl, HttpMethod.POST, entity, String.class);
            Date endTime = new Date();
            orderRequestLogModel.setRequestEndTime(endTime);
            orderRequestLogModel.setRequestTime((endTime.getTime() - startTime.getTime())/1000);
            callRequestLogModel.setRequestEndTime(endTime);
            log.info("向溯源同步返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                orderRequestLogModel.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
                orderRequestLogModel.setRemark("向溯源同步分销商发货信息失败，返回为空");
                throw new BusinessException("向溯源同步分销商发货信息失败，返回为空");
            }
            callRequestLogModel.setReponseHttpCode(responseEntity.getStatusCode().value() + "");
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                JSONObject resultBody = JSON.parseObject(body);
                orderRequestLogModel.setRequestStatus(RequestStatusEnum.REQUEST_SUCCESS.getCode());
                orderRequestLogModel.setReponseInfo(JSON.toJSONString(resultBody));
                callRequestLogModel.setReponseInfo(body);
                if (resultBody.getInteger("excCode") != null){
                    callRequestLogModel.setReponseCode(resultBody.getInteger("excCode") + "");
                    if(resultBody.getInteger("excCode") != 200 && resultBody.getString("excMsg") != null) {
                        callRequestLogModel.setRemark(resultBody.getString("excMsg"));
                    }
                }
                return body;
            } else {
                orderRequestLogModel.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
                orderRequestLogModel.setRemark("向溯源同步分销商发货信息失败，HTTP状态码:" + responseEntity.getStatusCode());
                throw new BusinessException("向溯源同步分销商发货信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("向溯源同步分销商发货信息", bus);
            orderRequestLogModel.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
            orderRequestLogModel.setRemark(bus.getMessage());
            orderRequestLogCommonService.save(orderRequestLogModel);
            callRequestLogModel.setRemark(bus.getMessage());
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("向溯源同步分销商发货信息-网络问题", e);
            orderRequestLogModel.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
            orderRequestLogModel.setRemark("向溯源同步信息失败");
            orderRequestLogCommonService.save(orderRequestLogModel);
            callRequestLogModel.setReponseHttpCode("208");
            callRequestLogModel.setRemark(e.getMessage());
            throw new BusinessException("向溯源同步信息失败");
        }finally {
            callRequestLogDao.insert(callRequestLogModel);
        }
    }

    /**
     * 往玄武同步信息
     *
     * @param url
     * @param param 参数
     */
    @Override
    public String sendInfo2Xw(String url, HttpMethod method, Object param) {
        log.info("向中台同步信息接口地址：{}，参数：{}", url, JSONObject.toJSONString(param));
        String token = getXwToken();
        if (StringUtils.isBlank(token)) {
            throw new BusinessException("获取中台接口Token失败");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Token", token);
        ResponseEntity<String> responseEntity = null;
        try {
            HttpEntity<Object> entity = new HttpEntity<>(param, headers);
            responseEntity = restTemplate.exchange(url, method, entity, String.class);
            log.info("向中台同步返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("向中台同步信息失败，返回为空");
            }
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                return body;
            } else {
                throw new BusinessException("向中台同步信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("向中台同步信息失败", bus);
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("向中台同步信息失败-网络问题", e);
            throw new BusinessException("向中台同步信息失败");
        }
    }

    /**
     * 功能描述: 获取头部信息
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @auther: tms
     * @date: 2021/01/19 16:08
     */
    private Map<String, String> getHeadParam() throws Exception {
        return SignUtilsCommon.assembleReqHeader(gtSynConfigModel.getAccessKey(), gtSynConfigModel.getAccessSecret());
    }

    /**
     * 根据出库单号获取所有码信息
     * https://zy-sk.feishu.cn/docx/GU2PdafuAoUffpxr64Aco4yJngg
     * https://gtsytest.tasly.com/enterprisewx/zy/facade/queryDeliveryInfoByStoreId/20230616154147983987362
     *
     * @param storeId
     * @return
     */
    public List<SuyuanDeliveryInfo> queryDeliveryInfoByStoreId(String storeId) {

        String queryUrl = queryDeliveryInfoByStoreIdUrl.replace("{storeId}", storeId);
        log.info("获取溯源出库单码信息---{}", queryUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject req = null;
        ResponseEntity<String> responseEntity = null;
        try {
            HttpEntity<JSONObject> entity = new HttpEntity<>(req, headers);
            responseEntity = restTemplate.exchange(queryUrl, HttpMethod.GET, entity, String.class);
            log.info("获取溯源出库单码信息返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("获取溯源出库单码信息，返回结果为空");
            }
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                JSONObject jsonObject = JSON.parseObject(responseEntity.getBody());
                int excCode = jsonObject.getIntValue("excCode");
                if (excCode == 200) {
                    JSONObject excData = jsonObject.getJSONObject("excData");
                    return JSON.parseArray(excData.getString("detaList"), SuyuanDeliveryInfo.class);
                } else {
                    throw new BusinessException("获取溯源出库单码信息，返回失败");
                }
            } else {
                throw new BusinessException("获取溯源出库单码信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("获取溯源出库单码信息失败", bus);
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("获取溯源出库单码信息失败-网络问题", e);
            throw new BusinessException("获取溯源出库单码信息失败");
        }
    }

    @Override
    public Boolean checkBeforeShip(String dealerCode, String mainCode, String qrCode, OrderRequestLogCommonModel requestLog) {
        log.info("分销商合伙人发货前扫码确认接口地址：{}，参数：dealerCode:{},mainCode:{},qrCode:{}", checkBeforeShipUrl, dealerCode, mainCode, qrCode);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject req = new JSONObject();
        ResponseEntity<String> responseEntity = null;
        req.put("agentId", dealerCode);
        req.put("mainCode", mainCode);
        req.put("deliveryInfo", qrCode);
        requestLog.setRequestInfo(JSON.toJSONString(req));
        try {
            HttpEntity<Object> entity = new HttpEntity<>(req, headers);
            Date startTime = new Date();
            requestLog.setRequestStartTime(startTime);
            responseEntity = restTemplate.exchange(checkBeforeShipUrl, HttpMethod.POST, entity, String.class);
            Date endTime = new Date();
            requestLog.setRequestEndTime(endTime);
            requestLog.setRequestTime((endTime.getTime() - startTime.getTime())/1000);
            log.info("分销商合伙人发货前扫码确认接口返回结果---{}", JSON.toJSONString(responseEntity));
            if (Objects.isNull(responseEntity)) {
                requestLog.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
                requestLog.setRemark("分销商合伙人发货前扫码确认失败，返回为空");
                throw new BusinessException("分销商合伙人发货前扫码确认失败，返回为空");
            }
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                JSONObject resultBody = JSON.parseObject(body);
                requestLog.setRequestStatus(RequestStatusEnum.REQUEST_SUCCESS.getCode());
                requestLog.setReponseInfo(JSON.toJSONString(resultBody));
                return resultBody.getIntValue("excCode") == 200;
            } else {
                requestLog.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
                requestLog.setRemark("分销商合伙人发货前扫码确认失败，HTTP状态码:" + responseEntity.getStatusCode());
                throw new BusinessException("分销商合伙人发货前扫码确认失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("分销商合伙人发货前扫码确认返回信息", bus);
            requestLog.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
            requestLog.setRemark(bus.getMessage());
            orderRequestLogCommonService.save(requestLog);
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("分销商合伙人发货前扫码确认-网络问题", e);
            requestLog.setRequestStatus(RequestStatusEnum.REQUEST_FAIL.getCode());
            requestLog.setRemark("分销商合伙人发货前扫码确认失败");
            orderRequestLogCommonService.save(requestLog);
            throw new BusinessException("分销商合伙人发货前扫码确认失败");
        }
    }

    @Override
    public String getReturnApplySy(String url, JSONObject param, CallRequestLogModel callRequestLogModel) {
        log.info("退货订单推送溯源接口地址：{}，参数：{}", url, JSONObject.toJSONString(param));
        GtTokenCommonResp token = getSyToken("139999001001", "999001001");
        if (token == null || StringUtils.isBlank(token.getToken())) {
            throw new BusinessException("获取溯源接口Token失败");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("gt_token", token.getToken());
        ResponseEntity<String> responseEntity = null;

        // 添加调用溯源日志
        callRequestLogModel.setCreateTime(new Date());
        callRequestLogModel.setCallType(1);

        try {
            callRequestLogModel.setRequestUrl(url);
            if(param != null){
                callRequestLogModel.setRequestInfo(JSONObject.toJSONString(param));
            }
            HttpEntity<JSONObject> entity = new HttpEntity<>(param, headers);
            callRequestLogModel.setRequestStartTime(new Date());

            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
            requestFactory.setConnectTimeout(1000 * 60 * 2);
            // 之前设置的是5000ms 20240626秦诗夫让改成20000ms   -->2024-08-28彭说先改成2分钟看看
            requestFactory.setReadTimeout(1000 * 60 * 2);
            RestTemplate resttemplate = new RestTemplate();
            resttemplate.setRequestFactory(requestFactory);
            log.info("开始调用退货订单推送溯源接口:{}", callRequestLogModel.getQrcode());
            responseEntity = resttemplate.exchange(url, HttpMethod.POST, entity, new ParameterizedTypeReference<String>() {});
            log.info("结束调用退货订单推送溯源接口:{}", callRequestLogModel.getQrcode());

            callRequestLogModel.setRequestEndTime(new Date());
            log.info("退货订单推送溯源返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("退货订单推送溯源，返回结果为空");
            }
            callRequestLogModel.setReponseHttpCode(responseEntity.getStatusCode().value() + "");
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                callRequestLogModel.setReponseInfo(body);
                JSONObject resp = JSONObject.parseObject(body);
                if (resp.getInteger("excCode") != null){
                    callRequestLogModel.setReponseCode(resp.getInteger("excCode") + "");
                    if(resp.getInteger("excCode") != 200 && resp.getString("excMsg") != null) {
                        callRequestLogModel.setRemark(resp.getString("excMsg"));
                    }
                }
                return body;
            } else {
                throw new BusinessException("从溯源获取信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.info("结束调用退货订单推送溯源接口【异常1】:{}", callRequestLogModel.getQrcode());
            log.error("从溯源获取信息失败", bus);
            callRequestLogModel.setRemark(bus.getMessage());
            callRequestLogModel.setRequestEndTime(new Date());
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.info("结束调用退货订单推送溯源接口【异常2】:{}", callRequestLogModel.getQrcode());
            log.error("从溯源获取信息失败-网络问题", e);
            //调用接口日志异常 自定义208
            callRequestLogModel.setReponseHttpCode("208");
            callRequestLogModel.setRemark(e.getMessage());
            callRequestLogModel.setRequestEndTime(new Date());
            throw new BusinessException("从溯源获取信息失败");
        }finally {
            callRequestLogService.insertLog(callRequestLogModel);
           //  callRequestLogDao.insert(callRequestLogModel);
        }
    }

    @Override
    public String zYRevokeDeliveryProduct(String url, JSONObject param, CallRequestLogModel callRequestLogModel) {
        log.info("智盈扫码撤销接口地址：{}，参数：{}", url, JSONObject.toJSONString(param));
        GtTokenCommonResp token = getSyToken("139999001001", "999001001");
        if (token == null || StringUtils.isBlank(token.getToken())) {
            throw new BusinessException("获取溯源接口Token失败");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("gt_token", token.getToken());
        ResponseEntity<String> responseEntity = null;

        // 添加调用溯源日志
        callRequestLogModel.setCreateTime(new Date());
        callRequestLogModel.setCallType(1);

        try {
            callRequestLogModel.setRequestUrl(url);
            if(param != null){
                callRequestLogModel.setRequestInfo(JSONObject.toJSONString(param));
            }
            HttpEntity<JSONObject> entity = new HttpEntity<>(param, headers);
            callRequestLogModel.setRequestStartTime(new Date());
            responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            callRequestLogModel.setRequestEndTime(new Date());
            log.info("智盈扫码撤销接口返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("智盈扫码撤销接口，返回结果为空");
            }
            callRequestLogModel.setReponseHttpCode(responseEntity.getStatusCode().value() + "");
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                callRequestLogModel.setReponseInfo(body);
                JSONObject resp = JSONObject.parseObject(body);
                if (resp.getInteger("excCode") != null){
                    callRequestLogModel.setReponseCode(resp.getInteger("excCode") + "");
                    if(resp.getInteger("excCode") != 200 && resp.getString("excMsg") != null) {
                        callRequestLogModel.setRemark(resp.getString("excMsg"));
                    }
                }
                return body;
            } else {
                throw new BusinessException("从溯源获取信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("从溯源获取信息失败", bus);
            callRequestLogModel.setRemark(bus.getMessage());
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("从溯源获取信息失败", e.getMessage());
            //调用接口日志异常 自定义208
            callRequestLogModel.setReponseHttpCode("208");
            callRequestLogModel.setRemark(e.getMessage());
            throw new BusinessException("从溯源获取信息失败");
        }finally {
            callRequestLogDao.insert(callRequestLogModel);
        }
    }

    /**
     *  码是否被宴席使用过
     */
    public Boolean isUsedByBanquet(String code) {
        JSONObject param = new JSONObject();
        param.put("code", code);
        param.put("type", 1);
        String token  = "";
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String timestamp = simpleDateFormat.format(new Date());
            String content = "zyapp|"+ timestamp +"|codeIsUsedByBanquet";
            // 加密数据, 返回密文
            byte[] cipherBytes = AES.encrypt(content.getBytes(), "guotai@yx123".getBytes());
            token  = Base64Encoder.encode(cipherBytes);
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException("获取TOKEN失败");
        }
        String url = banquetBottleRecoveryUrl + "?code="+code+"&type=1";
        log.info("向溯源查询码是否被宴席使用接口地址：{}，token:{}", url, token);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("app", "zyapp");
        headers.set("X-THIRD-TOKEN", token);
        ResponseEntity<String> responseEntity = null;
        try {
            HttpEntity<Object> entity = new HttpEntity<>(headers);
            responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            log.info("向溯源查询码是否被宴席使用返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("向溯源查询码是否被宴席使用信息失败，返回为空");
            }
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                log.info("返回body参数:{}",body);
                JSONObject bodyJson = JSON.parseObject(body);
                Boolean resultBody = bodyJson.getBoolean("success");
                if(resultBody){
                    return bodyJson.getBoolean("result");
                }else{
                    throw new BusinessException(bodyJson.getString("message"));
                }
            } else {
                throw new BusinessException("向溯源查询码是否被宴席使用信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("向溯源查询码是否被宴席使用信息", bus);
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("向溯源查询码是否被宴席使用信息-网络问题", e);
            throw new BusinessException("向溯源查询码是否被宴席使用信息失败");
        }
    }

    @Override
    public Boolean isOpenedByBanquet(String code, Date openTime) {
        try {
            // 转换时间格式
            String openTimeStr = DateUtil.format(openTime, DatePattern.NORM_DATETIME_FORMATTER);
            // 构建请求头
            HttpHeaders headers = buildHeaders(banquetOpenBottleQueryUrl);
            // 构建请求URL，由于time可能包含特殊字符，所以需要进行URL编码
            String url = String.format(banquetOpenBottleQueryUrl + "?code=%s&time=%s", code, openTimeStr);
            log.info("查询码是否被宴席使用接口地址：{}", url);
            // 执行请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    new HttpEntity<>(headers),
                    String.class);
            log.info("查询码是否被宴席使用返回结果---{}", JSON.toJSONString(responseEntity));
            // 验证响应
            if (Objects.isNull(responseEntity.getBody())) {
                throw new BusinessException("查询码是否被宴席使用信息失败，返回为空");
            }
            if (!Objects.equals(responseEntity.getStatusCode(), HttpStatus.OK)) {
                throw new BusinessException(String.format(
                        "查询码是否被宴席使用信息失败，HTTP状态码: %s",
                        responseEntity.getStatusCode()));
            }
            // 解析响应
            JSONObject bodyJson = JSON.parseObject(responseEntity.getBody());
            if (!bodyJson.getBoolean("success")) {
                throw new BusinessException(bodyJson.getString("message"));
            }
            if (!bodyJson.containsKey("result")) {
                throw new BusinessException("接口返回格式异常，缺少 result 字段");
            }
            JSONObject result = bodyJson.getJSONObject("result");
            if (!result.containsKey("isBanquetUsed")) {
                throw new BusinessException("接口返回格式异常，缺少 result.isBanquetUsed 字段");
            }
            return result.getBoolean("isBanquetUsed");
        } catch (BusinessException e) {
            log.error("业务异常 - 查询码是否被宴席使用信息失败", e);
            throw e;
        } catch (Exception e) {
            log.error("系统异常 - 查询码是否被宴席使用信息失败", e);
            throw new BusinessException("系统异常，请稍后重试");
        }
    }

    /**
     * 生成认证token
     * @param path API路径
     * @return 加密后的token
     * @throws BusinessException 加密失败时抛出
     */
    private String generateToken(String path) throws BusinessException {
        try {
            String content = String.format("%s|%s|%s",
                    APP_KEY,
                    LocalDateTime.now().format(TOKEN_TIME_FORMATTER),
                    path);
            log.info("Token原文：{}", content);
            byte[] cipherBytes = AES.encrypt(content.getBytes(), APP_KEY_SECRET.getBytes());
            return Base64Encoder.encode(cipherBytes);
        } catch (Exception e) {
            log.error("Token生成失败", e);
            throw new BusinessException("Token生成失败", e);
        }
    }

    /**
     * 获取URL最后路径段
     * @param url 完整URL
     * @return 最后路径段
     * @throws BusinessException URL解析失败时抛出
     */
    private String getLastPathSegment(String url) throws BusinessException {
        try {
            URI uri = new URI(url);
            String path = uri.getPath();
            String[] segments = path.split("/");
            return segments.length > 0 ? segments[segments.length - 1] : null;
        } catch (URISyntaxException e) {
            log.error("URL解析失败", e);
            throw new BusinessException("URL解析失败", e);
        }
    }

    /**
     * 构建HTTP请求头
     * @param url API地址
     * @return 包含认证信息的HttpHeaders
     * @throws BusinessException 认证信息生成失败时抛出
     */
    private HttpHeaders buildHeaders(String url) throws BusinessException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        try {
            String path = getLastPathSegment(url);
            headers.set("X-THIRD-TOKEN", generateToken(path));
            headers.set("app", APP_KEY);
            return headers;
        } catch (BusinessException e) {
            log.error("构建请求头失败", e);
            throw e;
        }
    }

    /**
     * 查询溯源该订单下盒码数量
     *
     * @param orderCode
     * @return
     */
    @Override
    public Long queryDeliveryBoxNum(String orderCode) {
        String url = queryDeliveryBoxNumUrl+"/"+orderCode;
        log.info("查询溯源该订单下盒码数量地址：{}，参数：{}", url, orderCode);
        GtTokenCommonResp token = getSyToken("139999001001", "999001001");
        if (token == null || StringUtils.isBlank(token.getToken())) {
            throw new BusinessException("获取溯源接口Token失败");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("gt_token", token.getToken());
        ResponseEntity<String> responseEntity = null;

        CallRequestLogModel callRequestLogModel = new CallRequestLogModel();
        // 添加调用溯源日志
        callRequestLogModel.setCreateTime(new Date());
        callRequestLogModel.setCallType(1);

        try {
            callRequestLogModel.setRequestUrl(queryDeliveryBoxNumUrl);
            if(orderCode != null){
                callRequestLogModel.setRequestInfo(JSONObject.toJSONString(orderCode));
            }
            HttpEntity<JSONObject> entity = new HttpEntity<>(headers);
            callRequestLogModel.setRequestStartTime(new Date());
            responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            callRequestLogModel.setRequestEndTime(new Date());
            log.info("查询溯源该订单下盒码数量接口返回结果---{}", JSON.toJSONString(responseEntity));
            if (responseEntity == null) {
                throw new BusinessException("查询溯源该订单下盒码数量接口，返回结果为空");
            }
            callRequestLogModel.setReponseHttpCode(responseEntity.getStatusCode().value() + "");
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                callRequestLogModel.setReponseInfo(body);
                JSONObject resp = JSONObject.parseObject(body);
                if (resp.getInteger("excCode") != null){
                    callRequestLogModel.setReponseCode(resp.getInteger("excCode") + "");
                    if(resp.getInteger("excCode") != 200 && resp.getString("excMsg") != null) {
                        callRequestLogModel.setRemark(resp.getString("excMsg"));
                    }
                }
                return resp.getLong("excData");
            } else {
                throw new BusinessException("从溯源获取信息失败，HTTP状态码:" + responseEntity.getStatusCode());
            }
        } catch (BusinessException bus) {
            log.error("从溯源获取信息失败", bus);
            callRequestLogModel.setRemark(bus.getMessage());
            throw new BusinessException(bus.getMessage());
        } catch (Exception e) {
            log.error("从溯源获取信息失败", e.getMessage());
            //调用接口日志异常 自定义208
            callRequestLogModel.setReponseHttpCode("208");
            callRequestLogModel.setRemark(e.getMessage());
            throw new BusinessException("从溯源获取信息失败");
        }finally {
            callRequestLogDao.insert(callRequestLogModel);
        }
    }
}
