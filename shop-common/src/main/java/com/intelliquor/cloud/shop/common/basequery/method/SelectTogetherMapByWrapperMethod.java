package com.intelliquor.cloud.shop.common.basequery.method;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.util.Map;

public class SelectTogetherMapByWrapperMethod extends AbstractMethod
{
    public SelectTogetherMapByWrapperMethod() {
        super(SqlMethod.SELECT_MAPS.getMethod());
    }

    @Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass,Class<?> modelClass, TableInfo tableInfo)
	{
		SqlMethod sqlMethod = SqlMethod.SELECT_MAPS;
		String sql = String.format(sqlMethod.getSql(), sqlFirst(), sqlSelectColumns(tableInfo, true), tableInfo.getTableName(),
	            sqlWhereEntityWrapper(true, tableInfo), this.sqlOrderBy(tableInfo), sqlComment());
	    SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
		return this.addSelectMappedStatementForOther(mapperClass,"selectTogetherMapByWrapper", sqlSource, Map.class);
	}

}
