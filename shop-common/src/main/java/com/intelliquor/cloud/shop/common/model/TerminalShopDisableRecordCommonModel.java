package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 终端启用禁用记录审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
@Setter
@TableName("t_terminal_shop_disable_record")
public class TerminalShopDisableRecordCommonModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 终端id
     */
    private Long terminalShopId;

    /**
     * 联盟id
     */
    private Long memberShopId;

    /**
     * 更新状态 0启用 1禁用
     */
    private Integer terminalStatus;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 创建人手机号
     */
    private String createUserPhone;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 审核人id
     */
    private Long checkUserId;

    /**
     * 审核人手机号
     */
    private String checkUserPhone;

    /**
     * 审核人名称
     */
    private String checkUserName;

    /**
     * 0未审核 1通过 2不通过
     */
    private Integer checkStatus;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;


}
