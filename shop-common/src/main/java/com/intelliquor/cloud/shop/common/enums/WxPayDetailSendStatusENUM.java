package com.intelliquor.cloud.shop.common.enums;

import lombok.Getter;

@Getter
public enum WxPayDetailSendStatusENUM {
    PAY_DETAIL_WAIT_SEND(0, "发送中"),
    PAY_DETAIL_SUCCESS_SEND(1, "发放成功"),
    PAY_DETAIL_FAIL_SEND(2, "发放失败");

    private Integer code;
    private String msg;

    WxPayDetailSendStatusENUM(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getName(Integer code){
        for (WxPayDetailSendStatusENUM contract : WxPayDetailSendStatusENUM.values()) {
            if (contract.getCode().equals(code)){
                return contract.getMsg();
            }
        }
        return "";
    }
}
