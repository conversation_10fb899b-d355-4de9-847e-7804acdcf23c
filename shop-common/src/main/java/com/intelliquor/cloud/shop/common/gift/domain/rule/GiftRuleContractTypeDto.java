package com.intelliquor.cloud.shop.common.gift.domain.rule;

import com.intelliquor.cloud.shop.common.gift.annotation.RuleField;
import java.util.List;
import lombok.Data;

@Data
public class GiftRuleContractTypeDto {


    private String id;

    /**
     * 合同类型
     */
    private Integer contractType;

    /**
     * 合同类型名称
     */
    private String contractTypeName;

    // TODO 改成terminalLevel
    @RuleField
    private List<GiftRuleContractTypeLevelDto> contractTypeLevelList;


}
