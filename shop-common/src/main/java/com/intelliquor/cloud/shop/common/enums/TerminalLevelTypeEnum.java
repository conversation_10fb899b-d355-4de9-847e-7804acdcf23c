package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

// 审批状态 0待处理 1转为正常 2确认异常
@Getter
@AllArgsConstructor
public enum TerminalLevelTypeEnum {
    /**
     * 非超级终端
     */
    NORMAL(0, "非超级终端"),

    /**
     * 超级终端
     */
    SUPER(1, "超级终端");

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (TerminalLevelTypeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }
}
