package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnum {
    // 事件类型 1-积分转实物 2-积分合并 TODO
    POINTS_TO_PHYSICAL(1, "积分转实物"),

    POINTS_TO_GOODS(2, "兑付实物奖励"),
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (EventTypeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }
}
