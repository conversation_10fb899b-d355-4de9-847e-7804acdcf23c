package com.intelliquor.cloud.shop.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

/**
 * 断言
 * <AUTHOR>
 */
public class ZyAssert extends Assert {
    public static void isFalse(boolean expression, String message) {
        org.springframework.util.Assert.isTrue(!expression,message);
    }

    public static void isBlank(CharSequence expression, String message) {
        if (StringUtils.isNotBlank(expression)) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void notBlank(CharSequence expression, String message) {
        if (StringUtils.isBlank(expression)) {
            throw new IllegalArgumentException(message);
        }
    }
}
