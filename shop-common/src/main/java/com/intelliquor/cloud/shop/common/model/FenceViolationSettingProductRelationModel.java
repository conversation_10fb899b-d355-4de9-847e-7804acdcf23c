package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 稽核规则配置关联产品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23
 */
@Getter
@Setter
@TableName("t_fence_violation_setting_product_relation")
public class FenceViolationSettingProductRelationModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 违约规则id
     */
    private Integer fenceSettingId;

    /**
     * 产品编码
     */
    private String goodsCode;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 是否删除（1已删除0未删除）
     */
    private Boolean delFlag;

    /**
     * 备注
     */
    private String remark;


}
