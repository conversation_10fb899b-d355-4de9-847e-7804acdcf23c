package com.intelliquor.cloud.shop.common.service;


import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.zt.ScoreOrderOpproveReq;

import java.util.List;

/**
 * 推送中台的服务接口类【最好统一写在这里】
 */
public interface IZtInterfaceService {
    /**
     * 积分订单数据接收接口
     */
    JSONObject sendScoreOrderOpprove(ShopModel shopInfo, CloudDealerInfoModel dealerInfoModel, ShopDealerOrderPlus order, List<ShopDealerOrderDetailPlus> detailList, CloudDealerInfoModel sendDealerInfoModel);

    /**
     * 积分订单审核状态推送
     */
    JSONObject receiveScoreOrderOpprove(ScoreOrderOpproveReq req);

    /**
     * 积分订单对账接口
     */
    JSONObject orderReconciliation(String ordercode);

    /**
     * 获取中台合同类型列表
     *
     * @return List<ContractTypeModel>
     */
    List<ContractTypeModel> getContractTypeList();

    /**
     *  推送经销商奖励
     */
    void sysZtDealerReword(ActivityRewardRecordModel rewardRecord, ZtBookRewardRecordModel ztBookRewardRecordModel, String uuidThread);

    /**
     *  推送非经销商奖励
     */
    void sysZtOtherReword(ActivityRewardRecordModel rewardRecord, ZtBookRewardRecordModel ztBookRewardRecordModel, String uuidThread);
    /**
     *  推送实物奖励
     */
    void sysZtGoodsReword(ActivityRewardRecordModel rewardRecord, ZtBookRewardRecordModel ztBookRewardRecordModel);
    /**
     *  批量奖扣
     */
    void sysZtBatchReward(BatchRewardDataModel batchRewardDataModel, Boolean isNew, ZtBookRewardRecordModel ztBookRewardRecordModel);

    /**
     *  批量奖扣
     */
    void sysZtDealerBatchReward(BatchRewardDataModel batchRewardDataModel, Boolean isNew, ZtBookRewardRecordModel ztBookRewardRecordModel);

    /**
     *  经销商实物奖励推送
     */
    void sysZtDealerGoodsReword(ActivityRewardRecordModel rewardRecord, ZtBookRewardRecordModel ztBookRewardRecordModel);

    Boolean isBottleCapRecycling(String code);

    void sysZtDealerPunishScoreReword(ActivityRewardRecordModel activityRewardRecordModel, ZtBookRewardRecordModel ztBookRewardRecordModel);

    /**
     * 高端酒经销商惩罚奖励中台推送(SZYXPT-921)
     * @param recordModel 惩罚奖励记录
     * @param ztBookRewardRecordModel 中台奖励记录
     */
    void sysZtHighEndLiquorDealerPunishScoreReword(ActivityRewardRecordModel recordModel, ZtBookRewardRecordModel ztBookRewardRecordModel);



     JSONObject syncOrderReceivedInfo(ActivityRewardOrderModel activityRewardOrderModel);

}
