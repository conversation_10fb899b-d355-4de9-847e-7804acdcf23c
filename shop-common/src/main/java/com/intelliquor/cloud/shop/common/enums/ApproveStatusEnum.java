package com.intelliquor.cloud.shop.common.enums;

import lombok.Getter;

// 审批状态 0待处理 1转为正常 2确认异常
@Getter
public enum ApproveStatusEnum {
    WAIT_HANDLE(0, "待处理"),
    NORMAL_TYPE(1, "转为正常"),
    CONFIRM_ANOMALIES(2, "确认异常");

    private Integer code;
    private String msg;

    ApproveStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getName(Integer code){
        for (ApproveStatusEnum contract : ApproveStatusEnum.values()) {
            if (contract.getCode().equals(code)){
                return contract.getMsg();
            }
        }
        return "";
    }
}
