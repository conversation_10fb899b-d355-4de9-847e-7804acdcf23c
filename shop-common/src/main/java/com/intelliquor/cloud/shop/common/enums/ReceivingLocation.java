package com.intelliquor.cloud.shop.common.enums;

import lombok.Getter;

@Getter
public enum ReceivingLocation {
    STORE(1, "门店"),
    storeroom(2, "库房"),
    banquet(3, "宴席现场");

    private Integer code;
    private String msg;

    ReceivingLocation(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getName(Integer code){
        for (ReceivingLocation contract : ReceivingLocation.values()) {
            if (contract.getCode().equals(code)){
                return contract.getMsg();
            }
        }
        return "";
    }
}
