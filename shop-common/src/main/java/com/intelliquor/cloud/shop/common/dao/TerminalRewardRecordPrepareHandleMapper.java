package com.intelliquor.cloud.shop.common.dao;

import com.intelliquor.cloud.shop.common.model.TerminalRewardRecordPrepareHandle;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【t_terminal_reward_record_prepare_handle(联盟终端收支明细表(预备终端)【处理数据使用，不要删除】)】的数据库操作Mapper
* @createDate 2023-09-06 14:01:53
* @Entity com.intelliquor.cloud.shop.common.model.TerminalRewardRecordPrepareHandle
*/
@Mapper
public interface TerminalRewardRecordPrepareHandleMapper extends BaseMapper<TerminalRewardRecordPrepareHandle> {

}




