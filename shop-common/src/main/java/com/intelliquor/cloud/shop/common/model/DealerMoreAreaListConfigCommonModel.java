package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.utils.ContractTypeConstant;
import com.intelliquor.cloud.shop.common.valid.DealerMoreAreaListConfigUpdate;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 业务区域多分销商配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-07
 */
@Getter
@Setter
@TableName("dealer_more_area_list_config")
public class DealerMoreAreaListConfigCommonModel implements Serializable {

    private static final long serialVersionUID = 1965401631860808222L;

    /**
     * 主键
     */
    @NotNull(message = "主键id不能为空", groups = {DealerMoreAreaListConfigUpdate.class})
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 合同类型 0：国台主品（国标）合同； 1：国台酱酒合同； 2：常规渠道经销合同； 3：国台酱酒金品合同； 4：专卖店经销合同； 5：数智体验中心经销合同； 6：团购特约经销合同； 7：电商平台经销合同； 8：商超连锁经销合同； 9：国台酱酒经销合同（电商）； 10：专卖店专销产品合同； 11：国台酱酒经销合同； 12：葡萄酒合同； 13：定制酒合同； 14：封坛酒合同
     */
    private Integer contractType;

    /**
     * 配置状态 0启动 1停用
     */
    private Integer configStatus;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 修改人
     */
    private Long updateUser;

    /**
     * 合同名称
     */
    @TableField(exist = false)
    private String contractName;

    public String getContractName() {
        if (Objects.nonNull(this.contractType)) {
            return ContractTypeConstant.CONTRACT_TYPE_MAP.get(this.contractType);
        } else {
            return "";
        }

    }


}
