package com.intelliquor.cloud.shop.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.FenceDealerAuth;
import com.intelliquor.cloud.shop.common.model.FenceDealerAuthArea;
import com.intelliquor.cloud.shop.common.model.FenceDealerBlankMarketModel;
import com.intelliquor.cloud.shop.common.model.req.DealerAuthAreaReq;
import com.intelliquor.cloud.shop.common.model.req.JudgeAreaReq;
import com.intelliquor.cloud.shop.common.model.resp.DealerAuthAreaResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description 针对表【t_fence_dealer_auth_area(经销商合同授权区域)】的数据库操作Mapper
 * @createDate 2024-02-23 15:03:55
 * @Entity com.intelliquor.cloud.shop.common.model.FenceDealerAuthArea
 */
public interface FenceDealerAuthAreaMapper extends BaseMapper<FenceDealerAuthArea> {

    List<DealerAuthAreaResp> selectPageList(DealerAuthAreaReq req);

    List<FenceDealerAuthArea> getAreaListByDealerId(Integer violateDealerId);

    List<FenceDealerAuthArea> getAreaListByContractCode(String contractCode);

    /**
     * 获取符合区域的经销商授权配置
     *
     * @param location
     * @return
     */
    List<FenceDealerAuth> getDealerByLocation(@Param("location") String location, @Param("dealerId") Long dealerId);


    /**
     * 获取符合区域的经销商授权配置(只查省)
     *
     * @param location
     * @return
     */
    List<FenceDealerAuth> getDealerByLocationAndProvince(@Param("location") String location, @Param("dealerId") Long dealerId);

    /**
     * 获取符合区域的经销商授权配置(只查省和市)
     *
     * @param location
     * @return
     */
    List<FenceDealerAuth> getDealerByLocationAndProvinceAndCity(@Param("location") String location, @Param("dealerId") Long dealerId);

    List<FenceDealerAuthArea> getJudgeArea(@Param("ro")JudgeAreaReq areaReq);

    /**
     * 获取符合区域的经销商授权配置 查询省市区
     *
     * @param location
     * @return
     */
    List<FenceDealerAuth> getDealerByLocationAndProvinceAndCityAndDistrict(@Param("location") String location, @Param("dealerId") Long dealerId, @Param("fenceContractType") Integer fenceContractType);

    /**
     * 查询被侵权经销商 查询省市且 区为空的
     * @param location
     * @param dealerId
     * @return
     */
    List<FenceDealerAuth> getDealerByLocationAndProvinceAndCityAndDistrictIsEmpty(@Param("location") String location, @Param("dealerId") Long dealerId, @Param("fenceContractType") Integer fenceContractType);


    /**
     * 查询被侵权经销商 查询省且 市区都为空的
     * @param location
     * @param dealerId
     * @return
     */
    List<FenceDealerAuth> getDealerByLocationAndProvinceAndCityIsEmpty(@Param("location") String location, @Param("dealerId") Long dealerId, @Param("fenceContractType") Integer fenceContractType);

    /**
     * 查询高端酒被侵权经销商 查询省市区都不为空的
     * @param location
     * @param dealerId
     * @return
     */
    List<FenceDealerAuth> getDealerByProvinceAndCityAndDistrict(@Param("location") String location, @Param("dealerId") Long dealerId);

    /**
     * 查询高端酒被侵权经销商 查询省市都不为空的
     * @param location
     * @param dealerId
     * @return
     */
    List<FenceDealerAuth> getDealerByProvinceAndCityAndDistrictIsEmpty(@Param("location") String location, @Param("dealerId") Long dealerId);

    /**
     * 查询高端酒被侵权经销商 查询省都不为空的
     * @param location
     * @param dealerId
     * @return
     */
    List<FenceDealerAuth> getDealerByProvinceAndCityIsEmpty(@Param("location") String location, @Param("dealerId") Long dealerId);

    Integer selectCountByParam(@Param("ro")FenceDealerBlankMarketModel model);
}




