package com.intelliquor.cloud.shop.common.enums;

import lombok.Getter;

@Getter
public enum EventType {
    TERMINAL_SALE_REWARD(10, "终端动销奖励"),
    MEMBER_SAlE_REWARD(11, "会员动销奖励"),
    DISTRIBUTOR_SALE_REWARD(12, "分销商动销奖励"),
    DEALER_SALE_REWARD(13, "经销商动销奖励"),
    TERMINAL_OPEN_BOTTLE_REWARD(20, "终端开瓶奖励"),
    MEMBER_OPEN_BOTTLE_REWARD(21, "会员开瓶奖励"),
    DISTRIBUTOR_OPEN_BOTTLE_REWARD(22, "分销商开瓶奖励"),
    DEALER_OPEN_BOTTLE_REWARD(23, "经销商开瓶奖励"),
    TERMINAL_VIOLATE_OPEN_BOTTLE_PUNISH(201, "终端异地违约动销处罚"),
    TERMINAL_VIOLATE_OPEN_BOTTLE_REFUND(202, "终端异地违约动销退还"),
    MEMBER_VIOLATE_OPEN_BOTTLE_PUNISH(211, "会员异地违约动销处罚"),
    MEMBER_VIOLATE_OPEN_BOTTLE_REFUND(212, "会员异地违约动销退还"),
    DISTRIBUTOR_VIOLATE_OPEN_BOTTLE_PUNISH(221, "分销商异地违约动销处罚"),
    DISTRIBUTOR_VIOLATE_OPEN_BOTTLE_REFUND(222, "分销商异地违约动销退还"),
    DEALER_VIOLATE_OPEN_BOTTLE_PUNISH(231, "经销商异地违约开瓶处罚"),
    DEALER_VIOLATE_OPEN_BOTTLE_SUPPLY(232, "被侵权经销商异地违约开瓶补偿"),
    DEALER_VIOLATE_OPEN_BOTTLE_REFUND(233, "经销商异地违约开瓶退还"),
    DEALER_VIOLATE_OPEN_BOTTLE_DEDUCT(234, "被侵权经销商异地违约开瓶扣除"),
    DEALER_VIOLATE_SALE_PUNISH(235, "经销商异地违约动销处罚"),
    DEALER_VIOLATE_SALE_REFUND(236, "经销商异地违约动销退还"),
    SELF_PUNISH_INTEGRAL_REWARD(241, "监察自主控盘奖励处罚"),
    SELF_PUNISH_SALES_SUPPORT_REWARD(242, "监察自主销售支持处罚"),
    TERMINAL_FENCE_VIOLATE_OPEN_BOTTLE_REWARD(250, "异地违约终端开瓶奖励"),
    MEMBER_FENCE_VIOLATE_OPEN_BOTTLE_REWARD(251, "异地违约会员开瓶奖励"),
    DISTRIBUTOR_FENCE_VIOLATE_OPEN_BOTTLE_REWARD(252, "异地违约分销商开瓶奖励"),
    DEALER_FENCE_VIOLATE_OPEN_BOTTLE_REWARD(253, "异地违约经销商开瓶奖励"),
    // 终端宴席开瓶奖励扣除
    TERMINAL_BANQUET_OPEN_BOTTLE_REWARD_DEDUCT(261, "终端宴席开瓶奖励扣除"),
    // 终端开瓶奖励运维补发
    TERMINAL_OPEN_BOTTLE_REWARD_OPERATIONAL_REISSUE(270, "终端开瓶奖励补发"),
    // 会员开瓶奖励运维补发
    MEMBER_OPEN_BOTTLE_REWARD_OPERATIONAL_REISSUE(271, "会员开瓶奖励补发"),
    // 分销商开瓶奖励运维补发
    DISTRIBUTOR_OPEN_BOTTLE_REWARD_OPERATIONAL_REISSUE(272, "分销商开瓶奖励补发"),
    // 经销商开瓶奖励运维补发
    DEALER_OPEN_BOTTLE_REWARD_OPERATIONAL_REISSUE(273, "经销商开瓶奖励补发"),
    // 终端开瓶奖励运维扣除
    TERMINAL_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT(280, "终端开瓶奖励扣除"),
    // 会员开瓶奖励运维扣除
    MEMBER_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT(281, "会员开瓶奖励扣除"),
    // 分销商开瓶奖励运维扣除
    DISTRIBUTOR_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT(282, "分销商开瓶奖励扣除"),
    // 经销商开瓶奖励运维扣除
    DEALER_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT(283, "经销商开瓶奖励扣除"),
    // 终端动销奖励运维扣除
    TERMINAL_SALE_REWARD_OPERATIONAL_DEDUCT(290, "终端动销奖励运维扣除"),
    // 会员动销奖励运维扣除
    MEMBER_SALE_REWARD_OPERATIONAL_DEDUCT(291, "会员动销奖励运维扣除"),
    // 分销商动销奖励运维扣除
    DISTRIBUTOR_SALE_REWARD_OPERATIONAL_DEDUCT(292, "分销商动销奖励运维扣除"),
    // 经销商动销奖励运维扣除
    DEALER_SALE_REWARD_OPERATIONAL_DEDUCT(293, "经销商动销奖励运维扣除"),
    ;

    private final Integer code;
    private final String msg;

    EventType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getName(Integer code){
        for (EventType contract : EventType.values()) {
            if (contract.getCode().equals(code)){
                return contract.getMsg();
            }
        }
        return "";
    }
}
