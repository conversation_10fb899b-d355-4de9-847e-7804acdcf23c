package com.intelliquor.cloud.shop.common.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.model.DeliveryModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ViolateOpenBottleResp {
    //违约日期
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date violateTime;

    // 违约扣除
    private BigDecimal score;

    // 违约扣除
    private String violateDesc;

    // 开瓶日期
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date drawTime;

    // 开瓶区域
    private String drawArea;

    // 产品名称
    private String goodsName;

    // 盖内码
    private String code;

    // 箱码
    private String codeXiang;

    // 经销商
    private String dealerName;

    // 合同授权区域
    private List<AuthAreaResp> authAreaList;

    // 收发货记录
    private List<DeliveryRecordResp> deliveryRecordList;
}
