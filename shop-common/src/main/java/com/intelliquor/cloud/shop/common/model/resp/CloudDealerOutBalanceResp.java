package com.intelliquor.cloud.shop.common.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
public class CloudDealerOutBalanceResp implements Serializable {

    private static final long serialVersionUID = -2612108572751192241L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "出库批次")
    private String transaction;

    @ApiModelProperty(value = "from经销商发货")
    private String fromDealerCode;

    @ApiModelProperty(value = "to那个经销商")
    private String toDealerCode;

    @ApiModelProperty(value = "扫码数")
    private Integer codeCount;

    @ApiModelProperty(value = "瓶数量")
    private Integer bottleNum;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 收货状态0 待收货 1收货中 2收货完成
     */
    private Integer receivingStatus;

    /**
     * 收货数量
     */
    private Integer receivingCount;


    /**
     * 代收人ID
     */
    private Integer receivingId;

    /**
     * 代收人名称
     */
    private String receivingName;

    /**
     * 代收人电话
     */
    private String receivingPhone;

    /**
     * 代收时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receivingDate;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 商品编码
     */
    private String goodsName;

    private String shopName;

    //随机取一码
    private String  code;
    //终端ID
    private Integer  shopId;

    //终端用户ID
    private Integer  shopUserId;

    private String remark;
}
