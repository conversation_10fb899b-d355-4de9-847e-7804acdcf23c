package com.intelliquor.cloud.shop.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.CloudDealerOrderSendWithdraw;
import com.intelliquor.cloud.shop.common.model.req.WithdrawOrderReq;

/**
* <AUTHOR>
* @description 针对表【t_cloud_dealer_order_send_withdraw(订单发货撤回详情)】的数据库操作Service
* @createDate 2024-05-23 13:39:48
*/
public interface CloudDealerOrderSendWithdrawService extends IService<CloudDealerOrderSendWithdraw> {

    void withdrawOrder(WithdrawOrderReq req);
}
