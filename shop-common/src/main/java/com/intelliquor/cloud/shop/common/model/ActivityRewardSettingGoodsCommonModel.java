package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 活动奖项配置,参与产品范围表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Getter
@Setter
@TableName("t_activity_reward_setting_goods")
public class ActivityRewardSettingGoodsCommonModel implements Serializable {

    private static final long serialVersionUID = -921566218454888968L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 实物产品编号
     */
    private String goodsCode;

    /**
     * 实物产品名称
     */
    private String goodsName;

    /**
     * t_activity_reward_setting表的id
     */
    private Integer settingId;

    /**
     * 商家id
     */
    private Long companyId;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


}
