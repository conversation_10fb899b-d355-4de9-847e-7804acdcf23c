package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2024/02/22 11:05
 */
@Getter
@AllArgsConstructor
public enum ProtocolTypeOldEnum {

    /**
     * 主协议
     */
    MAIN_PROTOCOL(0, "主协议"),

    /**
     * 附加协议
     */
    APPEND_PROTOCOL(1, "附加协议"),
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (ProtocolTypeOldEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

}
