package com.intelliquor.cloud.shop.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.TerminalProductProtocolConfigLimitModel;

import java.util.Date;

public interface TerminalProductProtocolConfigLimitCommonService extends IService<TerminalProductProtocolConfigLimitModel> {

    /**
     * 根据协议产品、终端等级和最早生效的协议生效时间，获取进货上限配置
     * @param protocolProductCode
     * @param levelCode
     * @param effectiveTime
     * @return
     */
    TerminalProductProtocolConfigLimitModel getLimitByProtocolProductAndLevelAndEffectiveTime(String protocolProductCode, String levelCode, Date effectiveTime);
}
