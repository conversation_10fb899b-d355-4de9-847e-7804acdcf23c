package com.intelliquor.cloud.shop.common.utils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.intelliquor.cloud.shop.common.enums.ContractTypeEnum;
import com.intelliquor.cloud.shop.common.service.resp.HotelTerminalProtocolProductResp;
import javafx.util.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.intelliquor.cloud.shop.common.service.resp.ProtocoMonthParamResp;
import com.intelliquor.cloud.shop.common.service.resp.ProtocoTimeParamResp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @PackageName com.intelliquor.cloud.shop.terminal.util
 * @ClassName CodeConstant
 * @description: TODO
 * @datetime 2023年 02月 18日 16:13
 * @version: 1.0
 */
@Component
public class CodeConstant {

    /**
     * 项目类型 gt:国台 gbs:国标拾
     */
    public static String gtType;
    @Value("${gt.type}")
    public void setGtType(String gtType) {
        CodeConstant.gtType = gtType;
        // 设置终端协议产品类型
        PROTOCOL_PRODUCT_TYPE_MAP = ImmutableMap.<Integer, String>builder()
                .put(0, "")
                // 国台显示“国标”，国标拾显示“国台国标10”
                .put(1, "gt".equals(CodeConstant.gtType) ? "国标":"国台国标酒10")
                .put(2, "酱酒")
                .put(3, "十五年")
                .put(4, "龙酒")
                .put(5, "十五年龙酒组合")
                .build();
    }

    /**
     * 终端类型
     */
    public static Map<String, String> SHOP_TYPE_MAP = ImmutableMap.<String, String>builder()
            .put("0", "烟酒终端")
            .put("1", "餐饮终端")
            .put("2", "团购终端")
            .put("3", "企业终端")
            .put("4", "连锁终端")
            .put("5", "会员")
            .put("6", "烟酒终端")
            .put("7", "连锁终端")
            .put("8", "虚拟终端")
            .put("9", "超级终端")
            .put("10", "连锁型餐饮酒店")
            .put("11", "特色型餐饮酒店")
            .put("12", "商务型餐饮酒店")
            .put("13", "宴席型餐饮酒店")
            .put("14", "渠道连锁终端")
            .put("20", "线上终端")
            .put("21", "商超连锁终端")
            .build();

    /**
     * 性别
     */
    public static Map<Integer, String> GENDER_MAP = ImmutableMap.of(0, "男", 1, "女");

    /**
     * 合同类型
     */
    public static Map<Integer, String> CONTRACT_TYPE_MAP = ImmutableMap.<Integer, String>builder()
            .put(2, "国标合同")
            .put(3, "酱酒合同")
            .put(4, "高端酒合同")
            .put(5, "高端酒合同")
            .put(6, "高端酒合同").build();
    /**
     * 标记
     */
    public static Map<Integer, String> TAG_MAP = ImmutableMap.of(1, "普通", 2, "多关注", 3, "重点关注");

    /**
     * 是否
     */
    public static Map<Integer, String> YES_NO_MAP = ImmutableMap.of(0, "否", 1, "是");
    public static Map<Integer, String> NO_YES_MAP = ImmutableMap.of(1, "否", 0, "是");

    /**
     * 收款方式
     */
    public static Map<Integer, String> RECEIVING_PAYMENT_TYPE_MAP = ImmutableMap.of(0, "支付宝", 1, "微信", 2, "银行卡");

    /**
     * 终端协议类型
     */
    public static Map<Integer, String> PROTOCOL_TYPE_MAP = ImmutableMap.of(0, "主协议", 1, "附加协议");

    /**
     * 终端协议的类型: (0陈列包量协议,1陈列协议,2-进货（无协议）3-超级终端陈列包量协议) 20240304杨俊让调整为：高端酒运营中心核心烟酒店销售协议
     */
    public static Map<Integer, String> PRODUCT_PROTOCOL_TYPE_MAP = ImmutableMap.of(0, "陈列包量协议", 1, "陈列协议",
            2, "进货（无协议）", 3, "超级终端陈列包量协议", 4, "高端酒运营中心核心烟酒店销售协议");

    /**
     * 终端协议产品类型
     * 由于需要根据配置文件设定具体的值，所以具体的值在setGtType中设置
     */
    public static Map<Integer, String> PROTOCOL_PRODUCT_TYPE_MAP = ImmutableMap.<Integer, String>builder().build();


    /**
     * 终端信息状态 0未激活 1激活 2客户经理审核中 3客户经理审核失败 4 中台审核中 5中台审核失败
     */
    public static Map<Integer, String> SHOP_STATUS_MAP = ImmutableMap.<Integer, String>builder()
            .put(0, "未激活")
            .put(1, "激活")
            .put(2, "客户经理审核中")
            .put(3, "客户经理审核失败")
            .put(4, "中台审核中")
            .put(5, "中台审核失败").build();
    /**
     * 修改数据类型 1:终端激活 2:终端协议变更 3:终端资料变更
     */
    public static Map<Integer, String> CHECK_TYPE_MAP = ImmutableMap.of(1, "终端激活", 2, "终端协议新增", 3, "终端资料变更", 4, "终端协议变更", 12, "补充协议");

    /**
     * 状态(0:未审批1:审批中,2:审批失败;3:审批成功)
     */
    public static Map<Integer, String> APPROVAL_STATUS_MAP = ImmutableMap.<Integer, String>builder()
            .put(0, "未审批")
            .put(1, "审批中")
            .put(2, "审批失败")
            .put(3, "审批成功").build();

    /**
     * 终端等级(1:A 2:B 3:C 4:D 5:进货终端 6:超级终端1星 7:超级终端2星 8:超级终端3星)
     */
    public static Map<Integer, String> TERMINAL_SHOP_LEVEL_MAP = ImmutableMap.<Integer, String>builder()
            .put(1, "A")
            .put(2, "B")
            .put(3, "C")
            .put(4, "D")
            .put(5, "进货终端")
            .put(6, "超级终端1星")
            .put(7, "超级终端2星")
            .put(8, "超级终端3星").build();

    /**
     * 账号类型(1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端)
     * 对应：表 t_cloud_dealer_info 的 account_type 字段
     */
    public static Map<Integer, String> ACCOUNT_TYPE = ImmutableMap.<Integer, String>builder()
            .put(1, "普通经销商")
            .put(2, "体验中心")
            .put(3, "普通分销商")
            .put(4, "合伙人")
            .put(5, "终端").build();

    /**
     * 对应表：t_terminal_shop_contract 的 contract_type 字段
     * 0：国台主品（国标）合同； 1：国台酱酒合同； 2：常规渠道经销合同； 3：国台酱酒金品合同；
     * 4：专卖店经销合同； 5：数智体验中心经销合同； 6：团购特约经销合同； 7：电商平台经销合同；
     * 8：商超连锁经销合同； 9：国台酱酒经销合同（电商）； 10：专卖店专销产品合同；
     * 11：国台酱酒经销合同； 12：葡萄酒合同； 13：定制酒合同； 14：封坛酒合同
     * 注：国标合同：0、2   酱酒合同：1、3
     */
    public static Map<Integer, String> CONTRACT_TYPE = ImmutableMap.<Integer, String>builder()
            .put(0, "国台国标合同")
            .put(1, "国台酱酒金品合同")
            .put(2, "国台国标合同")
            .put(3, "国台酱酒金品合同")
            .put(4, "专卖店经销合同")
            .put(5, "数智体验中心经销合同")
            .put(6, "团购特约经销合同")
            .put(7, "电商平台经销合同")
            .put(8, "商超连锁经销合同")
            .put(9, "国台酱酒经销合同（电商）")
            .put(10, "专卖店专销产品合同")
            .put(11, "国台酱酒经销合同")
            .put(12, "葡萄酒合同")
            .put(13, "定制酒合同")
            .put(14, "封坛酒合同").build();

    /**
     * 终端类型 1-烟酒店、2-专卖店、3-餐饮店、4-商超
     */
    public static Map<Integer, String> SHOP_TYPE_NAME = ImmutableMap.<Integer, String>builder()
            .put(1, "烟酒店")
            .put(2, "专卖店")
            .put(3, "餐饮店")
            .put(4, "商超").build();

    /**
     *  超级终端审批状态：0可升级 1未审核 2审核通过 3审核失败
     */
    public static Map<Integer, String> SUPER_SHOP_STATUS_MAP = ImmutableMap.<Integer, String>builder()
            .put(0, "可升级")
            .put(1, "待审核")
            .put(2, "已通过")
            .put(3, "已驳回").build();


    public final  static BigDecimal[] VISIT_COMPUTE_QTY = {BigDecimal.TEN, new BigDecimal(15), BigDecimal.ONE, new BigDecimal(1.5)};

    /**
     * 终端积分类型
     */
    public static Map<Integer, String> POINT_SOURCE_TYPE = ImmutableMap.<Integer, String>builder()
            .put(0, "")
            .put(1, "动销奖励")
            .put(2, "开瓶奖励")
            .put(3, "年返奖励")
            .put(4, "积分抵扣")
            .put(5, "取消进货")
            .put(6, "周期奖励")
            .put(7, "扫码退货")
            .put(8, "宴席推介人奖励")
            .put(9, "宴席季度奖励")
            .put(10, "陈列月度奖励")
            .put(11, "异常扣除")
            .put(12, "积分提现")
            .put(13, "个性化政策-半年度返利")
            .put(14, "下游进货款")
            .put(15, "地推陈列")
            .put(16, "年度包量奖励")
            .put(17, "动销进货坎级奖励")
            .put(18, "重复享受奖励扣除")
            .put(19, "异常开瓶扣除")
            .put(20,"个性化政策-季度奖励")
            .put(21,"酱酒分销")
            .put(22, "终端奖励")
            .put(23, "消费者奖励")
            .put(24, "积分迁移")
            .put(25,"合并终端").build();
    /**
     * 季度时间
     */
    public static Map<Integer, ProtocoTimeParamResp> QUARTER_TIME_MAP = ImmutableMap.<Integer, ProtocoTimeParamResp>builder()
            .put(1, new ProtocoTimeParamResp("01-01", "03-31"))
            .put(2, new ProtocoTimeParamResp("04-01", "06-30"))
            .put(3, new ProtocoTimeParamResp("07-01", "09-30"))
            .put(4, new ProtocoTimeParamResp("10-01", "12-31")).build();

    /**
     * 进货达成季度时间
     */
    public static Map<Integer, ProtocoTimeParamResp> STOCK_QUARTER_TIME_MAP = ImmutableMap.<Integer, ProtocoTimeParamResp>builder()
            .put(1, new ProtocoTimeParamResp("01-01", "03-31"))
            .put(2, new ProtocoTimeParamResp("04-03", "06-30"))
            .put(3, new ProtocoTimeParamResp("07-01", "09-30"))
            .put(4, new ProtocoTimeParamResp("10-01", "12-31")).build();
    /**
     * 陈列协议指定季度对应的协议生效时间范围
     * 作用：用于根据核算年、季度获取陈列协议的生效时间范围
     */
    public static Map<Integer, ProtocoTimeParamResp> DISPLAY_QUARTER_TIME_MAP = ImmutableMap.<Integer, ProtocoTimeParamResp>builder()
            .put(1, new ProtocoTimeParamResp("01-01", "03-20"))
            .put(2, new ProtocoTimeParamResp("01-01", "06-20"))
            .put(3, new ProtocoTimeParamResp("01-01", "09-20"))
            .put(4, new ProtocoTimeParamResp("01-01", "12-20")).build();

    /**
     * 陈列协议季度时间
     */
    public static Map<Integer, ProtocoTimeParamResp> DISPLAY_QUARTER_TIME_MAP_X = ImmutableMap.<Integer, ProtocoTimeParamResp>builder()
            .put(1, new ProtocoTimeParamResp("01-01", "03-20"))
            .put(2, new ProtocoTimeParamResp("03-21", "06-20"))
            .put(3, new ProtocoTimeParamResp("06-21", "09-20"))
            .put(4, new ProtocoTimeParamResp("09-21", "12-20")).build();

    public static Map<Integer, ProtocoTimeParamResp> DISPLAY_QUARTER_TIME_MAP_X_START = ImmutableMap.<Integer, ProtocoTimeParamResp>builder()
            .put(1, new ProtocoTimeParamResp("01-01", "03-31"))
            .put(2, new ProtocoTimeParamResp("04-01", "06-30"))
            .put(3, new ProtocoTimeParamResp("07-01", "09-30"))
            .put(4, new ProtocoTimeParamResp("10-01", "12-31")).build();
    /**
     * 陈列协议月份时间
     */
    public static Map<Integer, ProtocoMonthParamResp> DISPLAY_MONTH_TIME_MAP = ImmutableMap.<Integer, ProtocoMonthParamResp>builder()
            .put(1, new ProtocoMonthParamResp("01-01", "01-20",0,1))
            .put(2, new ProtocoMonthParamResp("01-21", "01-31",2,1))
            .put(3, new ProtocoMonthParamResp("02-01", "02-20",2,1))
            .put(4, new ProtocoMonthParamResp("02-21", "02-29",4,1))
            .put(5, new ProtocoMonthParamResp("03-01", "03-20",4,1))
            .put(6, new ProtocoMonthParamResp("03-21", "03-31",0,1))
            .put(7, new ProtocoMonthParamResp("04-01", "04-20",0,2))
            .put(8, new ProtocoMonthParamResp("04-21", "04-30",2,2))
            .put(9, new ProtocoMonthParamResp("05-01", "05-20",2,2))
            .put(10, new ProtocoMonthParamResp("05-21", "05-31",4,2))
            .put(11, new ProtocoMonthParamResp("06-01", "06-20",4,2))
            .put(12, new ProtocoMonthParamResp("06-21", "06-30",0,2))
            .put(13, new ProtocoMonthParamResp("07-01", "07-20",0,3))
            .put(14, new ProtocoMonthParamResp("07-21", "07-31",2,3))
            .put(15, new ProtocoMonthParamResp("08-01", "08-20",2,3))
            .put(16, new ProtocoMonthParamResp("08-21", "08-31",4,3))
            .put(17, new ProtocoMonthParamResp("09-01", "09-20",4,3))
            .put(18, new ProtocoMonthParamResp("09-21", "09-30",0,3))
            .put(19, new ProtocoMonthParamResp("10-01", "10-20",0,4))
            .put(20, new ProtocoMonthParamResp("10-21", "10-31",2,4))
            .put(21, new ProtocoMonthParamResp("11-01", "11-20",2,4))
            .put(22, new ProtocoMonthParamResp("11-21", "11-30",4,4))
            .put(23, new ProtocoMonthParamResp("12-01", "12-20",4,4))
            .put(24, new ProtocoMonthParamResp("12-21", "12-31",0,4)).build();

    /**
     * 陈列协议月份时间
     */
    public static Map<Integer, ProtocoMonthParamResp> DISPLAY_MONTH_MAP = ImmutableMap.<Integer, ProtocoMonthParamResp>builder()
            .put(1, new ProtocoMonthParamResp("01-01", "01-20",0,1))
            .put(2, new ProtocoMonthParamResp("01-21", "02-20",2,1))
            .put(3, new ProtocoMonthParamResp("02-21", "03-20",4,1))
            .put(4, new ProtocoMonthParamResp("03-21", "04-20",0,1))
            .put(5, new ProtocoMonthParamResp("04-21", "05-20",2,2))
            .put(6, new ProtocoMonthParamResp("05-21", "06-20",4,2))
            .put(7, new ProtocoMonthParamResp("06-21", "07-20",0,2))
            .put(8, new ProtocoMonthParamResp("07-21", "08-20",2,3))
            .put(9, new ProtocoMonthParamResp("08-21", "09-20",4,3))
            .put(10, new ProtocoMonthParamResp("09-21", "10-20",0,3))
            .put(11, new ProtocoMonthParamResp("10-21", "11-20",2,4))
            .put(12, new ProtocoMonthParamResp("11-21", "12-20",4,4)).build();

    /**
     * 季度时间
     * 作用：获取指定季度包量协议对应的协议生效时间范围
     */
    public static Map<Integer, ProtocoTimeParamResp> PACKAGE_QUANTITY_QUARTER_TIME_MAP = ImmutableMap.<Integer, ProtocoTimeParamResp>builder()
            .put(1, new ProtocoTimeParamResp("01-01", "03-31"))
            .put(2, new ProtocoTimeParamResp("01-01", "06-30"))
            .put(3, new ProtocoTimeParamResp("01-01", "09-30"))
            .put(4, new ProtocoTimeParamResp("01-01", "12-31")).build();

    /**
     * 高端酒的双月生效时间
     */
    public static Map<Integer, String> HIGH_END_WINE_DOUBLE_MONTH_TIME_MAP = ImmutableMap.<Integer, String>builder()
            .put(0, "01-01")
            .put(1, "03-01")
            .put(2, "05-01")
            .put(3, "07-01")
            .put(4, "09-01")
            .put(5, "11-01").build();
    // 获取当前年份
    private static String getYear() {
        Calendar calendar = Calendar.getInstance();
        return String.valueOf(calendar.get(Calendar.YEAR));
    }
    // 根据月份获取该月最后一天
    private static String getLastDayOfMonth(int month) {
        // 获取当前年份
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        YearMonth yearMonth = YearMonth.of(currentYear, month);
        return yearMonth.atEndOfMonth().toString();
    }
    /**
     * 高端酒协议指定对应的协议生效时间范围
     */
    public static   Map<Integer, ProtocoTimeParamResp> HIGH_END_WINE_TIME = ImmutableMap.<Integer, ProtocoTimeParamResp>builder()
            .put(1, new ProtocoTimeParamResp(getYear() +"01-01", getLastDayOfMonth(2)))
            .put(2, new ProtocoTimeParamResp(getYear() +"03-01", getYear() +"04-30"))
            .put(3, new ProtocoTimeParamResp(getYear() +"05-01", getYear() +"06-30"))
            .put(4, new ProtocoTimeParamResp(getYear() +"07-01", getYear() +"08-31"))
            .put(5, new ProtocoTimeParamResp(getYear() +"09-01", getYear() +"10-31"))
            .put(6, new ProtocoTimeParamResp(getYear() +"11-01", getYear() +"12-31"))
            .build();

    public static int getCurrentIntervalId() {
        LocalDate now = LocalDate.now();
        int year = Year.now().getValue();
        if (now.isBefore(LocalDate.of(year, 3, 1))) {
            return 1;
        } else if (now.isBefore(LocalDate.of(year, 5, 1))){
            return 2;
        } else if (now.isBefore(LocalDate.of(year, 7, 1))) {
            return 3;
        } else if (now.isBefore(LocalDate.of(year, 9, 1))) {
            return 4;
        } else if (now.isBefore(LocalDate.of(year, 11, 1))) {
            return 5;
        } else {
            return 6;
        }
    }
    public static ProtocoTimeParamResp getCurrentDateInterval() {
        int year = Year.now().getValue();
        LocalDate now = LocalDate.now();

        if (now.isBefore(LocalDate.of(year, 3, 1))) {
            return new ProtocoTimeParamResp(year + "-01-01", getLastDayOfMonth(2));
        } else if (now.isBefore(LocalDate.of(year, 5, 1))){
            return new ProtocoTimeParamResp(year + "-03-01", year + "-04-30");
        } else if (now.isBefore(LocalDate.of(year, 7, 1))) {
            return new ProtocoTimeParamResp(year + "-05-01", year + "-06-30");
        } else if (now.isBefore(LocalDate.of(year, 9, 1))) {
            return new ProtocoTimeParamResp(year + "-07-01", year + "-08-31");
        } else if (now.isBefore(LocalDate.of(year, 11, 1))) {
            return new ProtocoTimeParamResp(year + "-09-01", year + "-10-31");
        } else {
            return new ProtocoTimeParamResp(year + "-11-01", year + "-12-31");
        }
    }

    public static List<Pair<Integer, ProtocoTimeParamResp>> getTimeRanges() {
        List<Pair<Integer, ProtocoTimeParamResp>> list = new ArrayList<>();
        int year = Year.now().getValue();
        list.add(new Pair<>(1, new ProtocoTimeParamResp(year + "-01-01",getLastDayOfMonth(2))));
        list.add(new Pair<>(2, new ProtocoTimeParamResp(year + "-03-01", year + "-04-30")));
        list.add(new Pair<>(3, new ProtocoTimeParamResp(year + "-05-01", year + "-06-30")));
        list.add(new Pair<>(4, new ProtocoTimeParamResp(year + "-07-01", year + "-08-31")));
        list.add(new Pair<>(5, new ProtocoTimeParamResp(year + "-09-01", year + "-10-31")));
        list.add(new Pair<>(6, new ProtocoTimeParamResp(year + "-11-01", year + "-12-31")));
        return list;
    }

    public static ProtocoTimeParamResp getTimeRangeByIndex(Integer index) {
        List<Pair<Integer, ProtocoTimeParamResp>> timeRanges = getTimeRanges();

        for (Pair<Integer, ProtocoTimeParamResp> pair : timeRanges) {
            if (pair.getKey() == index) {
                return pair.getValue();
            }
        }

        return null; // 如果找不到对应的时间范围，返回null
    }

    public static Integer getStageByTime(LocalDateTime time) {
        List<Pair<Integer, ProtocoTimeParamResp>> timeRanges = getTimeRanges();

        for (Pair<Integer, ProtocoTimeParamResp> pair : timeRanges) {
            ProtocoTimeParamResp timeRange = pair.getValue();
            LocalDateTime startDate = LocalDate.parse(timeRange.getStartDate()).atStartOfDay();
            LocalDateTime endDate = LocalDate.parse(timeRange.getEndDate()).atTime(23, 59, 59);

            if ((time.isEqual(startDate) || time.isAfter(startDate)) && time.isBefore(endDate) || time.isEqual(endDate)) {
                return pair.getKey();
            }
        }

        return -1; // 如果时间不在任何阶段范围内，则返回-1
    }

    public static List<Pair<Integer, ProtocoTimeParamResp>> getCurrentAndNextTimeRange() {
        List<Pair<Integer, ProtocoTimeParamResp>> timeRanges = getTimeRanges();

        LocalDate now = LocalDate.now();
        Pair<Integer, ProtocoTimeParamResp> currentRange = null;
        Pair<Integer, ProtocoTimeParamResp> nextRange = null;

        for (Pair<Integer, ProtocoTimeParamResp> range : timeRanges) {
            String start = range.getValue().getStartDate();
            String end = range.getValue().getEndDate();

            LocalDate rangeStart = LocalDate.parse(start);
            LocalDate rangeEnd = LocalDate.parse(end);

            if ((now.equals(rangeStart) || now.equals(rangeEnd)) || (now.isAfter(rangeStart) && now.isBefore(rangeEnd)))
                currentRange = range;  //找到匹配当前日期的记录
            else if (now.isBefore(rangeStart)) {
                nextRange = range; //找到下一条记录
                break;
            }
        }

        List<Pair<Integer, ProtocoTimeParamResp>> result = new ArrayList<>();
        result.add(currentRange);
        result.add(nextRange);
        return result; //返回包含当前时间匹配的记录和下一条记录的列表
    }

    /**
     * 酒店渠道协议产品
     * 2-常规渠道经销合同
     * 24-常规渠道综合经销商合同
     * 3-国台酱酒经销合同
     * 26-饮渠道经销合同
     */
    public static Map<Integer, List<HotelTerminalProtocolProductResp>> HOTEL_CHANNEL_PROTOCOL_PRODUCT_MAP = ImmutableMap.<Integer, List<HotelTerminalProtocolProductResp>>builder()
            .put(ContractTypeEnum.USUAL_CHANNELS_DISTRIBUTION.getCode(), ImmutableList.of(
                    new HotelTerminalProtocolProductResp("0", "国标")
            ))
            .put(ContractTypeEnum.USUAL_CHANNELS_COMPREHENSIVE_DISTRIBUTION.getCode(), ImmutableList.of(
                    new HotelTerminalProtocolProductResp("0", "国标"),
                    new HotelTerminalProtocolProductResp("1", "酱酒")
            ))
            .put(ContractTypeEnum.SAUCE_WINE_DISTRIBUTION.getCode(), ImmutableList.of(
                    new HotelTerminalProtocolProductResp("1", "酱酒")
            ))
            .put(ContractTypeEnum.CATER_CHANNEL_DISTRIBUTION.getCode(), ImmutableList.of(
                    new HotelTerminalProtocolProductResp("0", "国标"),
                    new HotelTerminalProtocolProductResp("1", "酱酒")
            ))
            .build();
}
