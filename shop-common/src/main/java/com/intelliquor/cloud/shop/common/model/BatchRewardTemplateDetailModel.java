package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 账户奖扣模板详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Getter
@Setter
@TableName("batch_reward_template_detail")
public class BatchRewardTemplateDetailModel implements Serializable {

    private static final long serialVersionUID = -8782825926473341040L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 参数
     */
    private String paramCode;

    /**
     * 参数描述
     */
    private String paramDesc;

    /**
     * 是否必填 0非必填 1必填
     */
    private Integer requiredStatus;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}
