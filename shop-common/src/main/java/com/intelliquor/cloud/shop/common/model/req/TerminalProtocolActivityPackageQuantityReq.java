package com.intelliquor.cloud.shop.common.model.req;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 产品配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@ApiModel(value = "TerminalProtocolActivityPackageQuantityReq对象", description = "终端协议包量")
public class TerminalProtocolActivityPackageQuantityReq implements Serializable {

    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    @NotNull(message = "终端类别不能为空")
    private Integer terminalType;

    @NotBlank(message = "终端等级不能为空")
    private String levelCode;

    @NotNull(message = "协议类型不能为空")
    private Integer protocolType;

}
