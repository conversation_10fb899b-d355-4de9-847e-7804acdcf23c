package com.intelliquor.cloud.shop.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.intelliquor.cloud.shop.common.constant.CommonConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.gift.domain.req.GiftPromotionErrorLogReq;
import com.intelliquor.cloud.shop.common.gift.service.GiftPromotionErrorLogService;
import com.intelliquor.cloud.shop.common.gift.service.GiftPromotionInfoService;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.business.terminalReward.TerminalShopInfo;
import com.intelliquor.cloud.shop.common.model.resp.TerminalAccountTypeResp;
import com.intelliquor.cloud.shop.common.model.business.terminalRewardNew.RewardTerminalShopInfo;
import com.intelliquor.cloud.shop.common.service.*;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.HttpUtils;
import com.intelliquor.cloud.shop.common.utils.OaUtils;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TerminalShopCommonServiceImpl implements ITerminalShopCommonService {

    @Resource
    private TerminalRewardRecordPrepareMapper terminalRewardRecordPrepareMapper;

    @Autowired
    private TerminalScanDetailPlusDao terminalScanDetailPlusDao;

    @Autowired
    private TerminalRewardRecordNewDao terminalRewardRecordNewDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private CloudDealerRewardRecordPrepareMapper cloudDealerRewardRecordPrepareMapper;

    @Autowired
    private CloudDealerRewardRecordDao cloudDealerRewardRecordDao;

    @Autowired
    private DealerInfoCommonDao dealerInfoCommonDao;

    @Autowired
    private IDealerContractRelCommonService dealerContractRelCommonService;

    @Autowired
    private MemberShopCommonDao memberShopCommonDao;

    @Autowired
    private DealerInfoCommonService dealerInfoCommonService;

    @Autowired
    private TerminalShopContractCommonService terminalShopContractCommonService;

    @Autowired
    private TerminalShopCommonDao terminalShopCommonDao;

    @Resource
    private ITerminalShopDisableRecordCommonService terminalShopDisableRecordService;

    @Autowired
    private ShopDealerOrderPlusMapper shopDealerOrderPlusMapper;

    @Autowired
    private TerminalShopNodeDao terminalShopNodeDao;

    @Value("${send.oa.orderurl}")
    private String sendOaOrderUrl;

    @Value("${send.oa.orderapprovalurl}")
    private String sendOaOrderApprovalUrl;

    @Value("${account.score.url}")
    private String createAccountScoreUrl;
    @Autowired
    private IZtInterfaceService ztInterfaceService;

    @Autowired
    private ISendRewardCommomService sendRewardCommomService;


    @Autowired
    private ShopDealerOrderDetailPlusMapper shopDealerOrderDetailPlusMapper;

    @Autowired
    private GtSynOrderService gtSynOrderService;

    @Autowired
    private TerminalShopInfoScheduleCommonDao terminalShopInfoScheduleCommonDao;

    @Resource
    private OaUtils oaUtils;

    @Autowired
    private GiftPromotionInfoService giftPromotionInfoService;

    @Autowired
    private GiftPromotionErrorLogService giftPromotionErrorLogService;

    /**
     * @author: HLQ
     * @Date: 2023/5/8 15:09
     * @Description: 处理预备终端转正式终端时的积分数据
     */
    @Transactional
    @Override
    public void handlePrepareTerminalData(Integer shopId) {
        //处理终端的积分 t_terminal_reward_record_prepare
        LambdaQueryWrapper<TerminalRewardRecordPrepare> tqw = Wrappers.lambdaQuery();
        tqw.eq(TerminalRewardRecordPrepare::getIsDelete, 0);
        tqw.eq(TerminalRewardRecordPrepare::getRecordId, 0);
        tqw.eq(TerminalRewardRecordPrepare::getIsFlag, 0);
        tqw.eq(TerminalRewardRecordPrepare::getShopId, shopId);
        List<TerminalRewardRecordPrepare> recordPrepareList = terminalRewardRecordPrepareMapper.selectList(tqw);
        log.info("终端id为:{},共有{}终端积分记录", shopId, recordPrepareList.size());
        if (CollectionUtils.isNotEmpty(recordPrepareList)) {
            log.info("终端id为:{},共有{}终端积分记录", shopId, recordPrepareList.size());
            for (TerminalRewardRecordPrepare prepare : recordPrepareList) {
                Integer returnGoods = 0;
                if (prepare.getSource() == 1 && prepare.getDetailId() > 0) {
                    //可能有退货的情况
                    LambdaQueryWrapper<TerminalScanDetailModel> scanQw = Wrappers.lambdaQuery();
                    scanQw.eq(TerminalScanDetailModel::getId, prepare.getDetailId());
                    TerminalScanDetailModel detailModel = terminalScanDetailPlusDao.selectOne(scanQw);
                    returnGoods = detailModel.getReturnGoods();
                }

                if (returnGoods == 0) {
                    TerminalRewardRecordModel rewardRecord = new TerminalRewardRecordModel();
                    BeanUtils.copyProperties(prepare, rewardRecord);
                    rewardRecord.setId(null);
                    terminalRewardRecordNewDao.insert(rewardRecord);
                    prepare.setRecordId(rewardRecord.getId());
                } else {
                    prepare.setRecordId(-1L);
                }
                terminalRewardRecordPrepareMapper.updateById(prepare);
            }
            /*//处理终端得的奖励数据之和加到各个终端上
            Map<Integer, List<TerminalRewardRecordPrepare>> recordListMap = recordPrepareList.stream().filter(e -> e.getRecordId() > 0).collect(Collectors.groupingBy(TerminalRewardRecordPrepare::getShopId));
            for (Integer itemShop : recordListMap.keySet()) {
                List<TerminalRewardRecordPrepare> prepareList = recordListMap.get(itemShop);
                BigDecimal sumAwardVirtualAmount = Optional.ofNullable(prepareList)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(x -> x.getAmount() != null)
                        .map(TerminalRewardRecordPrepare::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                shopDao.setVirtualAmount(sumAwardVirtualAmount, itemShop);
            }*/
        }

        // 预备终端  可能存在补其他类型的积分
        LambdaQueryWrapper<TerminalRewardRecordModel> rLqw = Wrappers.lambdaQuery();
        rLqw.eq(TerminalRewardRecordModel::getShopId, shopId);
        rLqw.eq(TerminalRewardRecordModel::getIsDelete, 0);
        List<TerminalRewardRecordModel> terminalRewardRecordModelList = terminalRewardRecordNewDao.selectList(rLqw);
        BigDecimal sumVirtualAmount = Optional.ofNullable(terminalRewardRecordModelList)
                .orElse(new ArrayList<>())
                .stream()
                .filter(x -> x.getAmount() != null)
                .map(TerminalRewardRecordModel::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        shopDao.setVirtualAmount(sumVirtualAmount, shopId);


        //处理经销商得的奖励数据 t_cloud_dealer_reward_record_prepare
        LambdaQueryWrapper<CloudDealerRewardRecordPrepare> cqw = Wrappers.lambdaQuery();
        cqw.eq(CloudDealerRewardRecordPrepare::getIsDelete, 0);
        cqw.eq(CloudDealerRewardRecordPrepare::getRecordId, 0);
        cqw.eq(CloudDealerRewardRecordPrepare::getIsFlag, 0);
        cqw.eq(CloudDealerRewardRecordPrepare::getShopId, shopId);
        List<CloudDealerRewardRecordPrepare> dealerRewardList = cloudDealerRewardRecordPrepareMapper.selectList(cqw);
        log.info("终端id为:{},共有{}经销商积分记录", shopId, dealerRewardList.size());
        if (CollectionUtils.isNotEmpty(dealerRewardList)) {
            log.info("终端id为:{},共有{}经销商积分记录", shopId, dealerRewardList.size());
            for (CloudDealerRewardRecordPrepare prepare : dealerRewardList) {
                Integer returnGoods = 0;
                if (prepare.getOriginType() == 1 && prepare.getDetailId() > 0) {
                    //可能有退货的情况
                    LambdaQueryWrapper<TerminalScanDetailModel> scanQw = Wrappers.lambdaQuery();
                    scanQw.eq(TerminalScanDetailModel::getId, prepare.getDetailId());
                    TerminalScanDetailModel detailModel = terminalScanDetailPlusDao.selectOne(scanQw);
                    returnGoods = detailModel.getReturnGoods();
                }

                if (returnGoods == 0) {
                    CloudDealerRewardRecordModel cloudDealerRewardRecordModel = new CloudDealerRewardRecordModel();
                    BeanUtils.copyProperties(prepare, cloudDealerRewardRecordModel);
                    cloudDealerRewardRecordModel.setId(null);
                    cloudDealerRewardRecordDao.insert(cloudDealerRewardRecordModel);
                    prepare.setRecordId(cloudDealerRewardRecordModel.getId());
                } else {
                    prepare.setRecordId(-1l);
                }
                cloudDealerRewardRecordPrepareMapper.updateById(prepare);
            }
            //处理经销商得的奖励数据之和加到各个经销商上
            Map<String, List<CloudDealerRewardRecordPrepare>> recordListMap = dealerRewardList.stream().filter(e -> e.getRecordId() > 0l).collect(Collectors.groupingBy(CloudDealerRewardRecordPrepare::getDealerCode));
            for (String dealerCode : recordListMap.keySet()) {
                List<CloudDealerRewardRecordPrepare> prepareList = recordListMap.get(dealerCode);
                BigDecimal sumAwardVirtualAmount = Optional.ofNullable(prepareList)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(x -> x.getRewardAmount() != null)
                        .map(CloudDealerRewardRecordPrepare::getRewardAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                dealerInfoCommonDao.addVirtualAmount(dealerCode, sumAwardVirtualAmount);
            }
        }
    }

    @Override
    public void handleRewardData() {
        List<Integer> shopIdList = shopDao.getNoHandleRewardPrepare();
        for (Integer shopId : shopIdList) {
            handlePrepareTerminalData(shopId);
        }
    }

    /**
     * 根据终端id获取终端基础信息
     */
    @Override
    public TerminalShopInfo getTerminalInfoByTerminalShopId(Long terminalShopIdLong) {
        if (Objects.isNull(terminalShopIdLong)) {
            return null;
        }
        Integer terminalShopId = terminalShopIdLong.intValue();
        TerminalShopInfo terminalShopInfo = new TerminalShopInfo();
        TerminalShopCommonModel terminalShopCommonModel = terminalShopCommonDao.selectById(terminalShopId);
        if (Objects.nonNull(terminalShopCommonModel)) {
            terminalShopInfo.setMemberShopId(terminalShopCommonModel.getMemberShopId());
            // 查询终端合同信息
            TerminalShopContractCommonModel terminalShopContractCommonModel = terminalShopContractCommonService.getShopContractByMemberShopId(terminalShopCommonModel.getMemberShopId());
            if (Objects.nonNull(terminalShopContractCommonModel) && Objects.nonNull(terminalShopContractCommonModel.getDealerCode())) {
                terminalShopInfo.setContractCode(terminalShopContractCommonModel.getContractCode());
                // 查询经销商信息
                DealerContractRelModel dealerContractRelModel = dealerContractRelCommonService.getDealerContractRelByContractCode(terminalShopContractCommonModel.getContractCode());
                if (Objects.nonNull(dealerContractRelModel)) {
                    terminalShopInfo.setAffiliateId(dealerContractRelModel.getAffiliateId());
                    terminalShopInfo.setAffiliateName(dealerContractRelModel.getAffiliateName());
                    terminalShopInfo.setDealerCode(dealerContractRelModel.getDealerCode());
                    terminalShopInfo.setDealerName(dealerContractRelModel.getDealerName());
                    terminalShopInfo.setRegionId(dealerContractRelModel.getRegionId());
                    terminalShopInfo.setRegionName(dealerContractRelModel.getRegionName());
                }
            }

            // 根据终端id获取终端基础信息
            MemberShopCommon memberShop = memberShopCommonDao.selectById(terminalShopCommonModel.getMemberShopId());
            if (Objects.nonNull(memberShop)) {
                terminalShopInfo.setMemberShopId(terminalShopCommonModel.getMemberShopId());
                // 查询分销商信息
                DealerInfoCommonModel dealerInfoCommonModel = dealerInfoCommonService.getDealerInfoByDealerCodeAndAccountType(memberShop.getDealerCode(), OpenBottleTypeEnum.PTFXS.getType());
                if (Objects.nonNull(dealerInfoCommonModel)) {
                    terminalShopInfo.setDistributorCode(dealerInfoCommonModel.getDealerCode());
                    terminalShopInfo.setDistributorName(dealerInfoCommonModel.getDealerName());
                }

                // 查询经销商信息
                //DealerInfoCommonModel dealerInfoCommonModelParent = dealerInfoCommonService.getDealerInfoByDealerCodeAndAccountType(memberShop.getDealerCode(), OpenBottleTypeEnum.PTJXS.getType());
                //if(Objects.nonNull(dealerInfoCommonModelParent)) {
                //    terminalShopInfo.setDealerCode(dealerInfoCommonModelParent.getDealerCode());
                //    terminalShopInfo.setDealerName(dealerInfoCommonModelParent.getDealerName());
                //    // 根据经销商code获取经销商合同关系表信息（获取分公司和大区信息）
                //    DealerContractRelModel dealerContractRelModel = dealerContractRelCommonService.getDealerContractRelByDealerCode(dealerInfoCommonModelParent.getDealerCode());
                //        if(Objects.nonNull(dealerContractRelModel)) {
                //            terminalShopInfo.setAffiliateId(dealerContractRelModel.getAffiliateId());
                //            terminalShopInfo.setAffiliateName(dealerContractRelModel.getAffiliateName());
                //            terminalShopInfo.setRegionId(dealerContractRelModel.getRegionId());
                //            terminalShopInfo.setRegionName(dealerContractRelModel.getRegionName());
                //        }
                //}
            }
        }
        return terminalShopInfo;
    }

    @Override
    public RewardTerminalShopInfo getRewardCalculationTerminalInfoByShopId(Long terminalShopIdLong) {
        TerminalShopInfo terminalShopInfo = getTerminalInfoByTerminalShopId(terminalShopIdLong);
        if(Objects.isNull(terminalShopInfo)) {
            return null;
        }
        RewardTerminalShopInfo rewardTerminalShopInfo = new RewardTerminalShopInfo();
        BeanUtils.copyProperties(terminalShopInfo, rewardTerminalShopInfo);
        return rewardTerminalShopInfo;
    }

    /*
    * 通过终端副编码获取终端相关信息
     */
    @Override
    public  TerminalShopInfo getTerminalInfoByTerminalShopDeputyCode(Integer deputyCode){
        if(Objects.isNull(deputyCode)){
            return null;
        }
        // 根据终端副编码查询终端信息
        LambdaQueryWrapper<TerminalShopCommonModel> qw = Wrappers.lambdaQuery();
        qw.eq(TerminalShopCommonModel::getDeputyCode,deputyCode.toString());
        TerminalShopCommonModel terminalShopCommonModel = terminalShopCommonDao.selectOne(qw);
        TerminalShopInfo terminalShopInfo = new TerminalShopInfo();
        if (Objects.nonNull(terminalShopCommonModel)) {
            terminalShopInfo.setMemberShopId(terminalShopCommonModel.getMemberShopId());
            // 查询终端合同信息
            TerminalShopContractCommonModel terminalShopContractCommonModel = terminalShopContractCommonService.getShopContractByMemberShopId(terminalShopCommonModel.getMemberShopId());
            if (Objects.nonNull(terminalShopContractCommonModel) && Objects.nonNull(terminalShopContractCommonModel.getDealerCode())) {
                terminalShopInfo.setContractCode(terminalShopContractCommonModel.getContractCode());
                // 查询经销商信息
                DealerContractRelModel dealerContractRelModel = dealerContractRelCommonService.getDealerContractRelByContractCode(terminalShopContractCommonModel.getContractCode());
                if (Objects.nonNull(dealerContractRelModel)) {
                    terminalShopInfo.setAffiliateId(dealerContractRelModel.getAffiliateId());
                    terminalShopInfo.setAffiliateName(dealerContractRelModel.getAffiliateName());
                    terminalShopInfo.setDealerCode(dealerContractRelModel.getDealerCode());
                    terminalShopInfo.setDealerName(dealerContractRelModel.getDealerName());
                    terminalShopInfo.setRegionId(dealerContractRelModel.getRegionId());
                    terminalShopInfo.setRegionName(dealerContractRelModel.getRegionName());
                }
            }

            // 根据终端id获取终端基础信息
            MemberShopCommon memberShop = memberShopCommonDao.selectById(terminalShopCommonModel.getMemberShopId());
            if (Objects.nonNull(memberShop)) {
                terminalShopInfo.setMemberShopId(terminalShopCommonModel.getMemberShopId());
                // 查询分销商信息
                DealerInfoCommonModel dealerInfoCommonModel = dealerInfoCommonService.getDealerInfoByDealerCodeAndAccountType(memberShop.getDealerCode(), OpenBottleTypeEnum.PTFXS.getType());
                if (Objects.nonNull(dealerInfoCommonModel)) {
                    terminalShopInfo.setDistributorCode(dealerInfoCommonModel.getDealerCode());
                    terminalShopInfo.setDistributorName(dealerInfoCommonModel.getDealerName());
                }
            }
        }
        return terminalShopInfo;
    }
    /*
     * 通过宴席数据获取终端相关信息
     */
    @Override
    public  TerminalShopInfo getTerminalInfoByBanquetInfo(TerminalBanquetRewardRecordModel terminalBanquetRewardRecordModel){
        if(Objects.isNull(terminalBanquetRewardRecordModel.getMemberShopId())){
            log.error("宴席memberShopId为空,宴席数据:{}", JSON.toJSONString(terminalBanquetRewardRecordModel));
            throw new BusinessException("400","宴席memberShopId为空");
        }
        TerminalShopInfo terminalShopInfo = new TerminalShopInfo();
        terminalShopInfo.setMemberShopId(terminalBanquetRewardRecordModel.getMemberShopId());
        terminalShopInfo.setDealerCode(terminalBanquetRewardRecordModel.getDealerCode());
        terminalShopInfo.setDealerName(terminalBanquetRewardRecordModel.getDealerName());
        terminalShopInfo.setContractCode(terminalBanquetRewardRecordModel.getContractCode());
        // 查询经销商信息
        DealerContractRelModel dealerContractRelModel = dealerContractRelCommonService.getDealerContractRelByContractCode(terminalBanquetRewardRecordModel.getContractCode());
        if (Objects.nonNull(dealerContractRelModel)) {
            terminalShopInfo.setAffiliateId(dealerContractRelModel.getAffiliateId());
            terminalShopInfo.setAffiliateName(dealerContractRelModel.getAffiliateName());
            terminalShopInfo.setRegionId(dealerContractRelModel.getRegionId());
            terminalShopInfo.setRegionName(dealerContractRelModel.getRegionName());
        }
        // 根据终端id获取终端基础信息
        MemberShopCommon memberShop = memberShopCommonDao.selectById(terminalBanquetRewardRecordModel.getMemberShopId());
        if (Objects.nonNull(memberShop)) {
            // 查询分销商信息
            DealerInfoCommonModel dealerInfoCommonModel = dealerInfoCommonService.getDealerInfoByDealerCodeAndAccountType(memberShop.getDealerCode(), OpenBottleTypeEnum.PTFXS.getType());
            if (Objects.nonNull(dealerInfoCommonModel)) {
                terminalShopInfo.setDistributorCode(dealerInfoCommonModel.getDealerCode());
                terminalShopInfo.setDistributorName(dealerInfoCommonModel.getDealerName());
            }
        }
        return terminalShopInfo;
    }
    /**
     * 同步OA审批流的数据
     */
    @Override
    public void syncOAApproval() {
        LambdaQueryWrapper<TerminalShopNodeModel> qw = Wrappers.lambdaQuery();
        qw.eq(TerminalShopNodeModel::getNodeStatus, 0);
        qw.eq(TerminalShopNodeModel::getNodeType, 5);
        qw.eq(TerminalShopNodeModel::getNodeLevel, 1);
        List<TerminalShopNodeModel> shopNodeModelList = terminalShopNodeDao.selectList(qw);
        log.info("共有" + shopNodeModelList.size() + "条数据需要处理");
        HttpHeaders headers = oaUtils.getOaToken();
        for (int i = 0; i < shopNodeModelList.size(); i++) {
            TerminalShopNodeModel terminalShopNodeModel = shopNodeModelList.get(i);
            log.info("共有" + shopNodeModelList.size() + "条数据需要，当前处理到" + i + "条，id=" + terminalShopNodeModel.getId());
            try {
                handleOrderOAApproval(terminalShopNodeModel, headers);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 处理OA订单审批
     */
    @Transactional
    public void handleOrderOAApproval(TerminalShopNodeModel terminalShopNodeModel, HttpHeaders headers) {
        String oaId = terminalShopNodeModel.getOaId();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", oaId);
        log.info("查询OA--->{}的流程信息地址:::{}---->参数:{}", terminalShopNodeModel.getOaId(), sendOaOrderApprovalUrl, JSON.toJSONString(jsonObject));
        HttpEntity<Map<String, Object>> entity = new HttpEntity<Map<String, Object>>(jsonObject, headers);
        RestTemplate template = new RestTemplate();
        ResponseEntity<Map> exchange = template.exchange(sendOaOrderApprovalUrl, HttpMethod.POST, entity, Map.class);
        log.info("OA返回数据:{}", exchange.getBody().toString());
        Boolean hasAbandon = false;
        Map<String, Object> flowJson = null;
        Map<String, Object> flowPassJson = null;
        List<Map<String, Object>> list = (List<Map<String, Object>>) exchange.getBody().get("data");
        for (Map<String, Object> json : list) {
            if (json.containsKey("actionKey")) {
                String actionkey = json.get("actionKey").toString();
                if (actionkey.endsWith(CommonConstant.FLOW_TYPE_MAP.get("废弃"))) {
                    hasAbandon = true;
                    flowJson = json;
                    break;
                } else if (CommonConstant.FLOW_TYPE_MAP.get("流程结束").equals(actionkey)) {
                    flowJson = json;
                    hasAbandon = false;
                } else if (CommonConstant.FLOW_TYPE_MAP.get("审批人审批通过").equals(actionkey)) {
                    flowPassJson = json;
                    hasAbandon = false;
                } else if (CommonConstant.FLOW_TYPE_MAP.get("审批人驳回").equals(actionkey)) {
                    flowJson = json;
                    hasAbandon = true;
                }
            }
        }
        log.info("查询OA--->{}的流程信息有{}条,返回信息:{}", terminalShopNodeModel.getOaId(), list.size(), exchange.getBody().toString());
        if (Objects.nonNull(flowJson) && Objects.nonNull(flowPassJson) && CommonConstant.FLOW_TYPE_MAP.get("流程结束").equals(flowJson.get("actionKey").toString()) && !hasAbandon) {
            ShopDealerOrderPlus order = shopDealerOrderPlusMapper.getOrderParamById(terminalShopNodeModel.getOrderId());
            /*String handlerName = flowPassJson.get("handlerName").toString();
            String auditNote = flowPassJson.get("auditNote").toString();*/
            Date date = new Date();
            if (Objects.nonNull(flowJson) && flowJson.containsKey("actionDate")) {
                String actionDate = flowJson.get("actionDate").toString() + ":00";
                date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
            }
            terminalShopNodeModel.setUpdateName("OA审核");
            terminalShopNodeModel.setUpdateDate(date);
            terminalShopNodeModel.setNodeStatus("1");
            terminalShopNodeModel.setUpdateMsg("");
            terminalShopNodeDao.updateById(terminalShopNodeModel);

            ShopDealerOrderPlus model = new ShopDealerOrderPlus();
            model.setId(terminalShopNodeModel.getOrderId());
            model.setApprovalUserName("OA审核");
            model.setApprovalDate(date);
            model.setApprovalMsg("");
            model.setApprovalTime(new Date());
            //积分订单
            if (order.getVirtualAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (order.getHasDealer()) { //经销商 -->同步中台
                    model.setApprovalStatus(OrderApprovalStatus.ZT_APPROVAL.getCode());
                    // 订单推送到中台
                    sendScoreOrderToZT(order, model);
                } else { //分销商
                    model.setApprovalStatus(OrderApprovalStatus.DISTRIBUTOR_APPROVAL.getCode());
                }
            } else {
                model.setOrderStatus(1);
                //组装数据推送溯源
                sendOrderToSY(order);
                // 【赠酒】创建兑付订单
                try {
                    giftPromotionInfoService
                            .createGiftOrderByMainOrderId(terminalShopNodeModel.getOrderId());
                } catch (Exception e) {
                    log.error("赠酒订单创建异常主订单ID：{}", terminalShopNodeModel.getOrderId(), e);
                    GiftPromotionErrorLogReq req = new GiftPromotionErrorLogReq();
                    req.setOrderId(terminalShopNodeModel.getOrderId());
                    req.setErrorMsg(ExceptionUtils.getStackTrace(e));
                    giftPromotionErrorLogService.insertGiftPromotionErrorLog(req);
                }
            }
            shopDealerOrderPlusMapper.updateById(model);
        } else if (hasAbandon || (Objects.nonNull(flowJson) && CommonConstant.FLOW_TYPE_MAP.get("审批人驳回").equals(flowJson.get("actionKey").toString()))) {
               /* String handlerName = flowJson.get("handlerName").toString();
                String auditNote = flowJson.get("auditNote").toString();*/
            Date date = new Date();
            if (Objects.nonNull(flowJson) && flowJson.containsKey("actionDate")) {
                String actionDate = flowJson.get("actionDate").toString() + ":00";
                date = DateUtils.convert2Date(actionDate, "yyyy-MM-dd HH:mm:ss");
            }
            terminalShopNodeModel.setUpdateName("OA审核");
            terminalShopNodeModel.setUpdateDate(date);
            terminalShopNodeModel.setNodeStatus("1");
            terminalShopNodeModel.setUpdateMsg("");
            terminalShopNodeModel.setIsBack("1");
            ShopDealerOrderPlus model = new ShopDealerOrderPlus();
            model.setId(terminalShopNodeModel.getOrderId());
            model.setApprovalUserName("OA审核");
            model.setApprovalDate(date);
            model.setApprovalMsg("");
            model.setOrderStatus(4);
            model.setApprovalStatus(OrderApprovalStatus.ZT_REJECT.getCode());
            model.setApprovalTime(new Date());
            shopDealerOrderPlusMapper.updateById(model);
            terminalShopNodeDao.updateById(terminalShopNodeModel);

            // 如果积分下单 则退回积分
            ShopDealerOrderPlus orderCommonModel = shopDealerOrderPlusMapper.selectById(terminalShopNodeModel.getOrderId());
            BigDecimal virtualAmount = orderCommonModel.getVirtualAmount();
            if (virtualAmount.compareTo(BigDecimal.ZERO) > 0) {
                sendRewardCommomService.handleRejectScoreOrderRewardRecord(orderCommonModel);
            }
        }
    }

    private void sendScoreOrderToZT(ShopDealerOrderPlus orderCommonModel, ShopDealerOrderPlus updateOrder) {
        //获取订单明细数据  现在的逻辑一单一品只有一条明细数据
        LambdaQueryWrapper<ShopDealerOrderDetailPlus> lqw = Wrappers.lambdaQuery();
        lqw.eq(ShopDealerOrderDetailPlus::getOrderId, orderCommonModel.getId());
        List<ShopDealerOrderDetailPlus> detailList = shopDealerOrderDetailPlusMapper.selectList(lqw);
        //获取终端联盟信息
        ShopModel shopModel = shopDao.getById(orderCommonModel.getShopId());
        shopModel.setShopId(shopModel.getId());
        if (ObjectUtil.isEmpty(shopModel)) {
            throw new BusinessException("未找到联盟账号信息");
        }
        if (Integer.valueOf(1).equals(shopModel.getStatus())) {
            throw new BusinessException("联盟账号已被禁用");
        }
        if (StringUtils.isBlank(shopModel.getDealerCode())) {
            throw new BusinessException("联盟子编码为空");
        }
        //获取终端云商信息
        CloudDealerInfoModel shopDealerIfo = dealerInfoCommonDao.selectDealerIdByDealerCode(shopModel.getDealerCode());
        if (shopDealerIfo == null) {
            throw new BusinessException("联盟子编码对应的dealerInfo为空");
        }
        //发货商信息
        CloudDealerInfoModel sendDealerInfo = dealerInfoCommonDao.getDealerInfoByDealerCode(orderCommonModel.getSenderCode());
        // 数据推送中台
        JSONObject rtnJson = ztInterfaceService.sendScoreOrderOpprove(shopModel, shopDealerIfo, orderCommonModel, detailList, sendDealerInfo);
        log.info("下单调中台返回数据:{}", Objects.nonNull(rtnJson) ? rtnJson.toJSONString() : "null");
        if (Objects.isNull(rtnJson) || !rtnJson.getJSONObject("resp_data").getJSONObject("result").getBoolean("success")) {
            updateOrder.setOrderStatus(9);
            String message = "";
            try {
                message = rtnJson.getJSONObject("resp_data").getJSONObject("result").getString("message");
            } catch (Exception e) {
            }
            updateOrder.setApprovalMsg(message);
        }
    }

    /**
     * 组装数据推送溯源
     */
    private void sendOrderToSY(ShopDealerOrderPlus order) {
        LambdaQueryWrapper<ShopDealerOrderDetailPlus> lqw = Wrappers.lambdaQuery();
        lqw.eq(ShopDealerOrderDetailPlus::getOrderId, order.getId());
        List<ShopDealerOrderDetailPlus> detailList = shopDealerOrderDetailPlusMapper.selectList(lqw);
        log.info("OA审批订单单号为:{},同步溯源开始", order.getOrderCode());
        gtSynOrderService.sycOrderToGt(order, detailList.get(0));
        log.info("OA审批订单单号为:{},同步溯源完成", order.getOrderCode());
    }

    @Override
    public TerminalShopCommonModel getTerminalShopByMemberShopId(Integer memberShopId) {
        log.info("根据memberShopId:[{}]查询终端", memberShopId);
        if (Objects.isNull(memberShopId)) {
            throw new BusinessException("memberShopId不能为空");
        }
        return terminalShopCommonDao.selectOne(new LambdaQueryWrapper<>(TerminalShopCommonModel.class)
                .eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                .eq(TerminalShopCommonModel::getMemberShopId, memberShopId));
    }

    @Override
    public Boolean isHotelTerminalByTerminalShopId(Integer terminalShopId) {
        log.info("根据terminalShopId:{}查询终端是否为酒店餐饮终端", terminalShopId);
        if (Objects.isNull(terminalShopId)) {
            throw new BusinessException("terminalShopId不能为空");
        }
        LambdaQueryWrapper<TerminalShopCommonModel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TerminalShopCommonModel::getId, terminalShopId);
        lambdaQueryWrapper.eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey());
        lambdaQueryWrapper.last(" limit 1");
        TerminalShopCommonModel terminalShop = terminalShopCommonDao.selectOne(lambdaQueryWrapper);
        // 如果shopType为酒店餐饮终端，则返回true，否则返回false
        if (Objects.isNull(terminalShop)) {
            throw new BusinessException("根据terminalShopId:" + terminalShopId + "查询终端信息为空");
        }
        // 如果shopType为酒店餐饮终端，则返回true，否则返回false
        return isHotelTerminal(terminalShop);
    }

    @Override
    public Boolean isHotelTerminalByMemberShopId(Integer memberShopId) {
        log.info("根据memberShopId:[{}]查询终端是否为酒店餐饮终端", memberShopId);
        if (Objects.isNull(memberShopId)) {
            throw new BusinessException("memberShopId不能为空");
        }
        LambdaQueryWrapper<TerminalShopCommonModel> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TerminalShopCommonModel::getMemberShopId, memberShopId);
        lambdaQueryWrapper.eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey());
        lambdaQueryWrapper.last(" limit 1");
        TerminalShopCommonModel terminalShop = terminalShopCommonDao.selectOne(lambdaQueryWrapper);

        // 如果shopType为酒店餐饮终端，则返回true，否则返回false
        boolean isHotelTerminal = isHotelTerminal(terminalShop);
        log.info("根据memberShopId:[{}]查询终端是否为酒店餐饮终端结果为:[{}]", memberShopId, isHotelTerminal);
        return isHotelTerminal;
    }

    @Override
    public Boolean isHotelTerminal(TerminalShopCommonModel terminalShop) {
        if (Objects.isNull(terminalShop)) {
            throw new BusinessException("终端信息不能为空");
        }
        return terminalShop.getShopType().equals(TerminalShopTypeEnum.CHAIN_CATERING_HOTEL_TERMINAL.getType()) ||
                terminalShop.getShopType().equals(TerminalShopTypeEnum.FEATURED_CATERING_HOTEL_TERMINAL.getType()) ||
                terminalShop.getShopType().equals(TerminalShopTypeEnum.BUSINESS_CATERING_HOTEL_TERMINAL.getType()) ||
                terminalShop.getShopType().equals(TerminalShopTypeEnum.BANQUET_CATERING_HOTEL_TERMINAL.getType());
    }

    @Override
    public Boolean isHotelTerminalByShopType(Integer shopType) {
        if (Objects.isNull(shopType)) {
            throw new BusinessException("shopType不能为空");
        }
        // 如果shopType为酒店餐饮终端，则返回true，否则返回false
        return shopType.equals(TerminalShopTypeEnum.CHAIN_CATERING_HOTEL_TERMINAL.getType()) ||
                shopType.equals(TerminalShopTypeEnum.FEATURED_CATERING_HOTEL_TERMINAL.getType()) ||
                shopType.equals(TerminalShopTypeEnum.BUSINESS_CATERING_HOTEL_TERMINAL.getType()) ||
                shopType.equals(TerminalShopTypeEnum.BANQUET_CATERING_HOTEL_TERMINAL.getType());
    }
    @Override
    public List<TerminalShopCommonModel> getTerminalShopMergeList(List<Integer> terminalShopId) {
        if (CollectionUtils.isEmpty(terminalShopId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TerminalShopCommonModel> terminalShopQw = Wrappers.lambdaQuery();
        terminalShopQw.in(TerminalShopCommonModel::getId, terminalShopId);
//       与金星确认主店分店查询不包含删除标记，通过上层入口仅判断终端附表删除标记
//        terminalShopQw.eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey());
        List<TerminalShopCommonModel> terminalShopMergeList = terminalShopCommonDao.selectList(terminalShopQw);
        return terminalShopMergeList.stream()
                .filter(item -> item.getMergeType() != CommonConstant.TERMINAL_MERGE_TYPE)
                .collect(Collectors.toList());
    }
    @Override
    public List<Integer> getEnabledTerminalShopIds(List<Integer> terminalShopId) {
        LambdaQueryWrapper<TerminalShopCommonModel> terminalShopQw = Wrappers.lambdaQuery();
        terminalShopQw.in(TerminalShopCommonModel::getId, terminalShopId);
//        与金星确认查询启用禁用状态不查询删除标记，仅通过上层入口判断附表删除状态
//        terminalShopQw.eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey());
        List<TerminalShopCommonModel> terminalShopCommonList = terminalShopCommonDao.selectList(terminalShopQw);
        if (CollectionUtils.isEmpty(terminalShopCommonList)) {
            return new ArrayList<>();
        }
        List<Integer> memberShopId = terminalShopCommonList.stream()
                .map(TerminalShopCommonModel::getMemberShopId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<MemberShopCommon> memberShopQw = Wrappers.lambdaQuery();
        memberShopQw.in(MemberShopCommon::getId, memberShopId);
        memberShopQw.eq(MemberShopCommon::getStatus, MemberShopStatusEnum.STATUS_ENABLE.getStatus());
        List<MemberShopCommon> memberShopCommonList = memberShopCommonDao.selectList(memberShopQw);
        if (CollectionUtils.isEmpty(memberShopCommonList)) {
            return new ArrayList<>();
        }
        List<Integer> memberShopCommonId = memberShopCommonList.stream()
                .map(MemberShopCommon::getId)
                .collect(Collectors.toList());
        return terminalShopCommonList.stream()
                .filter(item -> memberShopCommonId.contains(item.getMemberShopId()))
                .map(TerminalShopCommonModel::getId)
                .collect(Collectors.toList());
    }

        /**
         * 根据memberShopId获取其相关的分店终端id列表
         * @param memberShopId
         * @return
         */
        @Override
        public List<Integer> getBranchTerminalShopIdsByMemberShopId (Integer memberShopId){
            if(Objects.isNull(memberShopId)) {
                throw new BusinessException("memberShopId不能为空");
            }
            List<Integer> terminalShopIds = new ArrayList<>();
            terminalShopIds.add(memberShopId);
            // 根据memberShopId查询终端信息
            TerminalShopCommonModel terminalShopCommonModel = terminalShopCommonDao.selectOne(new LambdaQueryWrapper<TerminalShopCommonModel>()
                    .eq(TerminalShopCommonModel::getMemberShopId, memberShopId)
                    .eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey()));
            // 判断是否为主店
            if (Objects.nonNull(terminalShopCommonModel) && terminalShopCommonModel.getMergeType() == MergeTypeEnum.MAIN_SHOP.getKey()) {
                // 查询分店终端信息(和金星确定包括已删除的终端)
                List<TerminalShopCommonModel> branchTerminalShopList = terminalShopCommonDao.selectList(new LambdaQueryWrapper<TerminalShopCommonModel>()
                                .eq(TerminalShopCommonModel::getMergeId, terminalShopCommonModel.getId())
                        //.eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                );
                if (CollectionUtils.isNotEmpty(branchTerminalShopList)) {
                    terminalShopIds.addAll(branchTerminalShopList.stream().map(TerminalShopCommonModel::getMemberShopId).collect(Collectors.toList()));
                }
            }
            return terminalShopIds;
        }

        @Override
        public Map<String, List<Integer>> filterNotActiveMergedSubShopTerminal (List < Long > terminalShopIds) {
            if (terminalShopIds == null || terminalShopIds.size() == 0) {
                throw new BusinessException("不存在需要补算的奖励终端！");
            }

            HashMap<String, List<Integer>> stringListHashMap = new HashMap<>();

            List<TerminalShopInfoScheduleCommonModel> terminalShopInfoSchedules = terminalShopInfoScheduleCommonDao.selectList(
                    new LambdaQueryWrapper<TerminalShopInfoScheduleCommonModel>().eq(TerminalShopInfoScheduleCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                            .eq(TerminalShopInfoScheduleCommonModel::getStatus, 0)
                            .in(TerminalShopInfoScheduleCommonModel::getTerminalShopId, terminalShopIds)
            );
            // 未激活抛出
            List<Integer> filterStatusEq0TerminalShopIds = terminalShopInfoSchedules.stream().map(item -> item.getTerminalShopId()).collect(Collectors.toList());
            stringListHashMap.put("filterStatusEq0TerminalShopIds", filterStatusEq0TerminalShopIds);

            List<TerminalShopCommonModel> terminalShops = terminalShopCommonDao.selectList(
                    new LambdaQueryWrapper<TerminalShopCommonModel>().eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                            .eq(TerminalShopCommonModel::getMergeType, 2)
                            .ne(TerminalShopCommonModel::getMergeId, 0)
                            .in(TerminalShopCommonModel::getId, terminalShopIds)
            );
            // 分店终端抛出
            List<Integer> filterSubShopIds = terminalShops.stream().map(item -> item.getId()).collect(Collectors.toList());
            stringListHashMap.put("filterSubShopIds", filterSubShopIds);
            return stringListHashMap;
        }
    @Override
    public String generateTerminalTypeByTerminalType(Integer terminalType) {
        String storeType = "";
        switch (terminalType) {
            case 0: //5-渠道终端
                storeType = "5";
                break;
            case 1: //7-餐饮终端
                storeType = "7";
                break;
            case 2: //9-团购终端
                storeType = "9";
                break;
            case 3: //6-企业终端
                storeType = "6";
                break;
            case 4: //8-连锁终端
                storeType = "8";
                break;
            case 6: //10-渠道终端会员
                storeType = "10";
                break;
            case 7: //11-连锁终端会员
                storeType = "11";
                break;
            case 8: //12-非会员虚拟终端
                storeType = "12";
                break;
            case 9: //14-超级终端
                storeType = "14";
                break;
            case 10: //15-连锁型餐饮酒店
                storeType = "15";
                break;
            case 11: //16-特色型餐饮酒店
                storeType = "16";
                break;
            case 12: //17-商务型餐饮酒店
                storeType = "17";
                break;
            case 13: //18-宴席型餐饮酒店
                storeType = "18";
                break;
            case 14: //19-渠道连锁终端
                storeType = "19";
                break;
            default:
                storeType = terminalType.toString();
                break;
        }
        return storeType;
    }

    @Override
    public Boolean isMainShop(Integer memberShopId) {
        log.info("判断终端是否为主店，memberShopId:{}", memberShopId);
        if (Objects.isNull(memberShopId)) {
            throw new IllegalArgumentException("memberShopId不能为空");
        }
        // 获取终端信息
        TerminalShopCommonModel terminalShopCommonModel = getTerminalShopByMemberShopId(memberShopId);

        if (Objects.isNull(terminalShopCommonModel)) {
            log.error("根据memberShopId:[{}]查询终端信息为空", memberShopId);
            throw new BusinessException("根据memberShopId:" + memberShopId + "查询终端信息为空");
        }

        // 直接比较 mergeType 字段的值：
        // 如果终端的合并类型为“未合并” 或者 “主店” 则视为主店
        Integer mergeType = terminalShopCommonModel.getMergeType();
        boolean isMainShop = Objects.equals(mergeType, MergeTypeEnum.NOT_MERGE.getKey()) ||
                             Objects.equals(mergeType, MergeTypeEnum.MAIN_SHOP.getKey());

        log.info("判断终端是否为主店，memberShopId: [{}]，结果为: [{}]", memberShopId, isMainShop);
        return isMainShop;
    }

    @Override
    public TerminalAccountTypeResp getTerminalAccountTypeByMemberShopId(Long memberShopId){
        if (Objects.isNull(memberShopId)) {
            throw new BusinessException("memberShopId不能为空");
        }
        TerminalAccountTypeResp terminalAccountTypeResp = new TerminalAccountTypeResp();
        // 终端账户类型
        Integer accountType = null;

        // 根据终端id获取终端基础信息
        MemberShopCommon memberShop = memberShopCommonDao.selectById(memberShopId);
        terminalAccountTypeResp.setMemberName(memberShop.getName());
        if (Objects.nonNull(memberShop)) {
            // 查询终端账户信息
            DealerInfoCommonModel dealerInfoCommonModel = dealerInfoCommonService.getDealerInfoByCode(memberShop.getDealerCode());
            if (Objects.nonNull(dealerInfoCommonModel)) {
                accountType = dealerInfoCommonModel.getAccountType();
                terminalAccountTypeResp.setAccountType(accountType);
            }
        }
        // 查询终端信息
        TerminalShopCommonModel terminalShopCommonModel = terminalShopCommonDao.selectOne(new LambdaQueryWrapper<TerminalShopCommonModel>()
                .eq(TerminalShopCommonModel::getMemberShopId, memberShopId)
                .eq(TerminalShopCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey()));
        if (Objects.nonNull(terminalShopCommonModel)) {
            terminalAccountTypeResp.setMergeType(terminalShopCommonModel.getMergeType());
            // 判断终端类型为终端，则查询终端类型
            if (Objects.nonNull(accountType) && Objects.equals(accountType, OpenBottleTypeEnum.ZD.getType())) {
                terminalAccountTypeResp.setShopType(terminalShopCommonModel.getShopType());
            }
        }
        return terminalAccountTypeResp;
    }

    @Override
    public void createAccountScoreByMemberShopId(Integer memberId) {
        try{
            if (Objects.isNull(memberId)) {
                throw new BusinessException("memberId不能为空");
            }
            String url = createAccountScoreUrl;
            JSONObject param = new JSONObject();
            param.put("transactionSource", "INIT_SCORE_ACCOUNT");
            param.put("memberId", memberId);
            param.put("business_no",memberId);
            Map<String, String> headParams = new HashMap<>();
            String result = HttpUtils.doRestfulPost(url, headParams, param.toString());
            JSONObject resultJson = JSONObject.parseObject(result);
            if (!resultJson.getBoolean("status")) {
                log.error("终端memberShopId:"+ memberId +"积分账户创建失败,失败原因：" + resultJson.getString("remark"));
                throw new BusinessException("400", "积分账户创建失败,失败原因：" + resultJson.getString("remark"));
            }else{
                log.info("终端memberShopId:{},积分账户创建成功", memberId);
            }
        }catch (Exception e) {
            log.error("终端memberShopId:{},积分账户创建失败,失败原因：error:{}", memberId, e.getMessage());
            throw new BusinessException("400",  e.getMessage());
        }
    }
}
