package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员业代发货授权表
 * @TableName t_terminal_auth_manag_delivery
 */
@TableName(value ="t_terminal_auth_manag_delivery")
@Data
public class TerminalAuthManagDeliveryModel implements Serializable {
    /**
     * terminal_shop表id
     */
    private Integer terminalShopId;

    /**
     * 账户表id
     */
    private Integer terminalAccountManagerId;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否删除（1删除0未删除）
     */
    private Integer delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否授权（1授权0未授权）
     */
    private Boolean isAuth;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}