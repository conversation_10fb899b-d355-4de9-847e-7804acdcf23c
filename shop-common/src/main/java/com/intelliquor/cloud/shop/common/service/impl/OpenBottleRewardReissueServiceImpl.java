package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.intelliquor.cloud.shop.common.constant.KafkaTopicConstant;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.ActivityRewardExceptionRecordDao;
import com.intelliquor.cloud.shop.common.dao.ActivityRewardRecordDao;
import com.intelliquor.cloud.shop.common.dao.ConsumerScanDetailCommonDao;
import com.intelliquor.cloud.shop.common.enums.ActivityType;
import com.intelliquor.cloud.shop.common.model.ConsumerScanDetailCommonModel;
import com.intelliquor.cloud.shop.common.model.req.ReissueOpenBottleRewardReq;
import com.intelliquor.cloud.shop.common.model.resp.ReissueOpenBottleRewardResp;
import com.intelliquor.cloud.shop.common.service.IOpenBottleRewardReissueService;
import com.intelliquor.cloud.shop.common.utils.KafkaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 开瓶奖励重新发放服务实现类
 * <p>
 * 主要功能：
 * 1. 实现开瓶奖励的重新发放逻辑
 * 2. 处理消费者扫码明细的状态更新
 * 3. 支持批量处理和分布式锁
 * 4. 提供幂等性检查和异常处理
 * </p>
 *
 * <AUTHOR>
 * @since 2024/03/21
 */
@Slf4j
@Service
public class OpenBottleRewardReissueServiceImpl extends AbstractRewardReissueService<ReissueOpenBottleRewardReq, ReissueOpenBottleRewardResp> 
        implements IOpenBottleRewardReissueService {

    private static final String CONSUMER_SCAN_DETAIL_TABLE = "t_consumer_scan_detail";
    private final ConsumerScanDetailCommonDao consumerScanDetailCommonDao;

    @Resource
    private OpenBottleRewardReissueServiceImpl self;

    public OpenBottleRewardReissueServiceImpl(
            ActivityRewardRecordDao activityRewardRecordDao,
            ActivityRewardExceptionRecordDao activityRewardExceptionRecordDao,
            KafkaUtils kafkaUtils,
            RedisTemplate<String, Object> redisTemplate,
            ConsumerScanDetailCommonDao consumerScanDetailCommonDao) {
        super(activityRewardRecordDao, activityRewardExceptionRecordDao, kafkaUtils, redisTemplate);
        this.consumerScanDetailCommonDao = consumerScanDetailCommonDao;
    }

    @Override
    public ReissueOpenBottleRewardResp reissueOpenBottleReward(final ReissueOpenBottleRewardReq req) {
        return self.processRewardReissue(req);
    }

    @Override
    protected String getRewardTypeName() {
        return "开瓶";
    }

    @Override
    protected String getLockValue() {
        return "reissueOpenBottleReward";
    }

    @Override
    protected String getLockKey(String lockValue) {
        return String.format(RedisConstant.OPS_OPEN_BOTTLE_REWARD_KEY, lockValue);
    }

    @Override
    protected void validateRequest(ReissueOpenBottleRewardReq request) {
        Optional.ofNullable(request)
            .map(ReissueOpenBottleRewardReq::getIds)
            .orElseThrow(() -> new IllegalArgumentException("请求参数不能为空"));
    }

    @Override
    protected List<Integer> getRequestIds(ReissueOpenBottleRewardReq request) {
        return request.getIds();
    }

    @Override
    protected String getOriginTable() {
        return CONSUMER_SCAN_DETAIL_TABLE;
    }

    @Override
    protected Integer getActivityType() {
        return ActivityType.OPEN_BOTTLE_REWARD.getCode();
    }

    @Override
    protected boolean validateBatchRecords(List<Integer> batchIds) {
        return Optional.ofNullable(consumerScanDetailCommonDao.selectBatchIds(batchIds))
                .map(CollectionUtils::isNotEmpty)
                .orElse(false);
    }

    /**
     * 更新消费者扫码明细状态
     * <p>
     * 将消费者扫码明细的状态重置为未处理状态，以便重新发放开瓶奖励
     * </p>
     *
     * @param batchIds 需要更新状态的ID列表
     * @return 成功更新状态的ID列表
     */
    @Override
    protected List<Integer> updateRecordStatus(List<Integer> batchIds) {
        log.info("开始更新消费者扫码明细状态, 记录数量: {}", batchIds.size());

        // 构建更新对象，重置状态为未处理
        final ConsumerScanDetailCommonModel updateModel = new ConsumerScanDetailCommonModel();
        updateModel.setStatus(0); // 重置状态为未处理

        // 构建更新条件
        final LambdaUpdateWrapper<ConsumerScanDetailCommonModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ConsumerScanDetailCommonModel::getId, batchIds);
        
        // 执行批量更新
        int updatedCount = consumerScanDetailCommonDao.update(updateModel, updateWrapper);
        log.info("消费者扫码明细状态更新完成, 更新记录数: {}", updatedCount);
        
        // 如果更新成功，返回原始ID列表；否则返回空列表
        return updatedCount > 0 ? batchIds : Collections.emptyList();
    }
    
    /**
     * 获取需要发送消息的ID列表
     * <p>
     * 对于开瓶奖励，需要获取消费者扫码明细关联的抽奖ID作为消息ID
     * </p>
     *
     * @param updatedIds 成功更新状态的ID列表
     * @return 需要发送消息的抽奖ID列表
     */
    @Override
    protected List<Integer> getMessageIds(List<Integer> updatedIds) {
        log.info("获取开瓶奖励消息ID列表, 原始ID数量: {}", updatedIds.size());
        
        // 参数校验
        if (CollectionUtils.isEmpty(updatedIds)) {
            log.warn("更新ID列表为空，无法获取消息ID");
            return Collections.emptyList();
        }

        // 查询消费者扫码明细记录
        List<ConsumerScanDetailCommonModel> scanDetails = consumerScanDetailCommonDao.selectBatchIds(updatedIds);
        if (CollectionUtils.isEmpty(scanDetails)) {
            log.warn("未找到对应的消费者扫码明细记录，无法获取抽奖ID");
            return Collections.emptyList();
        }

        // 提取有效的抽奖ID
        List<Integer> drawIds = scanDetails.stream()
                .map(ConsumerScanDetailCommonModel::getDrawId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 结果校验
        if (CollectionUtils.isEmpty(drawIds)) {
            log.warn("未找到有效的抽奖ID，跳过发送消息");
            return Collections.emptyList();
        }

        log.info("开瓶奖励使用抽奖ID作为消息ID，共获取到{}个有效抽奖ID", drawIds.size());
        return drawIds;
    }

    @Override
    protected ReissueOpenBottleRewardResp buildEmptyResponse() {
        return ReissueOpenBottleRewardResp.builder()
                .existingRewardIds(Collections.emptyList())
                .existingExceptionIds(Collections.emptyList())
                .build();
    }

    @Override
    protected ReissueOpenBottleRewardResp createResponse(List<Integer> existingRewardIds, List<Integer> existingExceptionIds) {
        return ReissueOpenBottleRewardResp.builder()
                .existingRewardIds(existingRewardIds)
                .existingExceptionIds(existingExceptionIds)
                .build();
    }

    @Override
    protected String getKafkaTopic() {
        return KafkaTopicConstant.OPEN_BOTTLE_REWARD_TOPIC;
    }

    /**
     * 获取事件类型列表
     * <p>
     * 普通开瓶奖励不限定特定的事件类型，返回空列表表示查询所有事件类型
     * </p>
     *
     * @return 空列表，表示不添加event_type条件，查询所有事件类型
     */
    @Override
    protected List<Integer> getEventTypes() {
        // 返回空列表表示不限制事件类型，这是一个明确的业务决策
        // 在这个服务中，我们需要处理所有类型的开瓶奖励
        return Collections.emptyList();
    }
}