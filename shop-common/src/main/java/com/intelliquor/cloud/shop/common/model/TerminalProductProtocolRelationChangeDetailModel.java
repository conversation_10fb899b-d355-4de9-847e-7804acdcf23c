package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@TableName("t_terminal_product_protocol_relation_change_detail")
public class TerminalProductProtocolRelationChangeDetailModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户id
     */
    private Integer companyId;

    /**
     * t_member_shop | 终端表的主键id
     */
    private Long memberShopId;

    /**
     * t_terminal_shop | 终端表的主键id
     */
    private Long terminalShopId;

    /**
     * 变更类型：1.新增 2.修改
     */
    private Long newId;

    /**
     * 协议审核状态 0未审核 1审核通过 2审核失败
     */
    private Long oldId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Integer createUserId;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Integer updateUserId;

    private Integer isDelete;

    /**
     * t_terminal_product_protocol_relation_change表id
     */
    private Long changeId;

    /**
     * 产品协议表-协议编码 新
     */
    private String newProtocolCode;

    /**
     * 产品协议表-协议编码 旧
     */
    private String oldProtocolCode;

}
