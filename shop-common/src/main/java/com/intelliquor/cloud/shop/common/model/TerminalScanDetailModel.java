package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
* 描述：终端扫码明细表实体类
* <AUTHOR>
* @date 2022-08-17
*/
@Data
@TableName("t_terminal_scan_detail")
public class TerminalScanDetailModel {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "扫码批次id")
    private Integer balanceId;

    @ApiModelProperty(value = "扫码进货方式1-按单扫码进货 2-按码扫码进货 3-一键收货")
    private Integer scanType;

    @ApiModelProperty(value = "扫的码")
    private String qrcode;

    @ApiModelProperty(value = "码类型 1-箱外码 2-箱内码 3-盒外 4-盒内 5-瓶外 6-瓶内 7-盖内 8-nfc")
    private Integer codeType;

    @ApiModelProperty(value = "箱码")
    private String codeXiang;

    @ApiModelProperty(value = "箱内所有瓶码,逗号分割")
    private String codeIn;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品所属经销商编码")
    private String dealerCode;

    @ApiModelProperty(value = "商品所属经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "出库单号")
    private String outNo;

    @ApiModelProperty(value = "出库时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    @ApiModelProperty(value = "进货终端id")
    private Integer shopId;

    @ApiModelProperty(value = "扫码人Id")
    private Integer createUserId;

    @ApiModelProperty(value = "扫码人类型0-客户经理1-经销商人员2-终端人员 3-联盟 4-业代")
    private Integer createUserType;

    @ApiModelProperty(value = "扫码时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "是否删除0-未删除 1-已删除")
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "商户Id")
    private Integer companyId;

    /**
     * 瓶箱比，eg. 1:6、1:6:6，1:6是没有盒码的，1:6:6是有盒码的
     */
    private String bottleBoxRatio;

    /**
     * 是否有盒码1-有 2-无，瓶箱比为1:6:6时有,1:6时无
     */
    private Integer hasBox;

    /**
     * 合同编号
     */
    private String contractNo;
    private String contractCode;

    /**
     * 按单出库的订单号
     */
    private String dealerOrderNo;

    @ApiModelProperty(value = "同步中台状态0-未同步或同步失败 1-同步成功")
    private Integer syncZtStatus;

    @ApiModelProperty(value = "同步中台的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncZtTime;

    @ApiModelProperty(value = "同步中台失败的原因")
    private String syncZtMsg;

    /**
     * 活动id
     */
    private Integer activityId;

    /**
     * 虚拟货款
     */
    private BigDecimal virtualAmount = BigDecimal.ZERO;

    /**
     * 是否首单 0-否 1-是
     */
    private Integer isFirstOrder;

    /**
     * 订单号
     */
    private String orderCode;

    /**
     * 是否退货标识 0:未退货 1:已退货
     * */
    private Integer returnGoods;

    /**
     * 退货时间
     * */
    private Date returnGoodsTime;

    /**
     * 实际收货的订单编号
     */
    private String receivedOrderCode;


    @ApiModelProperty(value = "一键收货实际扫的码")
    private String oneQrCode;

    /**
     * 是否需要审批(0:否;1是)
     */
    private Integer hasCheck;

    /**
     * 是否同步到顾问(0:未同步;1已同步)
     */
    private Integer hasGw;




    /**
     * 状态 0-正常 1-异常
     */
    @TableField(exist = false)
    private Integer status = 0;

    /**
     * 扫码描述信息
     */
    @TableField(exist = false)
    private String scanMsg;

    /**
     * 经销商手机号
     */
    @TableField(exist = false)
    private String dealerPhone;

    /**
     * 批次号
     */
    @TableField(exist = false)
    private String transaction;


    /**
     * 经度
     */
    @TableField(exist = false)
    private Double longitude;

    /**
     * 纬度
     */
    @TableField(exist = false)
    private Double latitude;



    @ApiModelProperty(value = "周期奖励批次id")
    @TableField(exist = false)
    private Integer cycleBalanceId;

    /**
     * 订货单Id
     */
    @TableField(exist = false)
    private Long orderId;

    /**
     * 周期奖励金额
     */
    @TableField(exist = false)
    private BigDecimal cycleVirtualAmount = BigDecimal.ZERO;

    /**
     * 实际收货的订单id
     */
    @TableField(exist = false)
    private Long receivedOrderId;

    //0:会员 1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端，6:平台公司
    @TableField(exist = false)
    private Integer accountType;

    @TableField(exist = false)
    private Integer sourceId;

    /**
     * 代收人ID
     */
    @TableField(exist = false)
    private Integer receivingId;

    /**
     * 代收人名称
     */
    @TableField(exist = false)
    private String receivingName;

    /**
     * 代收人电话
     */
    @TableField(exist = false)
    private String receivingPhone;

    /**
     * t_terminal_scan_detail_check_code 的ID
     */
    @TableField(exist = false)
    private Integer checkCodeId;

    /**
     * 审批状态(0:审批中;1通过;2驳回)
     */
    @TableField(exist = false)
    private Integer checkState;

    @TableField(exist = false)
    private String checkMsg;

    @ApiModelProperty(value = "是否已成功执行经销商奖励:默认是1已计算;0:未计算;-1:计算失败")
    private Integer isDealerReward;

    @ApiModelProperty(value = "是否已成功执行终端奖励:默认是1已计算;0:未计算;-1:计算失败")
    private Integer isShopReward;

    @ApiModelProperty(value = "划分ID;业代ID;采集人ID")
    private Integer businessUserId;

    @ApiModelProperty(value = "t_terminal_task_package主键")
    private Integer tpId;

    @ApiModelProperty(value = "业务代表id")
    private Integer brokerId;

    @ApiModelProperty(value = "客户经理ID")
    private Integer managerId;


    //收货类型:(0:扫码收货;1:出库收货;2:一键收货;3:客户经理代收)
    @TableField(exist = false)
    private Integer receiptType;

    @ApiModelProperty(value = "是否稽查:-1无需稽核,0:未稽查;1收货已稽查;2:退货已稽查")
    private Integer hasInspect;

    //收否为选销品 1是 默认null或者0为部署
    @TableField(exist = false)
    private Integer isXuanXiao;

    //数据一致性使用
    @TableField(exist = false)
    private String tid;

    @TableField(exist = false)
    private Integer isOneFlag;

    // 收货地点
    @TableField(exist = false)
    private Integer receivingLocation;

    // 距离(米)
    @TableField(exist = false)
    private BigDecimal distanceNum;
    /**
     * 卖方类型 0:会员 1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端，6:平台公司
     */
    @TableField(exist = false)
    private Integer salerAccountType;

    @TableField(exist = false)
    private Integer isMember;
}
