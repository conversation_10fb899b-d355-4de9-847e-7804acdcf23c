package com.intelliquor.cloud.shop.common.model;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 一键收货设置
 * @TableName t_dealer_set_receipt
 */
@Data
@TableName("t_dealer_set_receipt")
public class DealerSetReceiptModel implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "set_id", type = IdType.AUTO)
    private Long setId;

    private Long relId;
    @TableField(exist = false)
    private String relIds;
    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 是否开启(0:未开启;1开启)
     */
    private Integer isStatus;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 合同类型 0：国台主品合同1：国台酱酒合同2：常规渠道经销合同3：国台酱酒经销合同4：专卖店经销合同5：数智体验中心经销合同6：团购特约经销合同7：电商平台经销合同8：商超连锁经销合同9：国台酱酒经销合同(电商)
     */
    private Integer contractType;
    @TableField(exist = false)
    private String contractTypeName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 创建人ID
     */
    private Integer createUser;

    /**
     * 最后一次更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * 最后一次更新人
     */
    private Integer updateUser;

    private static final long serialVersionUID = 1L;

    public String getIsStatusName() {
        return this.isStatus == 0? "否": "是";
    }
}
