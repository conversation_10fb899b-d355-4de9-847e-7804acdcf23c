package com.intelliquor.cloud.shop.common.model.req;

import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.common.model.TerminalScanDetailModel;
import com.intelliquor.cloud.shop.common.model.resp.CodeInfoResp;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @PackageName com.intelliquor.cloud.shop.common.model.req
 * @ClassName AsyncSendDealerRewardReq
 * @description: TODO
 * @datetime 2023年 07月 28日 16:15
 * @version: 1.0
 */
@Data
public class AsyncSendDealerRewardReq implements Serializable {
    private List<CodeInfoResp> codeInfoRespList;
    private String shopType;
    private Integer originType;
    private ShopModel shopInfo;
    private List<TerminalScanDetailModel> detailModelList;


    private String contractCode;
    private String dealerCode;
    private Integer balanceId;
    private Map<Integer,CodeInfoResp> codeMap;
}
