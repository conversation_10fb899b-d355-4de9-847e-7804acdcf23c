package com.intelliquor.cloud.shop.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.CheckRuleSettingModel;

/**
 * <AUTHOR>
 * @description 稽核规则
 * @createDate 2023-10-19 15:00:20
 */
public interface ICheckRuleSettingService extends IService<CheckRuleSettingModel> {

    void saveCheckRuleSetting(CheckRuleSettingModel checkRuleSettingModel);

    void updateCheckRuleSettingModel(CheckRuleSettingModel checkRuleSettingModel);

    CheckRuleSettingModel getCheckRuleSettingModelById(String id);

    void delCheckRuleSettingModelById(String id);
}
