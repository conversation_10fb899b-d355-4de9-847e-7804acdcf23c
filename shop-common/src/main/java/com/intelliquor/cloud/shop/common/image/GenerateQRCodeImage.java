package com.intelliquor.cloud.shop.common.image;

import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.entity.AccessTokenEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.Map;

/**
 *  生成小程序码
 * <AUTHOR>
 * @date 2019/11/19 11:32
 * @desc
 */
@Component
public class GenerateQRCodeImage {

    /**
     * 小程序 APPID
     */
    @Value("${APP_ID_NEW}")
    private String appIdNew;

    /**
     * 小程序 AppSecret
     */
    @Value("${APP_SECRET_NEW}")
    private String appSecretNew;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取小程序接口调用凭证
     * @return
     */
    public AccessTokenEntity getAccessToken(){
        ResponseEntity<String> responseEntity = null;

        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appIdNew + "&secret=" + appSecretNew;
        responseEntity = restTemplate.postForEntity(url, null, String.class);

        return JSONObject.parseObject(responseEntity.getBody(), AccessTokenEntity.class);
    }


    public BufferedImage createQRCode() {
        ResponseEntity<BufferedImage> responseEntity = null;
        Map map = new HashMap<>();
        map.put("path", "pages/message/message");
        map.put("width", 400);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_JPEG);

        String accessToken = this.getAccessToken().getAccess_token();

        String url = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=" + accessToken;

        responseEntity = restTemplate.postForEntity(url, headers, BufferedImage.class, map);

        return responseEntity.getBody();


    }


}











