package com.intelliquor.cloud.shop.common.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Auther: tianms
 * @Date: 2021/05/15 17:25
 * @Description:
 */
@Data
public class SmsInfoConfigModel implements Serializable {

    private static final long serialVersionUID = 1947874578745401618L;
    /**
     * 主键
     */
    private Integer Id;
    /**
     * 公司Id
     */
    private Integer companyId;
    /**
     * keyId
     */
    private String accessKeyId;
    /**
     * key密钥
     */
    private String accessKeySecret;
    /**
     * 类别
     */
    private  Integer type;
    /**
     * 签名名称
     */
    private String signName;
    /**
     * 模板code
     */
    private  String templateCode;

    /**
     * 平台类别
     */
    private Integer platformType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date  updateTime;

    /**
     * 场景
     */
    private String scene;

    /**
     * 场景模板code
     */
    private String sceneTemplateCode;
}
