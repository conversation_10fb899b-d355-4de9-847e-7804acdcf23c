package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReturnGoodsStatusEnum {
    /**
     * 未退货
     */
    NOT_RETURN(0, "未退货"),

    /**
     * 已退货
     */
    RETURNED(1, "已退货")
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (ReturnGoodsStatusEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }
}
