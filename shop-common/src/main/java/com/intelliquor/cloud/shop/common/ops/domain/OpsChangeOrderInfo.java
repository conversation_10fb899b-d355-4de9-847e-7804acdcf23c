package com.intelliquor.cloud.shop.common.ops.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.intelliquor.cloud.shop.common.basequery.MybatisPlusEntity;
import java.util.Date;
import lombok.Data;


/**
 * 更新订单信息 t_ops_change_order_info
 * <AUTHOR>
@TableName("t_ops_change_order_info")
@Data
public class OpsChangeOrderInfo extends MybatisPlusEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 批件号
     */
    private String code;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发货人
     */
    private String deliveryAgentId;

    /**
     * 收货人
     */
    private String agentId;

    /**
     * 收货人子编码
     */
    private String agentSubCode;

    /**
     * sku
     */
    private String productSku;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 状态1未同步溯源2同步溯源完成3同步溯源出错
     */
    private Integer status;

    /**
     * 发货数量
     */
    private String num;

    /**
     * 结果
     */
    private String result;

    /**
     * 扩展1（发货商编号）
     */
    private String ext1;

    /**
     * 扩展2
     */
    private String ext2;

    /**
     * 修改前数据
     */
    private String originalData;


    /**
     * 备注
     */
    private String remark;

}
