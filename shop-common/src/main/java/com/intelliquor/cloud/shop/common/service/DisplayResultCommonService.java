package com.intelliquor.cloud.shop.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.DisplayResultModel;

import java.util.List;

public interface DisplayResultCommonService extends IService<DisplayResultModel> {
    /*
    * 判断宴席用酒奖励是否满足发放标准
    * */
    Boolean isExecutionDeal (DisplayResultModel displayResultModel);

    Integer generateRewardType(Integer rewardType);

    Integer resetGenerateRewardType(Integer rewardType);
    List<DisplayResultModel> splitDisplayResultModel(DisplayResultModel displayResultModel);

}
