package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProtocolProductCodeEnum {
    /**
     * 国标
     */
    GUOBIAO(0, "国标"),

    /**
     * 酱酒
     */
    JIANGJIU(1, "酱酒"),
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (ProtocolProductCodeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }
}
