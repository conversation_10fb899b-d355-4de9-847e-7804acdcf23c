package com.intelliquor.cloud.shop.common.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: MAX
 * @CreateTime: 2023-08-02  14:39
 */
@Data
public class ContractPointsSummaryResp implements Serializable {
    private static final long serialVersionUID = 8911305738520565282L;

    /**
     * 合同编码
     */
    @JsonProperty("contract_code")
    private String contractCode;

    /**
     * 积分合计
     */
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    /**
     * 待审核金额
     */
    @JsonProperty("audit_amount")
    private BigDecimal auditAmount;

    /**
     * 已使用金额
     */
    @JsonProperty("used_amount")
    private BigDecimal usedAmount;

    /**
     * 余额
     */
    private BigDecimal balance;
}
