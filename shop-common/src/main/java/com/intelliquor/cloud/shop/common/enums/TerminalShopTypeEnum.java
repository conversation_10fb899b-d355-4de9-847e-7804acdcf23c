package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Auther: sunshine
 * @Date: 2024/4/26
 * @Description: 终端类型
 */
@Getter
@AllArgsConstructor
public enum TerminalShopTypeEnum {
    // type, storeCode, name
    CHANNEL_TERMINAL(0, "5", "渠道终端"),
    CATERING_TERMINAL(1, "7", "餐饮终端"),
    TEAM_TERMINAL(2, "9", "团购终端"),
    COMPANY_TERMINAL(3, "6", "企业终端"),
    CHAIN_TERMINAL(4, "8", "连锁终端"),
    // 原代码中 case 5 没有对应的 storeType，这里暂时不添加，或者根据实际业务补充
    // case 6 对应 10-渠道终端会员
    CHANNEL_TERMINAL_MEMBER(6, "10", "渠道终端会员"),
    // case 7 对应 11-连锁终端会员
    CHAIN_TERMINAL_MEMBER(7, "11", "连锁终端会员"),
    // case 8 对应 12-非会员虚拟终端
    NON_MEMBER_VIRTUAL_TERMINAL(8, "12", "非会员虚拟终端"),
    // case 9 对应 14-超级终端
    SUPER_TERMINAL(9, "14", "超级终端"),
    CHAIN_CATERING_HOTEL_TERMINAL(10, "15", "连锁型餐饮酒店"),
    FEATURED_CATERING_HOTEL_TERMINAL(11, "16", "特色型餐饮酒店"),
    BUSINESS_CATERING_HOTEL_TERMINAL(12, "17", "商务型餐饮酒店"),
    BANQUET_CATERING_HOTEL_TERMINAL(13, "18", "宴席型餐饮酒店"),
    CHANNEL_CHAIN_TERMINAL(14, "19", "渠道连锁终端"),
    ONLINE_TERMINAL(20, "20", "线上终端"), // 示例，如果type>=20的storeCode就是type的字符串值，这里可以保持一致
    SUPER_SHOP_TERMINAL(21, "21", "商超连锁终端"), // 示例
    ;

    private final Integer type;
    private final String storeCode; // storeCode是中台需要的字段，对应这里定义的terminalShopType。但是从20开始，terminalShopType和中台的字段完全对应（增加终端类型时，需要中台相关人员确认。）
    private final String name;

    static Map<Integer, TerminalShopTypeEnum> enumMap = new HashMap<>();

    static {
        for (TerminalShopTypeEnum type : TerminalShopTypeEnum.values()) {
            enumMap.put(type.getType(), type);
        }
    }

    public static TerminalShopTypeEnum getName(Integer type) {
        return enumMap.get(type);
    }

    public static String getNameByType(Integer type) {
        TerminalShopTypeEnum enumValue = enumMap.get(type);
        return Objects.nonNull(enumValue) ? enumValue.getName() : null; // 使用Objects.nonNull避免空指针
    }

    // 新增方法，用于获取 storeCode
    public static String getStoreCodeByType(Integer type) {
        TerminalShopTypeEnum enumValue = enumMap.get(type);
        return Objects.nonNull(enumValue) ? enumValue.getStoreCode() : null; // 使用Objects.nonNull避免空指针
    }
}
