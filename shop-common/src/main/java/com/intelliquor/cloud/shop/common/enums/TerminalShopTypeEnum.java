package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: sunshine
 * @Date: 2024/4/26
 * @Description: 终端类型
 */
@Getter
@AllArgsConstructor
public enum TerminalShopTypeEnum {
    CHANNEL_TERMINAL(0, "渠道终端"),
    CATERING_TERMINAL(1, "餐饮终端"),
    TEAM_TERMINAL(2, "团购终端"),
    COMPANY_TERMINAL(3, "企业终端"),
    CHAIN_TERMINAL(4, "连锁终端"),
    VIP_TERMINAL(5, "会员终端"),
    CHAIN_CATERING_HOTEL_TERMINAL(10,"连锁型餐饮酒店"),
    FEATURED_CATERING_HOTEL_TERMINAL(11,"特色型餐饮酒店"),
    BUSINESS_CATERING_HOTEL_TERMINAL(12,"商务型餐饮酒店"),
    BANQUET_CATERING_HOTEL_TERMINAL(13,"宴席型餐饮酒店"),
    CHANNEL_CHAIN_TERMINAL(14,"渠道连锁终端"),
    ONLINE_TERMINAL(20,"线上终端"),
    SUPER_SHOP_TERMINAL(21,"商超连锁终端"),
    ;

    private Integer type;

    private String name;

    static Map<Integer, TerminalShopTypeEnum> enumMap = new HashMap<Integer, TerminalShopTypeEnum>();

    static {
        for (TerminalShopTypeEnum type : TerminalShopTypeEnum.values()) {
            enumMap.put(type.getType(), type);
        }
    }

    public static TerminalShopTypeEnum getName(Integer type) {
        return enumMap.get(type);
    }
    public static String getNameByType(Integer type) {
        return enumMap.get(type).getName();
    }

}
