package com.intelliquor.cloud.shop.common.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DealerRewardRecordDataResp {

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 商品编码
     */
    private String goodCode;

    /**
     * 来源类型
     */
    private Integer originType;

    /**
     * 来源类型String
     */
    private String originTypeName;

    /**
     * ；来源Id
     */
    private Integer originId;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 批次号
     */
    private String transaction;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 同步状态
     */
    private Integer status;

    /**
     * 同步失败返回信息
     */
    private String message;

    /**
     * 上账类型(是增加还是扣减的标识) 1:增加 2：冻结 3:扣减
     */
    private Integer changeType;

    /**
     * 账户类型1：分销出库奖(经销商发货、终端按单收货) 2：零售推荐奖（零售） 3：终端奖励（终端向经销商下单后，产生的给经销商的奖励即为终端奖励）
     */
    private String accountType;

    /**
     * 扫的码
     */
    private String qrCode;

    /**
     * 终端Id
     */
    private Integer shopId;
    /**
     * 终端名称
     */
    private String shopName;

    /**
     * 终端编号
     */
    private String shopCode;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 异常类型
     */
    private Integer errorType;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String remark;

    private Integer goodNumber;

    /**
     * 上账类型明细
     */
    private String changeTypeDetail;

    /**
     * 码类型 1箱码 2盒码 3瓶码
     */
    private Integer codeType;

    /**
     * 箱码/盒码/瓶码
     */
    private String codeStr;

    /**
     * 终端、会员、分销商编码
     */
    private String storecode;


}
