<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.FenceDealerAuthAreaMapper">
    <select id="selectPageList" resultType="com.intelliquor.cloud.shop.common.model.resp.DealerAuthAreaResp">
        SELECT * FROM t_fence_dealer_auth_area t WHERE t.is_delete =0
        <if test="authId != null">
            AND t.auth_id = #{authId}
        </if>
        order by t.id
    </select>

    <select id="getAreaListByDealerId" parameterType="java.lang.Integer" resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuthArea">
        SELECT
            b.*
        FROM
            t_fence_dealer_auth a
                LEFT JOIN t_fence_dealer_auth_area b ON a.id = b.auth_id
        WHERE
            a.is_delete = 0
          AND b.is_delete = 0
          AND a.contract_status = 1
          AND a.dealer_id = #{violateDealerId}
    </select>

    <select id="getAreaListByContractCode" parameterType="java.lang.String" resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuthArea">
        SELECT
            b.*
        FROM
            t_fence_dealer_auth a
                LEFT JOIN t_fence_dealer_auth_area b ON a.id = b.auth_id
        WHERE
            a.is_delete = 0
          AND b.is_delete = 0
          AND a.contract_status = 1
          AND a.contract_code = #{contractCode}
    </select>

    <select id="getDealerByLocation" resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*
        FROM t_fence_dealer_auth_area daa
        left join t_fence_dealer_auth da on daa.auth_id = da.id
        WHERE
        da.dealer_id != #{dealerId}
        and LOCATE(CONCAT(daa.province, daa.city, daa.district), #{location} ) > 0
    </select>

    <select id="getDealerByLocationAndProvince" resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*
        FROM t_fence_dealer_auth_area daa
        left join t_fence_dealer_auth da on daa.auth_id = da.id
        WHERE
        da.dealer_id != #{dealerId}
        and LOCATE(daa.province, #{location}) > 0
    </select>

    <select id="getDealerByLocationAndProvinceAndCity"
            resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*
        FROM t_fence_dealer_auth_area daa
        left join t_fence_dealer_auth da on daa.auth_id = da.id
        WHERE
        da.dealer_id != #{dealerId}
        and LOCATE(CONCAT(daa.province, daa.city), #{location} ) > 0
    </select>

    <select id="getDealerByLocationAndProvinceAndCityAndDistrict"
            resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*
        FROM t_fence_dealer_auth_area daa
        left join t_fence_dealer_auth da on daa.auth_id = da.id
        left join t_dealer_contract_rel cr on cr.contract_code = da.contract_code
        WHERE da.dealer_id != #{dealerId}
        and da.contract_status = 1
        and daa.province != ''
        <!-- and daa.city != '' 因为直辖市中，city是空串，所以需要去掉这个条件 -->
        and daa.district != ''
        and daa.source = 1
        and daa.is_delete = 0
        and cr.contract_type = #{fenceContractType}
        and LOCATE(CONCAT(daa.province, COALESCE(daa.city, ''), COALESCE(daa.district, '')), #{location}) > 0
        group by da.dealer_id
    </select>
    <select id="getJudgeArea" resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuthArea">
        SELECT * FROM (
             SELECT fdaa.province,fdaa.city,fdaa.district,fdaa.source,fdaa.id FROM t_fence_dealer_auth fda,t_fence_dealer_auth_area fdaa WHERE fda.id = fdaa.auth_id
             AND fda.is_delete=0 AND fdaa.is_delete=0 AND fda.contract_code= #{ro.contractCode}
             UNION all
             SELECT t.province,t.city,t.district,0 source,t.id FROM t_fence_dealer_blank_market t WHERE t.is_delete=0 and t.is_auth = 1
             AND t.contract_code=#{ro.contractCode}
        )tab ORDER BY CASE WHEN source =1 THEN 1 ELSE 0 END DESC
    </select>
    <!-- 1.已合作，已生效，已启用，2已合作，已生效，已停用，3未合作，已生效，已启用 ；
        contract_cooperation_status 合作状态：1已合作，2终止合作，3未合作（null也是未合作）
        contract_applystatus 	审批状态：1待提交审核、2审核中、3退回、4已作废、5待生成合同、6待经销商盖章、7待销售公司盖章、8待生效、9已生效、10失效、11已过期、12审核不通过、50待发起签署
        contract_status	 合同状态 0停用 1启用 2已过期 3作废
    -->
    <select id="selectCountByParam" resultType="java.lang.Integer">
        SELECT count(1) FROM t_fence_dealer_auth fda LEFT JOIN t_fence_dealer_auth_area fdaa ON fda.id = fdaa.auth_id
        LEFT JOIN t_dealer_contract_rel r ON fda.contract_code = r.contract_code
        WHERE fda.is_delete=0 AND fdaa.is_delete=0 and r.contract_type = #{ro.contractType} AND fdaa.province= #{ro.province}
        AND fdaa.city= #{ro.city}
        AND ((r.contract_cooperation_status = 1 and r.contract_applystatus = 9 and r.contract_status =1)
         or (r.contract_cooperation_status = 1 and r.contract_applystatus = 9 and r.contract_status =0)
        or (r.contract_cooperation_status = 3 and r.contract_applystatus = 9 and r.contract_status =1))
        <choose>
            <when test="ro.district != null and ro.district !=''">
                AND fdaa.district = #{ro.district}
            </when>
            <otherwise>
                AND fdaa.district IS NULL
            </otherwise>
        </choose>
    </select>
    <select id="getDealerByLocationAndProvinceAndCityAndDistrictIsEmpty"
            resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*
        FROM t_fence_dealer_auth_area daa
                 left join t_fence_dealer_auth da on daa.auth_id = da.id
                 left join t_dealer_contract_rel cr on cr.contract_code = da.contract_code
        WHERE da.dealer_id != #{dealerId}
          and da.contract_status = 1
          and daa.province != ''
          and daa.city != ''
          and COALESCE(daa.district,'') = ''
          and daa.source = 1
          and daa.is_delete = 0
          and cr.contract_type = #{fenceContractType}
          and LOCATE(CONCAT(daa.province, COALESCE(daa.city,''), COALESCE(daa.district,'')), #{location}) > 0
        group by da.dealer_id
    </select>
    <select id="getDealerByLocationAndProvinceAndCityIsEmpty"
            resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*
        FROM t_fence_dealer_auth_area daa
                 left join t_fence_dealer_auth da on daa.auth_id = da.id
                 left join t_dealer_contract_rel cr on cr.contract_code = da.contract_code
        WHERE da.dealer_id != #{dealerId}
          and da.contract_status = 1
          and daa.province != ''
          and COALESCE(daa.city,'') = ''
          and COALESCE(daa.district,'') = ''
          and daa.source = 1
          and daa.is_delete = 0
          and cr.contract_type = #{fenceContractType}
          and LOCATE(CONCAT(daa.province, COALESCE(daa.city,''), COALESCE(daa.district,'')), #{location}) > 0
        group by da.dealer_id
    </select>

    <select id="getDealerByProvinceAndCityAndDistrict"
            resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*, cr.contract_type
        FROM t_fence_dealer_auth_area daa
                 left join t_fence_dealer_auth da on daa.auth_id = da.id
                 left join t_dealer_contract_rel cr on cr.contract_code = da.contract_code
        WHERE da.dealer_id != #{dealerId}
          and da.contract_status = 1
          and daa.province != ''
          <!-- and daa.city != '' 因为直辖市中，city是空串，所以需要去掉这个条件 -->
          and daa.district != ''
          and daa.source = 1
          and daa.is_delete = 0
          and cr.contract_status = 1
          and cr.contract_applystatus = 9
          and cr.contract_cooperation_status = 1
          and LOCATE(CONCAT(daa.province, COALESCE(daa.city, ''), COALESCE(daa.district, '')), #{location}) > 0
        group by da.dealer_id
    </select>
    <select id="getDealerByProvinceAndCityAndDistrictIsEmpty"
            resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*, cr.contract_type
        FROM t_fence_dealer_auth_area daa
                 left join t_fence_dealer_auth da on daa.auth_id = da.id
                 left join t_dealer_contract_rel cr on cr.contract_code = da.contract_code
        WHERE da.dealer_id != #{dealerId}
          and da.contract_status = 1
          and daa.province != ''
          and daa.city != ''
          and COALESCE(daa.district,'') = ''
          and daa.source = 1
          and daa.is_delete = 0
          and cr.contract_status = 1
          and cr.contract_applystatus = 9
          and cr.contract_cooperation_status = 1
          and LOCATE(CONCAT(daa.province, COALESCE(daa.city,''), COALESCE(daa.district,'')), #{location}) > 0
        group by da.dealer_id
    </select>
    <select id="getDealerByProvinceAndCityIsEmpty"
            resultType="com.intelliquor.cloud.shop.common.model.FenceDealerAuth">
        SELECT da.*, cr.contract_type
        FROM t_fence_dealer_auth_area daa
                 left join t_fence_dealer_auth da on daa.auth_id = da.id
                 left join t_dealer_contract_rel cr on cr.contract_code = da.contract_code
        WHERE da.dealer_id != #{dealerId}
          and da.contract_status = 1
          and daa.province != ''
          and COALESCE(daa.city,'') = ''
          and COALESCE(daa.district,'') = ''
          and daa.source = 1
          and daa.is_delete = 0
          and cr.contract_status = 1
          and cr.contract_applystatus = 9
          and cr.contract_cooperation_status = 1
          and LOCATE(CONCAT(daa.province, COALESCE(daa.city,''), COALESCE(daa.district,'')), #{location}) > 0
        group by da.dealer_id
    </select>
</mapper>
