<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.AccountManagerReturnApplyRecordDao">

    <resultMap id="BaseResultMap" type="com.intelliquor.cloud.shop.common.model.AccountManagerReturnApplyRecord">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="shopId" column="shop_id" jdbcType="INTEGER"/>
            <result property="batchId" column="batch_id" jdbcType="VARCHAR"/>
            <result property="goodsCode" column="goods_code" jdbcType="VARCHAR"/>
            <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
            <result property="goodsNum" column="goods_num" jdbcType="INTEGER"/>
            <result property="returnReason" column="return_reason" jdbcType="VARCHAR"/>
            <result property="batchEvidence" column="batch_evidence" jdbcType="VARCHAR"/>
            <result property="returnCode" column="return_code" jdbcType="VARCHAR"/>
            <result property="verifyStatus" column="verify_status" jdbcType="TINYINT"/>
            <result property="verifyMsg" column="verify_msg" jdbcType="VARCHAR"/>
            <result property="verifyTime" column="verify_time" jdbcType="TIMESTAMP"/>
            <result property="verifyUser" column="verify_user" jdbcType="INTEGER"/>
            <result property="verifyUserName" column="verify_user_name" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="INTEGER"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="INTEGER"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
            <result property="companyId" column="company_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,shop_id,batch_id,
        goods_code,goods_name,goods_num,
        return_reason,batch_evidence,return_code,
        verify_status,verify_msg,verify_time,
        verify_user,verify_user_name,create_user,
        create_user_name,create_time,update_user,
        update_user_name,update_time,is_delete,
        company_id
    </sql>

    <select id="getList" resultType="com.intelliquor.cloud.shop.common.model.resp.AccountManagerReturnApplyRecordResp">
        select
            a.*,
            b.name as "shopName"
        from
            t_account_manager_return_apply_record a
        left join
            t_member_shop b
        on
            a.shop_id = b.id
        where
            a.is_delete = 0
        <if test="createUser != null">
            and create_user = #{createUser}
        </if>
        <if test="shopName != null and shopName != ''">
            and b.name like concat('%',#{shopName},'%')
        </if>
        <if test="verifyStatus != null">
            and a.verify_status = #{verifyStatus}
        </if>
        <if test="startDate != null">
            and a.create_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and a.create_time &lt;= #{endDate}
        </if>
        order by a.create_time desc
    </select>

    <select id="getDetailById"
            resultType="com.intelliquor.cloud.shop.common.model.resp.AccountManagerReturnApplyRecordResp">
        select * from t_account_manager_return_apply_record where id = #{id} and is_delete = 0
    </select>
</mapper>
