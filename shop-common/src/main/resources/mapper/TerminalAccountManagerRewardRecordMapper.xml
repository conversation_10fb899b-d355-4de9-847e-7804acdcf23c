<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.TerminalAccountManagerRewardRecordDao">

    <select id="selectTerminalAccountManagerRewardRecordList" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalAccountManagerRewardRecordResp">
        select
        tamrr.id,
        tamrr.account_id,
        tamrr.type,
        tamrr.name,
        tamrr.phone,
        tp.post_name,
        tamrr.shop_id,
        ms.name as shop_name,
        tamrr.wechat_name,
        tamrr.open_id,
        tamrr.goods_name,
        tamrr.goods_code,
        tamrr.scan_lottery_time,
        tamrr.reward_number,
        tamrr.create_time,
        tamrr.update_time
        from
        t_terminal_account_manager_reward_record tamrr
        left join t_terminal_account_manager tam
        on tamrr.account_id = tam.id
        left join t_terminal_post tp
        on tam.post_id = tp.id
        left join t_member_shop ms
        on tamrr.shop_id = ms.id
        <where>
            <if test="phoneOrName != null and phoneOrName != ''">
               and (
                tamrr.name like concat('%',#{phoneOrName},'%')
                or
                tamrr.phone like concat('%',#{phoneOrName},'%')
                )
            </if>
            <if test="startTime != null">
               and tamrr.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and tamrr.create_time &lt;= #{endTime}
            </if>
        </where>
        order by tamrr.id desc
    </select>
</mapper>
