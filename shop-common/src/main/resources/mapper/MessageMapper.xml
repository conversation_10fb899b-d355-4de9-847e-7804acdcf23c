<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.common.dao.MessageDao">

    <sql id="baseColumn">
        id,
        phone,
        code,
        content,
        type,
        send_time,
        remark
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.common.model.MessageModel">
        <id property="id" column="id"/>
        <result property="phone" column="phone"/>
        <result property="code" column="code"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="sendTime" column="send_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_message
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.common.model.MessageModel">
        INSERT INTO t_system_message(
            phone,
            code,
            content,
            type,
            send_time,
            remark
        )VALUES(
            #{phone},
            #{code},
            #{content},
            #{type},
            #{sendTime},
            #{remark}
        )
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.common.model.MessageModel">
        UPDATE t_system_message
        <set>
            <if test="phone != null and phone !=''  ">
                phone = #{phone},
            </if>
            <if test="code != null and code !=''  ">
                code = #{code},
            </if>
            <if test="content != null and content !=''  ">
                content = #{content},
            </if>
            <if test="type != null and type !=''  ">
                type = #{type},
            </if>
            <if test="sendTime != null and sendTime !=''  ">
                send_time = #{sendTime},
            </if>
            <if test="remark != null and remark !=''  ">
                remark = #{remark},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_system_message
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_message
        WHERE id = #{id}
    </select>

    <select id="countMessageByPhone" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            t_system_message
        WHERE
            phone = #{phone}
        AND date(send_time) = curdate()

    </select>

    <select id="getLastMessageSendMinuteByPhone" resultType="java.util.Map">
        SELECT
            code,
            TIMESTAMPDIFF(MINUTE, send_time, NOW()) send_minute
        FROM
            `t_system_message`
        WHERE
            phone = #{phone}
        ORDER BY
            id DESC
        LIMIT 1
    </select>
</mapper>