<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.CheckRuleSettingMapper">

    <select id="findRuleActionCheckRuleSettingListByProdcut"
            resultType="com.intelliquor.cloud.shop.common.model.CheckRuleSettingProductRelationModel">
        SELECT
        relation.*
        FROM
        t_check_rule_setting_product_relation relation
        LEFT JOIN
        t_check_rule_setting setting
        ON
        relation.chek_rule_setting_id = setting.id
        WHERE
        setting.rule_action = #{ruleAction}
        AND relation.product_code IN
        <foreach collection="productCodes" item="productCode"  open="(" separator="," close=")">
            #{productCode}
        </foreach>
        AND setting.del_flag = FALSE
        AND relation.del_flag = FALSE
    </select>

    <select id="getCheckRuleByGoodsCode" resultType="com.intelliquor.cloud.shop.common.model.CheckRuleSettingModel">
        SELECT t1.* FROM t_check_rule_setting t1 LEFT JOIN t_check_rule_setting_product_relation t2 ON  t1.id = t2.chek_rule_setting_id
        WHERE t2.product_code = #{goodsCode} AND t1.rule_action = #{ruleAction} AND t1.del_flag = FALSE AND t2.del_flag = FALSE
    </select>
</mapper>

