# 业务需求分析Prompt使用指南

## 使用说明

### 1. 如何使用此Prompt
1. **复制完整的Prompt模板**：将`business_analysis_prompt.md`中的内容完整复制
2. **添加具体需求**：在最后的"请在下方提供您的具体业务需求"部分，粘贴您的业务需求文档
3. **发送给LLM**：将完整的内容发送给ChatGPT、Claude或其他大语言模型
4. **获取分析结果**：LLM会按照框架对您的需求进行全面分析

### 2. 适用场景
- ✅ 新功能开发前的需求分析
- ✅ 现有系统的优化改进
- ✅ 业务流程梳理和重构
- ✅ 技术方案设计前的准备
- ✅ 跨部门协作的需求对齐

### 3. 输入需求的建议格式
为了获得更好的分析效果，建议您的业务需求包含以下信息：

```
## 业务背景
[描述当前业务现状和遇到的问题]

## 具体需求
[详细描述要实现的功能或解决的问题]

## 业务目标
[希望通过这个需求达到什么目标]

## 约束条件
[技术约束、时间约束、资源约束等]

## 相关干系人
[涉及的用户角色和业务方]
```

### 4. 预期输出示例
使用此Prompt后，您将获得类似以下结构的详细分析：

```
# 业务需求分析报告

## 1. 需求理解与澄清
- 核心目标：[明确的问题定义]
- 业务价值：[量化的价值描述]
- 关键利益相关者：[角色清单]
- 需求背景：[背景分析]

## 2. 业务场景分析
- 主要业务场景：[场景列表]
- 异常场景：[异常情况]
- 并发场景：[并发分析]
- 数据一致性要求：[一致性要求]

...（其他7个部分的详细分析）
```

### 5. 使用技巧

#### 5.1 针对不同类型需求的调整
- **营销活动类需求**：重点关注防刷、限额、时效性
- **数据统计类需求**：重点关注实时性、准确性、性能
- **用户体验类需求**：重点关注交互流程、响应时间
- **系统集成类需求**：重点关注接口设计、异常处理

#### 5.2 获得更精准分析的方法
1. **提供完整信息**：尽可能详细地描述需求背景和期望
2. **明确约束条件**：说明技术栈、时间、资源限制
3. **提供现状描述**：说明当前系统的情况
4. **指出特殊要求**：如果有特殊的性能、安全要求要明确说明

### 6. 后续应用

#### 6.1 基于分析结果的下一步行动
- **技术方案设计**：根据分析结果设计具体的技术方案
- **项目计划制定**：基于开发计划建议制定详细的项目计划
- **风险管理**：针对识别的风险制定具体的应对措施
- **团队沟通**：使用分析结果与开发团队、业务团队对齐理解

#### 6.2 与现有开发流程的结合
1. **需求评审阶段**：使用分析结果进行更深入的需求评审
2. **技术设计阶段**：基于分析结果进行详细技术设计
3. **开发阶段**：参考业务流程梳理进行开发
4. **测试阶段**：基于识别的风险和异常场景设计测试用例

### 7. 常见问题解答

**Q: 如果需求文档很简单，这个Prompt还有用吗？**
A: 是的。即使需求文档简单，这个Prompt也能帮助您挖掘潜在的问题和考虑点，避免开发过程中的返工。

**Q: 分析结果太详细了，如何筛选重点？**
A: 建议重点关注第3、4、6、7部分（业务流程、数据分析、风险识别、实现建议），这些直接影响开发工作。

**Q: 可以针对特定场景定制Prompt吗？**
A: 可以。您可以根据具体的业务领域调整"特别关注点"部分，添加行业特有的考虑因素。

### 8. 版本控制建议
- 建议将此Prompt作为团队的标准工具
- 根据团队经验不断优化和完善
- 为不同类型的项目创建定制版本
- 定期回顾和更新，确保与技术栈和业务需求保持同步

---

## 快速开始模板

如果您现在就要使用，可以直接复制以下内容发送给LLM：

```
[这里复制business_analysis_prompt.md的完整内容]

## 我的业务需求：
[在此处详细描述您的具体业务需求]
``` 