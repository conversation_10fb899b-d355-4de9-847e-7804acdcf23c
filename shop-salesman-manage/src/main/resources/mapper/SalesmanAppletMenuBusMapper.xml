<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.salesmanManage.dao.SalesmanAppletMenuBusDao">

    <sql id="baseColumn">
        id,
        salesman_menu_id,
        company_id,
        type
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.salesmanManage.model.SalesmanAppletMenuBusModel">
        <id     property="id"               column="id"/>
        <result property="salesmanMenuId"   column="salesman_menu_id"/>
        <result property="companyId"        column="company_id"/>
        <result property="type"             column="type"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
        </where>
    </sql>

    <!-- 查询列表 -->
    <select id="queryList" resultMap="baseResultMap">
        SELECT
            <include refid="baseColumn"></include>
        FROM
            t_salesman_applet_menu_bus tsamc
        <include refid="selectiveWhere"/>
    </select>

    <!-- 根据id获取 -->
    <select id="findById" resultMap="baseResultMap">
        SELECT
            <include refid="baseColumn"></include>
        FROM
            t_salesman_applet_menu_bus tsamc
        WHERE id = #{id}
    </select>

    <!-- 批量添加 -->
    <insert id="insertBatch">
        INSERT INTO t_salesman_applet_menu_bus (salesman_menu_id, company_id, type)
        values
        <foreach collection="menuIds" item="menuId" separator=",">
            (#{menuId}, #{companyId}, #{type})
        </foreach>
    </insert>

    <!-- 根据商户id删除 -->
    <delete id="deleteByCompanyId">
        delete from t_salesman_applet_menu_bus where company_id = #{companyId} and type = #{type}
    </delete>

</mapper>