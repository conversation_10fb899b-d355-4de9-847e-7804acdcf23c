<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.salesmanManage.dao.AppSettingDao">

    <sql id="baseColumn">
        id,
        app_name,
        app_id,
        app_secret,
        company_id
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.salesmanManage.model.AppSettingModel">
        <id property="id" column="id"/>
        <result property="appName" column="app_name"/>
        <result property="appId" column="app_id"/>
        <result property="appSecret" column="app_secret"/>
        <result property="companyId" column="company_id"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="appName != null ">
                and app_name = #{appName}
            </if>
            <if test="companyId != null ">
                and company_id = #{companyId}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_app_setting
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.salesmanManage.model.AppSettingModel">
        INSERT INTO t_system_app_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appName != null ">
                app_name,
            </if>
            <if test="appId != null ">
                app_id,
            </if>
            <if test="appSecret != null ">
                app_secret,
            </if>
            <if test="companyId != null ">
                company_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appName != null ">
                #{appName},
            </if>
            <if test="appId != null ">
                #{appId},
            </if>
            <if test="appSecret != null ">
                #{appSecret},
            </if>
            <if test="companyId != null ">
                #{companyId}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.salesmanManage.model.AppSettingModel">
        UPDATE t_system_app_setting
        <set>
            <if test="appName != null ">
                app_name = #{appName},
            </if>
            <if test="appId != null ">
                app_id = #{appId},
            </if>
            <if test="appSecret != null ">
                app_secret = #{appSecret},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_system_app_setting
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_app_setting
        WHERE id = #{id}
    </select>

    <select id="getByName" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_app_setting
        WHERE app_name = #{name} and company_id = #{companyId}
    </select>

</mapper>