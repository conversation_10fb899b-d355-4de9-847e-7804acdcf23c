<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.salesmanManage.dao.DealerActivityDao">

    <sql id="baseColumn">
        id,
        name,
        company_id,
        type,
        start_time,
        end_time,
        status,
        remark,
        enable_factory_send,
        factory_send_start_time,
        factory_send_end_time,
        reward_scope,
        dealer_type_id_str,
        reward_received_type,
        account_days,
        enable_reward_dealer_scan_input,
        dealer_scan_input_bottle,
        dealer_scan_input_reward_type,
        dealer_scan_input_reward_num,
        enable_reward_distributor_scan_input,
        distributor_scan_input_bottle,
        distributor_scan_input_reward_type,
        distributor_scan_input_reward_num,
        enable_reward_dealer_scan_output,
        dealer_scan_output_bottle,
        dealer_scan_output_reward_type,
        dealer_scan_output_reward_num,
        enable_reward_distributor_scan_output,
        distributor_scan_output_bottle,
        distributor_scan_output_reward_type,
        distributor_scan_output_reward_num,
        create_time,
        is_delete,
        enable_status,
        code,
        enable_waiter_scan_reward,
        waiter_scan_bottle,
        waiter_scan_reward_type,
        waiter_scan_reward_reward_num
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.salesmanManage.model.DealerActivityModel">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="companyId" column="company_id"/>
        <result property="type" column="type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="enableFactorySend" column="enable_factory_send"/>
        <result property="factorySendStartTime" column="factory_send_start_time"/>
        <result property="factorySendEndTime" column="factory_send_end_time"/>
        <result property="rewardScope" column="reward_scope"/>
        <result property="dealerTypeIdStr" column="dealer_type_id_str"/>
        <result property="rewardReceivedType" column="reward_received_type"/>
        <result property="accountDays" column="account_days"/>
        <result property="enableRewardDealerScanInput" column="enable_reward_dealer_scan_input"/>
        <result property="dealerScanInputBottle" column="dealer_scan_input_bottle"/>
        <result property="dealerScanInputRewardType" column="dealer_scan_input_reward_type"/>
        <result property="dealerScanInputRewardNum" column="dealer_scan_input_reward_num"/>
        <result property="enableRewardDistributorScanInput" column="enable_reward_distributor_scan_input"/>
        <result property="distributorScanInputBottle" column="distributor_scan_input_bottle"/>
        <result property="distributorScanInputRewardType" column="distributor_scan_input_reward_type"/>
        <result property="distributorScanInputRewardNum" column="distributor_scan_input_reward_num"/>
        <result property="enableRewardDealerScanOutput" column="enable_reward_dealer_scan_output"/>
        <result property="dealerScanOutputBottle" column="dealer_scan_output_bottle"/>
        <result property="dealerScanOutputRewardType" column="dealer_scan_output_reward_type"/>
        <result property="dealerScanOutputRewardNum" column="dealer_scan_output_reward_num"/>
        <result property="enableRewardDistributorScanOutput" column="enable_reward_distributor_scan_output"/>
        <result property="distributorScanOutputBottle" column="distributor_scan_output_bottle"/>
        <result property="distributorScanOutputRewardType" column="distributor_scan_output_reward_type"/>
        <result property="distributorScanOutputRewardNum" column="distributor_scan_output_reward_num"/>
        <result property="createTime" column="create_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="code" column="code"/>
        <result property="enableWaiterScanReward" column="enable_waiter_scan_reward"/>
        <result property="waiterScanBottle" column="waiter_scan_bottle"/>
        <result property="waiterScanRewardType" column="waiter_scan_reward_type"/>
        <result property="waiterScanRewardNum" column="waiter_scan_reward_num"/>
    </resultMap>

    <!--筛选活动-->
    <select id="screenActivity" resultMap="baseResultMap" >
        select
        DISTINCT a.*
        from t_cloud_dealer_activity  a
        left join t_cloud_dealer_activity_goods g on g.activity_id = a.id
       <where>
         a.is_delete =1 and a.status = 1 and a.enable_status = 1
        <if test="activityTime !=null ">
          and a.start_time &lt;= #{activityTime} and a.end_time &gt;= #{activityTime}
        </if>
        <if test="goodCode !=null and goodCode!='' " >
            and g.goods_code = #{goodCode}
        </if>
        <if test="outTime!=null ">
            and a.factory_send_start_time &lt;= #{outTime} and a.factory_send_end_time &gt;= #{outTime}
        </if>
       <if test="enableWaiterScanReward != null">
           and a.enable_waiter_scan_reward = #{enableWaiterScanReward}
       </if>
        </where>
        order by a.create_time desc
    </select>

    <!--业务区域过滤-->
    <select id="filterActivityByArea" resultType="int">
        select  count(*) from  t_cloud_dealer_activity_bus_type b
        where b.activity_id = #{activityId}
        and b.bus_area_list = #{dealerArea}
    </select>

    <!--业务区域过滤-->
    <select id="filterActivityByDealerCode" resultType="int">
        select  count(*) from  t_cloud_dealer_activity_appoint_dealer b
        where b.activity_id = #{activityId}
        and b.dealer_code = #{dealerCode}
    </select>
</mapper>