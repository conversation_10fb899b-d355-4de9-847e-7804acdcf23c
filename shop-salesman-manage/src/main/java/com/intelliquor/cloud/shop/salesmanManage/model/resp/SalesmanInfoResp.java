package com.intelliquor.cloud.shop.salesmanManage.model.resp;

import com.intelliquor.cloud.shop.salesmanManage.model.CloudDealerStaffModel;
import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanStaffModel;
import lombok.Data;

/**
 * @Auther: tianms
 * @Date: 2020/12/17 09:11
 * @Description:
 */
@Data
public class SalesmanInfoResp extends SalesmanStaffModel {

    public SalesmanInfoResp() {
    }

    /**
     * 登陆成功签名
     */
    private String authLocal;

    /**
     * 当前登录员工信息
     */
    private CloudDealerStaffModel salesmanStaffModel;

    /**
     * 总提报次数
     */
    private Integer totalReportNum;

    /**
     * 提报审批中数量
     */
    private Integer reportExaminingNum;

}
