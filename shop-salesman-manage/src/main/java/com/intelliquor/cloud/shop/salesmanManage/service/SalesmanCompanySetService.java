package com.intelliquor.cloud.shop.salesmanManage.service;


import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanCompanySetModel;
import com.intelliquor.cloud.shop.salesmanManage.util.MapUtils;

/**
 * @Auther: tianms
 * @Date: 2020/12/17 10:32
 * @Description: 云商公司信息配置接口
 */
public interface SalesmanCompanySetService {

    /**
     * 功能描述: 根据公司id获取配置详情
     *
     * @param companyId
     * @return com.intelliquor.cloud.shop.dealer.model.DealerCompanySetModel
     * @auther: tms
     * @date: 2020/12/17 10:36
     */
    SalesmanCompanySetModel getByCompanyId(Integer companyId);

    /**
     * 功能描述: 根据条件获取配置详情
     *
     * @param param
     * @return com.intelliquor.cloud.shop.dealer.model.DealerCompanySetModel
     * @auther: tms
     * @date: 2020/12/17 10:36
     */
    SalesmanCompanySetModel getByParam(MapUtils param);

    /**
     * 功能描述: 根据小程序获取配置详情
     *
     * @param appId 小程序id
     * @return com.intelliquor.cloud.shop.dealer.model.DealerCompanySetModel
     * @auther: tms
     * @date: 2020/12/17 10:36
     */
    SalesmanCompanySetModel getByAppId(String appId);

}
