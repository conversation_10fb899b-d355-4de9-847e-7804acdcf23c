package com.intelliquor.cloud.shop.salesmanManage.dao;
import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanSavourApplyRecordModel;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanEvaluationWineResp;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanSavourApplyRecordDoneeResp;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanSavourPartyResp;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanSavourRecordByCustomerResp;

import java.util.Map;
import java.util.List;
/**
* 描述：品鉴酒/品鉴会申请表 Dao接口
* <AUTHOR>
* @date 2021-04-14
*/
public interface SalesmanSavourApplyRecordDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<SalesmanSavourApplyRecordModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(SalesmanSavourApplyRecordModel model);

    /**
     * 新增品鉴酒数据
     *
     * @param model
     */
    Integer insertDone(SalesmanSavourApplyRecordDoneeResp model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(SalesmanSavourApplyRecordModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    SalesmanSavourApplyRecordModel getById(Integer id);

    /**
     * 查询品鉴会数据-后台
     * @return
     */
    List<SalesmanSavourPartyResp> selectPartyList(Map<String, Object> searchMap);

    /**
     * 查询品鉴会数据-小程序
     * @return
     */
    List<SalesmanSavourPartyResp> selectPartyList4App(Map<String, Object> searchMap);

    /**
     * 获取评鉴酒审批列表
     *
     * @param searchMap
     * @return
     */
    List<SalesmanEvaluationWineResp> getEvaluationWineList(Map<String, Object> searchMap);

    /**
     * 获取评鉴酒审批详情
     *
     * @param applyType
     * @param applyId
     * @param companyId
     * @return
     */
    SalesmanEvaluationWineResp getEvaluationWineInfo(Integer applyType , Integer applyId, Integer companyId);

    List<SalesmanSavourRecordByCustomerResp> getByCustomer(String phone);
}