package com.intelliquor.cloud.shop.salesmanManage.model.open;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: tianms
 * @Date: 2021/02/02 10:59
 * @Description: 对外经销商实体类
 */
@Data
public class DealerOpenModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "经销商编号")
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "营业执照号")
    private String businessLicenseNum;

    @ApiModelProperty(value = "法人")
    private String corporationLegalPerson;

    @ApiModelProperty(value = "法人身份证号")
    private String legalPersonCardNo;

}
