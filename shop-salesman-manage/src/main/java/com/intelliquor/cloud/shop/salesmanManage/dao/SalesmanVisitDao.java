package com.intelliquor.cloud.shop.salesmanManage.dao;

import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanVisitModel;
import com.intelliquor.cloud.shop.salesmanManage.model.VisitListModel;
import com.intelliquor.cloud.shop.salesmanManage.util.MapUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SalesmanVisitDao {


    List<VisitListModel> getPlanListById(SalesmanVisitModel salesmanVisitModel);

    List<VisitListModel> getList(SalesmanVisitModel salesmanVisitModel);

    Date getLastVisitTimeById(Map<String, Object> param);

    VisitListModel getById(SalesmanVisitModel param);
}
