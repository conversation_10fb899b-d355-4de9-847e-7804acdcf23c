package com.intelliquor.cloud.shop.salesmanManage.model.resp;

import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanSavourFeeVerifyRecordModel;
import lombok.Data;

@Data
public class SalesmanSavourFeeVerifyRecordResp extends SalesmanSavourFeeVerifyRecordModel {

    /**
     * 业务员名称
     */
    private String staffName;

    /**
     * 业务员手机号
     */
    private String staffPhone;

    /**
     * 经销商Id
     */
    private Integer dealerId;
    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 此次费用核销对应申请的酒的数量
     */
    private Integer num;

    private String statusStr;
}
