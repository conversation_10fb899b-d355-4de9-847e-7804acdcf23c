package com.intelliquor.cloud.shop.salesmanManage.dao;


import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanShopReportRecordModel;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanShopReportRecordResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Auther: tianms
 * @Date: 2021/03/24 17:20
 * @Description: 终端店提报记录-dao
 */
public interface SalesmanShopReportRecordDao {

    /**
     * 查询列表
     *
     * @param param
     * @return java.util.List<com.intelliquor.cloud.shop.system.model.SalesmanShopReportRecordResp>
     * @auther: tms
     * @date: 2021/03/24 17:33
     */
    List<SalesmanShopReportRecordResp> queryList(Map<String, Object> param);

    /**
     * 根据id获取详情
     *
     * @param id
     * @return com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanShopReportRecordResp
     * @auther: tms
     * @date: 2021/03/24 18:17
     */
    SalesmanShopReportRecordResp getById(@Param("id") Integer id);

    /**
     * 新增
     *
     * @param salesmanShopReportRecordModel 终端提报信息
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/03/24 18:10
     */
    Integer insert(SalesmanShopReportRecordModel salesmanShopReportRecordModel);

    /**
     * 修改
     *
     * @param salesmanShopReportRecordModel 终端提报信息
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/03/24 18:10
     */
    Integer update(SalesmanShopReportRecordModel salesmanShopReportRecordModel);


}
