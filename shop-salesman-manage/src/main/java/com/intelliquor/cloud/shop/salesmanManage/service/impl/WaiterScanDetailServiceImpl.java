package com.intelliquor.cloud.shop.salesmanManage.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.CodeUtil;
import com.intelliquor.cloud.shop.common.utils.TaiBaoCodeDecryptUtil;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.salesmanManage.dao.*;
import com.intelliquor.cloud.shop.salesmanManage.model.CloudDealerInfoModel;
import com.intelliquor.cloud.shop.salesmanManage.model.DealerActivityModel;
import com.intelliquor.cloud.shop.salesmanManage.model.WaiterIncomeBalanceModel;
import com.intelliquor.cloud.shop.salesmanManage.model.WaiterScanDetailModel;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.WaiterRecordResp;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.WaiterScanDetailResp;
import com.intelliquor.cloud.shop.salesmanManage.service.WaiterScanDetailService;
import com.intelliquor.cloud.shop.salesmanManage.service.WebCodeComponent;
import com.intelliquor.cloud.shop.salesmanManage.util.constants.StatusConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 描述：服务员扫码明细表 服务实现层
 *
 * <AUTHOR>
 * @date 2021-05-12
 */
@Slf4j
@Service
public class WaiterScanDetailServiceImpl implements WaiterScanDetailService {

    @Autowired
    private WaiterScanDetailDao waiterScanDetailDao;

    @Value("${lyt-company.id:-1}")
    private Integer lytCompanyId;

    @Value("${jinsha-company.id:-1}")
    private Integer jsCompanyId;

    @Autowired
    WebCodeComponent webCodeComponent;

    @Autowired
    private DealerActivityDao dealerActivityDao;

    @Autowired
    private CloudDealerDao cloudDealerDao;

    @Autowired
    private WaiterIncomeBalanceDao waiterIncomeBalanceDao;

    @Autowired
    private WaiterRecordDao waiterRecordDao;

    /**
     * 查询数据
     *
     * @return
     */
    @Override
    public List<WaiterScanDetailModel> selectList(Map<String, Object> searchMap) {
        return waiterScanDetailDao.selectList(searchMap);
    }


    /**
     * 新增数据
     *
     * @param model
     */
    @Override
    public void insert(WaiterScanDetailModel model) {
        waiterScanDetailDao.insert(model);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    @Override
    public void update(WaiterScanDetailModel model) {
        waiterScanDetailDao.update(model);
    }

    /**
     * 删除数据
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        waiterScanDetailDao.delete(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    @Override
    public WaiterScanDetailModel getById(Integer id) {
        return waiterScanDetailDao.getById(id);
    }

    @Override
    public List<WaiterScanDetailResp> selectListByCondition(Map<String, Object> searchMap) {
        return waiterScanDetailDao.selectListByCondition(searchMap);
    }

    /**
     * 服务员扫码
     *
     * @param model
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<WaiterScanDetailModel> scan(WaiterScanDetailModel model) {
        //交易服务员有效性
        WaiterRecordResp waiter = waiterRecordDao.findById(model.getWaiterId());
        if(waiter == null || waiter.getIsDelete() == 0 || waiter.getStatus() == 0){
            return Response.fail("服务员无效");
        }
        String originQrcode = model.getOriginalQrcode();
        Integer companyId = model.getCompanyId();
        String qrcode = model.getOriginalQrcode();
        //解析码
        if (companyId.equals(lytCompanyId)) {
            log.info("琅琊台码：{}", originQrcode);
            if (!CodeUtil.matchingCodeJJ(originQrcode)) {
                qrcode = qrcode.substring(qrcode.lastIndexOf("/") + 1);
                qrcode = TaiBaoCodeDecryptUtil.decrypt(qrcode);
                log.info("琅琊台码解析：{}", qrcode);
                if (StringUtils.isEmpty(qrcode)) {
                    return Response.fail("码解析失败");
                }
            }
        } else if (companyId.equals(jsCompanyId)) {
            log.info("金沙码：{}", originQrcode);
            qrcode = CodeUtil.matchingCodeJS(originQrcode);
            log.info("金沙码解析：{}", qrcode);
            if (StringUtils.isEmpty(qrcode)) {
                return Response.fail("码解析失败");
            }
        }
        model.setQrcode(qrcode);
        //校验码是否被扫
        Map<String, Object> param = Maps.newHashMap();
        param.put("companyId", companyId);
        param.put("qrcode", model.getQrcode());
        List<WaiterScanDetailModel> oldRecordList = waiterScanDetailDao.selectList(param);
        if (CollectionUtils.isNotEmpty(oldRecordList)) {
            return Response.fail("该码已被扫，不可重复扫码");
        }
        //查询码数据
        Map<String, String> codeInfoMap = getQrcodeInfo(qrcode, companyId);
        if (MapUtils.isEmpty(codeInfoMap)) {
            return Response.fail("查询码信息失败");
        }
        String codeType = codeInfoMap.get("codeType");
        if (!codeType.equals("0")) {
            return Response.fail("请扫描瓶盖内码");
        }
        //产品编码
        String goodsCode = codeInfoMap.get("WBProductNo");
        if (StringUtils.isBlank(goodsCode)) {
            return Response.fail("未查询到码对应的商品信息");
        }
        log.info("商品编码：{}", goodsCode);
        String goodsName = codeInfoMap.get("ProductName");
        String dealerCode = codeInfoMap.get("WBCustomerNo");
        String dealerName = codeInfoMap.get("CustomerName");
        model.setGoodsCode(goodsCode);
        model.setGoodsName(goodsName);
        model.setDealerCode(dealerCode);
        model.setDealerName(dealerName);
        model.setNum(1);
        try {
            if (codeInfoMap.get("OutTime") != null) {
                model.setOutTime(TimeUtilis.getDateByForm(TimeUtilis.dateFormat(codeInfoMap.get("OutTime"), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
            }
        }catch (Exception e){
            log.error("出库时间：",codeInfoMap.get("OutTime"));
            log.error("出库时间格式不正确时不能影响整体扫码进度，",e);
        }
        List<DealerActivityModel> activityModelList = screenActivity(model);
        if(CollectionUtils.isEmpty(activityModelList)){
            return Response.fail("未匹配到奖励活动");
        }

     /*   if(!Objects.equals(waiter.getDealerId() , model.getDealerId())){
            throw new BusinessException("服务员所属经销商与商品所属经销商不一致");
        }*/
        log.info("匹配的活动信息:{}", JSON.toJSONString(activityModelList));
        DealerActivityModel activityModel = activityModelList.get(0);
        if(activityModel.getWaiterScanRewardType() == 1){
            //红包
            BigDecimal amount = activityModel.getWaiterScanRewardNum();
            model.setAmount(amount);
        }else{
            return Response.fail("服务员扫码仅支持红包奖励");
        }
        String transaction = "WS" + System.currentTimeMillis() + (int) ((Math.random() * 9 + 1) * 1000);
        model.setTransaction(transaction);
        int receivedType = activityModel.getRewardReceivedType();
        if(receivedType == 2){
            //立即到账
            model.setGainedTime(new Date());
            model.setGainedStatus(1);
            waiterScanDetailDao.insert(model);
            WaiterIncomeBalanceModel balanceModel = WaiterIncomeBalanceModel.builder()
                    .transaction(transaction)
                    .scanId(model.getId())
                    .amount(model.getAmount())
                    .waiterId(model.getWaiterId())
                    .earningType(1)
                    .companyId(companyId)
                    .build();
            waiterIncomeBalanceDao.insert(balanceModel);
            //增加服务员金额
            waiterRecordDao.addAmount(model.getWaiterId(),model.getAmount());
        }else{
            Date delay = TimeUtilis.setStartDay(TimeUtilis.addDays(new Date(), activityModel.getAccountDays()));
            model.setGainedTime(delay);
            model.setGainedStatus(0);
            waiterScanDetailDao.insert(model);
        }
        waiterRecordDao.addScanNum(model.getWaiterId(),1);
        return Response.ok(model);
    }

    /**
     * 获取码信息
     *
     * @param qrcode
     * @param companyId
     * @return
     */
    private Map<String, String> getQrcodeInfo(String qrcode, Integer companyId) {
        //查询琅琊台码信息
        Map<String, String> map = Maps.newHashMap();
        if (StringUtils.isNotEmpty(qrcode)) {
            try {
                map = webCodeComponent.getCodeInfo(qrcode, companyId);
            } catch (Exception e) {
                throw new BusinessException("查询码信息失败", e);
            }
            log.info("琅琊台解析出来的数据：{}", map);
        }
        return map;
    }

    public List<DealerActivityModel> screenActivity(WaiterScanDetailModel detailModel) {
        List<DealerActivityModel> activityModels = new ArrayList<>();
        // 先进行匹配活动时间和商品和出库时间
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("activityTime", new Date());
        searchMap.put("goodCode", detailModel.getGoodsCode());
        searchMap.put("enableWaiterScanReward",1);//启用服务员扫码奖励
        // 查询活动
        List<DealerActivityModel> dealerActivityModels = dealerActivityDao.screenActivity(searchMap);
        if (org.springframework.util.CollectionUtils.isEmpty(dealerActivityModels)) {
            return null;
        }
        log.info("dealerActivityModels: {}", JSON.toJSONString(dealerActivityModels));
        // 查询商品经销商信息
        CloudDealerInfoModel dealerInfoModel = cloudDealerDao.getByDealerCodeAndCompanyId(detailModel.getDealerCode(), detailModel.getCompanyId());
        if (dealerInfoModel == null) {
            throw new BusinessException("进货经销商查询系统为空");
        }
        for (DealerActivityModel activityModel : dealerActivityModels) {
            // 如果开启了厂家发货时间，比对时间
            if (StatusConstant.ENABLE_DISABLE.ENABLE.equals(activityModel.getEnableFactorySend())) {
                if (activityModel.getFactorySendStartTime().compareTo(detailModel.getOutTime()) > 0
                        && activityModel.getFactorySendEndTime().compareTo(detailModel.getOutTime()) < 0) {
                    continue;
                }
            }
            detailModel.setDealerId(dealerInfoModel.getId());
            Integer dealerType = dealerInfoModel.getDealerType();
            // 全国
            if (activityModel.getRewardScope().equals("all")) {
                if (dealerType != null && !org.springframework.util.StringUtils.isEmpty(activityModel.getDealerTypeIdStr())) {
                    String[] a = activityModel.getDealerTypeIdStr().split(",");
                    if (Arrays.asList(a).contains(String.valueOf(dealerType))) {
                        activityModels.add(activityModel);
                    }
                } else {
                    activityModels.add(activityModel);
                }
                // 业务区域
            } else if (activityModel.getRewardScope().equals("bus_type")) {
                int count = 0;
                if (dealerInfoModel.getDealerArea() != null) {
                    count = dealerActivityDao.filterActivityByArea(activityModel.getId(), dealerInfoModel.getDealerArea());
                }
                if (count > 0 && dealerType != null && !org.springframework.util.StringUtils.isEmpty(activityModel.getDealerTypeIdStr())) {
                    String[] a = activityModel.getDealerTypeIdStr().split(",");
                    if (Arrays.asList(a).contains(String.valueOf(dealerType))) {
                        activityModels.add(activityModel);
                    }
                } else if (count > 0 && dealerType != null && org.springframework.util.StringUtils.isEmpty(activityModel.getDealerTypeIdStr())) {
                    activityModels.add(activityModel);
                }
                // 指定经销商
            } else {
                int count = dealerActivityDao.filterActivityByDealerCode(activityModel.getId(), dealerInfoModel.getDealerCode());
                if (count > 0) {
                    activityModels.add(activityModel);
                }
            }
        }
        return activityModels;
    }

    @Override
    public BigDecimal sumAmount(WaiterScanDetailModel model) {
        return waiterScanDetailDao.sumAmount(model);
    }

    /**
     * 获取数据条数
     *
     *
     * @param search
     * @auther: tms
     * @date: 2021/06/25 10:30
     * @return java.lang.Integer
     */
    @Override
    public Integer countListByCondition(Map<String, Object> search) {
        return waiterScanDetailDao.countListByCondition(search);
    }
}