package com.intelliquor.cloud.shop.salesmanManage.model.resp;

import lombok.Data;

/**
 * 终端基础信息返回实体
 * <AUTHOR>
 * @createTime 2021/3/18
 * @description
 */
@Data
public class ShopResp {
    /**
     *终端店名称
     */

    private String shopName;
    /**
     *终端店id
     */
    private String  shopId;

    /**
     * 最后拜访时间
     */
    private String lastVisitTime ;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String linkphone;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 门头照地址
     */
    private String storesImg;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;
    private String dealerCode;
    private String dealerName;
}
