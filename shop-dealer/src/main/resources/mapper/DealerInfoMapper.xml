<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.dealer.dao.DealerInfoDao">

    <sql id="baseColumn">
        id,
        company_id,
        open_id,
        type,
        dealer_code,
        dealer_name,
        dealer_type,
        dealer_invite_code,
        shop_invite_code,
        superior_id,
        dealer_area,
        linkman,
        phone,
        address,
        create_time,
        status,
        is_delete,
        we_chart_name,
        head_img_url,
        dealer_we_chart_group_img_url,
        shop_we_chart_group_img_url,
        provinces,
        city,
        district,
        street,
        gt_dealer_id,
        gt_dealer_data,
        dealer_area_list,
        account_balance,
        longitude,
        latitude,
        stores_img,
        contract_img,
        license_img,
        remark,
        level,
        account_type,
        score
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
            <id property="id" column="id"/>
            <result property="companyId"                column="company_id"/>
            <result property="openId"                   column="open_id"/>
            <result property="type"                     column="type"/>
            <result property="dealerCode"               column="dealer_code"/>
            <result property="dealerName"               column="dealer_name"/>
            <result property="dealerType"               column="dealer_type"/>
            <result property="dealerInviteCode"         column="dealer_invite_code"/>
            <result property="shopInviteCode"           column="shop_invite_code"/>
            <result property="superiorId"               column="superior_id"/>
            <result property="dealerArea"               column="dealer_area"/>
            <result property="linkman"                  column="linkman"/>
            <result property="phone"                    column="phone"/>
            <result property="address"                  column="address"/>
            <result property="createTime"               column="create_time"/>
            <result property="status"                   column="status"/>
            <result property="isDelete"                 column="is_delete"/>
            <result property="weChartName"              column="we_chart_name"/>
            <result property="headImgUrl"               column="head_img_url"/>
            <result property="dealerWeChartGroupImgUrl" column="dealer_we_chart_group_img_url"/>
            <result property="shopWeChartGroupImgUrl"   column="shop_we_chart_group_img_url"/>
            <result property="provinces"                column="provinces"/>
            <result property="city"                     column="city"/>
            <result property="district"                 column="district"/>
            <result property="street"                   column="street"/>
            <result property="dealerTypeName"           column="type_name"/>
            <result property="statusName"               column="status_name"/>
            <result property="dealerAreaList"           column="dealer_area_list"/>
            <result property="fullAddress"              column="full_address"/>
            <result property="accountBalance"           column="account_balance"/>
            <result property="gtDealerId"               column="gt_dealer_id"/>
            <result property="gtDealerData"             column="gt_dealer_data"/>
            <result property="longitude"                column="longitude"/>
            <result property="latitude"                 column="latitude"/>
            <result property="storesImg"                column="stores_img"/>
            <result property="contractImg"              column="contract_img"/>
            <result property="licenseImg"               column="license_img"/>
            <result property="remark"                   column="remark"/>
	        <result property="level"                 column="level"/>
            <result property="score"                 column="score"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="openId != null and openId != ''">
                and open_id = #{openId}
            </if>
            <if test="superiorId != null">
                and superior_id = #{superiorId}
            </if>
            <if test="dealerArea != null">
                and dealer_area = #{dealerArea}
            </if>
            <if test="eqDealerName != null and eqDealerName != ''">
                and dealer_name = #{eqDealerName}
            </if>
            <if test="dealerName != null and dealerName != ''">
                and dealer_name like concat('%', #{dealerName}, '%')
            </if>
            <if test="notContainId != null"><!-- 不包含输入的id -->
                and id &lt;&gt; #{notContainId}
            </if>
            <if test="dealerCode != null and dealerCode != ''">
                and dealer_code like concat('%', #{dealerCode}, '%')
            </if>
            <if test="eqDealerCode != null and eqDealerCode != ''">
                and dealer_code = #{eqDealerCode}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="dealerInviteCode != null and dealerInviteCode != ''">
                and dealer_invite_code = #{dealerInviteCode}
            </if>
            <if test="gtDealerId != null">
                and gt_dealer_id = #{gtDealerId}
            </if>
            <if test="eqDealerCode != null and eqDealerCode != ''">
                and dealer_code = #{eqDealerCode}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="accountType != null">
                and account_type = #{accountType}
            </if>

        </where>
    </sql>

    <sql id="joinSelectiveWhere">
        <where>
            <if test="phone != null and phone != ''">
                and tcdu.phone = #{phone}
            </if>
            <if test="isDelete != null">
                and tcdu.is_delete = #{isDelete}
            </if>
            <if test="companyId != null">
                and tcdu.company_id = #{companyId}
            </if>
            <if test="openId != null and openId != ''">
                and tcdu.open_id = #{openId}
            </if>
            <if test="superiorId != null">
                and tcdu.superior_id = #{superiorId}
            </if>
            <if test="dealerArea != null">
                and tcdu.dealer_area = #{dealerArea}
            </if>
            <if test="eqDealerName != null and eqDealerName != ''">
                and tcdu.dealer_name = #{eqDealerName}
            </if>
            <if test="dealerName != null and dealerName != ''">
                and tcdu.dealer_name like concat('%', #{dealerName}, '%')
            </if>
            <if test="notContainId != null"><!-- 不包含输入的id -->
                and tcdu.id &lt;&gt; #{notContainId}
            </if>
            <if test="dealerCode != null and dealerCode != ''">
                and tcdu.dealer_code like concat('%', #{dealerCode}, '%')
            </if>
            <if test="eqDealerCode != null and eqDealerCode != ''">
                and tcdu.dealer_code = #{eqDealerCode}
            </if>
            <if test="status != null">
                and tcdu.status = #{status}
            </if>
            <if test="type != null">
                and tcdu.type = #{type}
            </if>
            <if test="dealerInviteCode != null and dealerInviteCode != ''">
                and tcdu.dealer_invite_code = #{dealerInviteCode}
            </if>
            <if test="dealerType != null and dealerType != ''">
                and tcdu.dealer_type = #{dealerType}
            </if>
            <if test="notEqDealerName != null and notEqDealerName != ''">
                and tcdu.dealer_name != #{notEqDealerName}
            </if>
            <if test="accountType != null">
                and tcdu.account_type = #{accountType}
            </if>
        </where>
    </sql>

    <!-- 根据条件查询云商客户信息 -->
    <select id="getByParam" resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        from t_cloud_dealer_info
        <include refid="selectiveWhere"/>
    </select>
    <select id="getSubDealer" resultMap="baseResultMap">
        SELECT i.dealer_code,i.dealer_name, i.phone,i.account_type
        FROM t_cloud_dealer_info i
        left join t_cloud_dealer_relation r on i.id=r.dealer_id
        <where>
            <if test="companyId !=null">
                and i.company_id = #{companyId}
            </if>
            <if test="dealerId !=null">
                and r.parent_id = #{dealerId}
            </if>
            <if test="accountType !=null">
                and i.account_type = #{accountType}
            </if>
        </where>
    </select>
    <select id="getSubCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_cloud_dealer_info i
            left join t_cloud_dealer_relation r on i.id=r.dealer_id
        <where>
            <if test="companyId !=null">
                and i.company_id = #{companyId}
            </if>
            <if test="dealerId !=null">
                and r.parent_id = #{dealerId}
            </if>
            <if test="accountType !=null">
                and i.account_type = #{accountType}
            </if>
        </where>
    </select>

    <update id="addVirtualAmount">
        update t_cloud_dealer_info set virtual_amount=virtual_amount + #{amount} where dealer_code=#{dealerCode} and company_id=#{companyId}
    </update>

    <select id="getListByParam" resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        from t_cloud_dealer_info
        <include refid="selectiveWhere"/>
    </select>

    <!-- 根据条件查询云商客户列表 -->
    <select id="queryList" resultMap="baseResultMap">
        select
            tcdu.id,
            tcdu.company_id,
            tcdu.open_id,
            tcdu.type,
            tcdu.dealer_code,
            tcdu.dealer_name,
            tcdu.dealer_type,
            tcdu.dealer_invite_code,
            tcdu.shop_invite_code,
            tcdu.superior_id,
            tcdu.dealer_area,
            tcdu.linkman,
            tcdu.phone,
            tcdu.create_time,
            tcdu.status,
            tcdu.is_delete,
            tcdu.we_chart_name,
            tcdu.head_img_url,
            tcdu.dealer_we_chart_group_img_url,
            tcdu.shop_we_chart_group_img_url,
            tcdu.provinces,
            tcdu.city,
            tcdu.district,
            tcdu.address,
            tcdu.dealer_area_list,
            (case tcdu.status when 0 then '停用' when 1 then '启用' else '' end) as status_name,
            tcdt.type_name,
            concat(tcdu.provinces, tcdu.city, tcdu.district, tcdu.address) as full_address,
            tcdu.account_balance,
            tcdu.level
        from
            t_cloud_dealer_info tcdu
        left join t_cloud_dealer_business_area_record tcdbar on tcdu.dealer_area = tcdbar.id
        left join t_cloud_dealer_type tcdt on tcdu.dealer_type = tcdt.id
        <include refid="joinSelectiveWhere"/>
        order by tcdu.create_time desc
    </select>

    <select id="queryListPermission" resultMap="baseResultMap">
        select tcdu.id,
        tcdu.open_id,
        tcdu.type,
        tcdu.dealer_code,
        tcdu.dealer_name,
        tcdu.dealer_type,
        tcdu.dealer_invite_code,
        tcdu.shop_invite_code,
        tcdu.superior_id,
        tcdu.dealer_area,
        tcdu.linkman,
        tcdu.phone,
        tcdu.company_id,
        tcdu.create_time,
        tcdu.status,
        tcdu.is_delete,
        tcdu.we_chart_name,
        tcdu.head_img_url,
        tcdu.dealer_we_chart_group_img_url,
        tcdu.shop_we_chart_group_img_url,
        tcdu.provinces,
        tcdu.city,
        tcdu.district,
        tcdu.address,
        tcdu.bus_area_name,
        tcdu.dealer_area_list,
        tcdu.status_name,
        tcdu.type_name,
        tcdu.full_address,
        tcdu.account_balance from (
        <foreach collection="areaModelList" item="item" index="index" separator="union">select
            tcdu.id,
            tcdu.open_id,
            tcdu.type,
            tcdu.dealer_code,
            tcdu.dealer_name,
            tcdu.dealer_type,
            tcdu.dealer_invite_code,
            tcdu.shop_invite_code,
            tcdu.superior_id,
            tcdu.dealer_area,
            tcdu.linkman,
            tcdu.phone,
            tcdu.company_id,
            tcdu.create_time,
            tcdu.status,
            tcdu.is_delete,
            tcdu.we_chart_name,
            tcdu.head_img_url,
            tcdu.dealer_we_chart_group_img_url,
            tcdu.shop_we_chart_group_img_url,
            tcdu.provinces,
            tcdu.city,
            tcdu.district,
            tcdu.address,
            tcdbar.bus_area_name,
            tcdu.dealer_area_list,
            (case tcdu.status when 0 then '停用' when 1 then '启用' else '' end) as status_name,
            tcdt.type_name,
            concat(tcdu.provinces, tcdu.city, tcdu.district, tcdu.address) as full_address,
            tcdu.account_balance
            from
            t_cloud_dealer_info tcdu
            left join t_cloud_dealer_business_area_record tcdbar on tcdu.dealer_area = tcdbar.id
            left join t_cloud_dealer_type tcdt on tcdu.dealer_type = tcdt.id
            <where>
                <if test="item.province != null and item.province != ''">
                    AND tcdu.provinces = #{item.province}
                </if>
                <if test="item.city != null and item.city != ''">
                    AND tcdu.city = #{item.city}
                </if>
                <if test="item.county != null and item.county != ''">
                    AND tcdu.district = #{item.county}
                </if>
            </where>
        </foreach>
        ) tcdu
        <include refid="joinSelectiveWhere"/>
        order by tcdu.create_time desc

    </select>

    <!-- 根据条件查询分销商客户列表 -->
    <select id="getDistributorList" resultType="com.intelliquor.cloud.shop.dealer.model.resp.DistributorUserResp">
        select
            tcdu.id,
            tcdu.company_id,
            tcdu.open_id,
            tcdu.type,
            tcdu.dealer_code as distributorCode ,
            tcdu.dealer_name as distributorName,
            tcdu.dealer_type,
            tcdu.dealer_invite_code,
            tcdu.shop_invite_code,
            tcdu.superior_id,
            tcdu.dealer_area,
            tcdu.linkman,
            tcdu.phone,
            tcdu.address,
            tcdu.create_time,
            tcdu.status,
            tcdu.is_delete,
            u1.dealer_code,
            u1.dealer_name,
            tcdu.dealer_area_list,
	    concat(tcdu.provinces, tcdu.city, tcdu.district, tcdu.street, tcdu.address) as full_address,
            tcdu.account_balance,
            tcdu.level
        from
        t_cloud_dealer_info tcdu
        left join t_cloud_dealer_info u1 on u1.id = tcdu.superior_id
        <where>
            tcdu.is_delete=1 and tcdu.type = 2

            <if test="companyId != null">
                and tcdu.company_id = #{companyId}
            </if>
            <if test="areaId != null">
                and tcdu.dealer_area = #{areaId}
            </if>

            <if test="dealerName != null and dealerName != ''">
                and u1.dealer_name like concat('%', #{dealerName}, '%')
            </if>
            <if test="dealerCode != null and dealerCode != ''">
                and u1.dealer_code like concat('%', #{dealerCode}, '%')
            </if>

            <if test="distributorCode !=null and distributorCode !='' ">
                and  tcdu.dealer_code like concat('%',#{distributorCode},'%')
            </if>
            <if test="distributorName !=null and distributorName !='' ">
                and  tcdu.dealer_name like concat('%',#{distributorName},'%')
            </if>
            <if test="dealerCode !=null and dealerCode !='' ">
                and   u1.dealer_code like concat('%',#{dealerCode},'%')
            </if>
            <if test="dealerName !=null and dealerName !='' ">
                and   u1.dealer_name like concat('%',#{dealerName},'%')
            </if>

            <if test="startTime!=null and startTime !='' ">
                and tcdu.create_time >= #{startTime}
            </if>
            <if test="endTime!=null and endTime !='' ">
                and tcdu.create_time &lt;= #{endTime}
            </if>

        </where>
        order by tcdu.create_time desc
    </select>

    <!-- 新增云商用户信息 -->
    <insert id="insert" parameterType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_cloud_dealer_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="openId != null"> open_id, </if>
            <if test="type != null"> type, </if>
            <if test="dealerCode != null"> dealer_code, </if>
            <if test="dealerName != null"> dealer_name, </if>
            <if test="dealerType != null"> dealer_type, </if>
            <if test="dealerInviteCode != null"> dealer_invite_code, </if>
            <if test="shopInviteCode != null"> shop_invite_code, </if>
            <if test="superiorId != null"> superior_id, </if>
            <if test="dealerArea != null"> dealer_area, </if>
            <if test="linkman != null"> linkman, </if>
            <if test="phone != null"> phone, </if>
            <if test="address != null"> address, </if>
            <if test="companyId != null"> company_id, </if>
            <if test="createTime != null"> create_time, </if>
            <if test="status != null"> status, </if>
            <if test="isDelete != null"> is_delete, </if>
            <if test="weChartName != null"> we_chart_name, </if>
            <if test="headImgUrl != null"> head_img_url, </if>
            <if test="dealerWeChartGroupImgUrl != null"> dealer_we_chart_group_img_url, </if>
            <if test="shopWeChartGroupImgUrl != null"> shop_we_chart_group_img_url, </if>
            <if test="provinces != null"> provinces, </if>
            <if test="city != null"> city, </if>
            <if test="district != null"> district, </if>
            <if test="street != null"> street, </if>
            <if test="gtDealerId != null"> gt_dealer_id, </if>
            <if test="gtDealerData != null"> gt_dealer_data, </if>
            <if test="dealerAreaList != null"> dealer_area_list, </if>
            <if test="accountBalance != null"> account_balance, </if>
            <if test="longitude != null"> longitude, </if>
            <if test="latitude != null"> latitude, </if>
            <if test="storesImg != null"> stores_img, </if>
            <if test="contractImg != null"> contract_img, </if>
            <if test="licenseImg != null"> license_img, </if>
            <if test="remark != null"> remark, </if>
            <if test="accountType != null"> account_type, </if>
	    <if test="level != null"> level, </if>
            <if test=" storeProvinces != null"> store_provinces, </if>
            <if test="storeCity != null"> store_city, </if>
            <if test="storeDistrict != null"> store_district, </if>
            <if test="storeStreet != null"> store_street, </if>
            <if test="storeAddress != null"> store_address, </if>
            <if test="contractCode != null"> contract_code, </if>
            <if test="contractType != null"> contract_type, </if>
            <if test="contractName != null"> contract_name, </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="openId != null"> #{openId}, </if>
            <if test="type != null"> #{type}, </if>
            <if test="dealerCode != null"> #{dealerCode}, </if>
            <if test="dealerName != null"> #{dealerName}, </if>
            <if test="dealerType != null"> #{dealerType}, </if>
            <if test="dealerInviteCode != null"> #{dealerInviteCode}, </if>
            <if test="shopInviteCode != null"> #{shopInviteCode}, </if>
            <if test="superiorId != null"> #{superiorId}, </if>
            <if test="dealerArea != null"> #{dealerArea}, </if>
            <if test="linkman != null"> #{linkman}, </if>
            <if test="phone != null"> #{phone}, </if>
            <if test="address != null"> #{address}, </if>
            <if test="companyId != null"> #{companyId}, </if>
            <if test="createTime != null"> #{createTime}, </if>
            <if test="status != null"> #{status}, </if>
            <if test="isDelete != null"> #{isDelete}, </if>
            <if test="weChartName != null"> #{weChartName}, </if>
            <if test="headImgUrl != null"> #{headImgUrl}, </if>
            <if test="dealerWeChartGroupImgUrl != null"> #{dealerWeChartGroupImgUrl}, </if>
            <if test="shopWeChartGroupImgUrl != null"> #{shopWeChartGroupImgUrl}, </if>
            <if test="provinces != null"> #{provinces}, </if>
            <if test="city != null"> #{city}, </if>
            <if test="district != null"> #{district}, </if>
            <if test="street != null"> #{street}, </if>
            <if test="gtDealerId != null"> #{gtDealerId}, </if>
            <if test="gtDealerData != null"> #{gtDealerData}, </if>
            <if test="dealerAreaList != null"> #{dealerAreaList}, </if>
            <if test="accountBalance != null"> #{accountBalance}, </if>
            <if test="longitude != null"> #{longitude}, </if>
            <if test="latitude != null"> #{latitude}, </if>
            <if test="storesImg != null"> #{storesImg}, </if>
            <if test="contractImg != null"> #{contractImg}, </if>
            <if test="licenseImg != null"> #{licenseImg}, </if>
            <if test="remark != null"> #{remark}, </if>
            <if test="accountType != null"> #{accountType}, </if>
	    <if test="level != null"> #{level}, </if>
            <if test=" storeProvinces != null"> #{storeProvinces}, </if>
            <if test="storeCity != null"> #{storeCity}, </if>
            <if test="storeDistrict != null"> #{storeDistrict}, </if>
            <if test="storeStreet != null"> #{storeStreet}, </if>
            <if test="storeAddress != null"> #{storeAddress}, </if>
            <if test="contractCode != null"> #{contractCode}, </if>
            <if test="contractType != null"> #{contractType}, </if>
            <if test="contractName != null"> #{contractName}, </if>
        </trim>
    </insert>

    <!-- 更新云商用户信息 -->
    <update id="update" parameterType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        update t_cloud_dealer_info
        <set>
            <if test="openId != null"> open_id = #{openId}, </if>
            <if test="type != null"> type = #{type}, </if>
            <if test="dealerCode != null"> dealer_code = #{dealerCode}, </if>
            <if test="dealerName != null"> dealer_name = #{dealerName}, </if>
            <if test="dealerType != null"> dealer_type = #{dealerType}, </if>
            <if test="dealerInviteCode != null"> dealer_invite_code = #{dealerInviteCode}, </if>
            <if test="shopInviteCode != null"> shop_invite_code = #{shopInviteCode}, </if>
            <if test="superiorId != null"> superior_id = #{superiorId}, </if>
            <if test="dealerArea != null"> dealer_area = #{dealerArea}, </if>
            <if test="linkman != null"> linkman = #{linkman}, </if>
            <if test="phone != null"> phone = #{phone}, </if>
            <if test="address != null"> address = #{address}, </if>
            <if test="companyId != null"> company_id = #{companyId}, </if>
            <if test="createTime != null"> create_time = #{createTime}, </if>
            <if test="status != null"> status = #{status}, </if>
            <if test="isDelete != null"> is_delete = #{isDelete}, </if>
            <if test="weChartName != null"> we_chart_name = #{weChartName}, </if>
            <if test="headImgUrl != null"> head_img_url = #{headImgUrl}, </if>
            <if test="dealerWeChartGroupImgUrl != null"> dealer_we_chart_group_img_url = #{dealerWeChartGroupImgUrl}, </if>
            <if test="shopWeChartGroupImgUrl != null"> shop_we_chart_group_img_url = #{shopWeChartGroupImgUrl}, </if>
            <if test="provinces != null"> provinces = #{provinces}, </if>
            <if test="city != null"> city = #{city}, </if>
            <if test="district != null"> district = #{district}, </if>
            <if test="street != null"> street = #{street}, </if>
            <if test="gtDealerId != null"> gt_dealer_id = #{gtDealerId}, </if>
            <if test="gtDealerData != null"> gt_dealer_data = #{gtDealerData}, </if>
            <if test="dealerAreaList != null"> dealer_area_list = #{dealerAreaList}, </if>
            <if test="accountBalance != null"> account_balance = #{accountBalance}, </if>
            <if test="longitude != null"> longitude = #{longitude}, </if>
            <if test="latitude != null"> latitude = #{latitude}, </if>
            <if test="storesImg != null"> stores_img = #{storesImg}, </if>
            <if test="contractImg != null"> contract_img = #{contractImg}, </if>
            <if test="licenseImg != null"> license_img = #{licenseImg}, </if>
            <if test="contractType != null"> contract_type = #{contractType}, </if>
            <if test="contractCode != null"> contract_code = #{contractCode}, </if>
            <if test="contractName != null"> contract_name = #{contractName}, </if>
            <if test="storeProvinces != null"> store_provinces = #{storeProvinces}, </if>
            <if test="storeCity != null"> store_city = #{storeCity}, </if>
            <if test="storeDistrict != null"> store_district = #{storeDistrict}, </if>
            <if test="storeAddress != null"> store_address = #{storeAddress}, </if>
            <if test="accountType != null"> account_type = #{accountType}, </if>
        </set>
        where id = #{id}
    </update>

    <!--更新数据-->
    <update id="addAmountBalance">

        update t_cloud_dealer_info
        set account_balance = account_balance + #{amount}
        where id = #{id}
    </update>

    <!--更新数据-->
    <update id="addScore">

        update t_cloud_dealer_info
        set score = score + #{score}
        where id = #{id}
    </update>

    <!--更新数据-->
    <update id="addAmountAndScore">

        update t_cloud_dealer_info
        set score = score + #{score},account_balance = account_balance + #{amount}
        where id = #{id}
    </update>

    <!--根据主键获取经销商信息-->
    <select id="getById" resultMap="baseResultMap">
        select
            <include refid="baseColumn"/>
        from t_cloud_dealer_info
        where id = #{id}
    </select>

    <select id="totalByDealerCode" resultType="java.lang.Integer">
        select count(1) from t_cloud_dealer_info where dealer_code = #{dealerCode} and company_id = #{companyId}
    </select>

    <!--  根据分销商邀请码统计  -->
    <select id="totalByDealerInviteCode" resultType="java.lang.Integer">
        select count(1) from t_cloud_dealer_info where dealer_invite_code = #{dealerInviteCode} and company_id = #{companyId}
    </select>

    <!--  根据终端店邀请码统计  -->
    <select id="totalByShopInviteCode" resultType="java.lang.Integer">
        select count(1) from t_cloud_dealer_info where shop_invite_code = #{shopInviteCode} and company_id = #{companyId}
    </select>

    <select id="getByOpenId" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_info
        WHERE open_id = #{openId}
        limit 1
    </select>

    <!-- 查询分销商列表 -->
    <select id="queryDistributorUserList" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        select
        cdu.id,
        cdu.company_id,
        cdu.open_id,
        cdu.type,
        cdu.dealer_code,
        cdu.dealer_name,
        cdu.dealer_type,
        cdu.dealer_invite_code,
        cdu.shop_invite_code,
        cdu.superior_id,
        cdu.dealer_area,
        cdu.linkman,
        cdu.phone,
        cdu.create_time,
        cdu.status,
        cdu.is_delete,
        cdu.we_chart_name,
        cdu.head_img_url,
        cdu.dealer_we_chart_group_img_url,
        cdu.shop_we_chart_group_img_url,
        cdu.provinces,
        cdu.city,
        cdu.district,
        cdu.street,
        cdu.address,
        cdu.dealer_area_list,
        (case cdu.status when 0 then '停用' when 1 then '启用' else '' end) as status_name,
        cdt.type_name,
        concat(cdu.provinces, cdu.city, cdu.district, cdu.street, cdu.address) as full_address,
        cdu.account_balance,
        cdu.longitude,
        cdu.latitude
        FROM  t_cloud_dealer_relation cdr
        LEFT JOIN t_cloud_dealer_info cdu ON cdr.dealer_id = cdu.id
        LEFT JOIN t_cloud_dealer_type cdt on cdu.dealer_type = cdt.id
        <where>
            cdu.is_delete = 1 and cdu.type = 2 and status = 1 and cdu.account_type = 3
            <if test="companyId != null">
                and cdr.company_id = #{companyId} and cdu.company_id = #{companyId}
            </if>
            <if test="parentId != null">
                and cdr.parent_id = #{parentId}
            </if>
            <if test="dealerName != null and dealerName != ''">
                and cdu.dealer_name like concat('%', #{dealerName}, '%')
            </if>
            <if test="dealerCode != null and dealerCode != ''">
                and cdu.dealer_code like concat('%', #{dealerCode}, '%')
            </if>
            <if test="startTime!=null and startTime !='' ">
                and cdr.create_time >= #{startTime}
            </if>
            <if test="endTime!=null and endTime !='' ">
                and cdr.create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY cdr.create_time DESC
    </select>

    <select id="queryTerminalList" resultType="com.intelliquor.cloud.shop.dealer.model.resp.TerminalShopResp">
        SELECT ts.id,
               ts.shop_name,
               ts.leader_name,
               ts.leader_phone,
               ts.tag,
               ts.head_img,
               ts.remark,
               ts.province,
               ts.city,
               ts.district,
               ts.address,
               ts.longitude,
               ts.latitude,
               ts.type,
               ts.is_image,
               ts.keeper_name,
               ts.keeper_phone,
               ts.shop_area,
               ts.license_img,
               ts.license_code,
               ts.is_delete,
               ts.create_time,
               ts.create_user,
               ts.update_time,
               ts.company_id,
               ts.member_shop_id,
               ts.level_code,
               ts.level_code as levelName,
               ts.shop_type  as shopType,
               ts.is_high_member,
               tam.name                             as managerOrStaffName,
               tam.phone                            as managerOrStaffPhone,
               ts.terminal_divide_id,
               IFNULL(tamm.name, tammm.name)        as accountManagerName,
               IFNULL(tamm.phone, tammm.phone)      as accountManagerPhone,
               ms.virtual_amount,
               ts.merge_type,
               ts.merge_id,
               ms.status as terminalStatus,
               ts.is_prepare FROM t_cloud_dealer_info i left join t_cloud_dealer_relation r on r.dealer_id =i.id
                                                        left join t_terminal_shop ts on ts.deputy_code =i.dealer_code
                                                        left join t_member_shop ms on ts.member_shop_id = ms.id
                                                        left join t_terminal_shop_level_config tslc on ms.level_code = tslc.level_code
                                                        left join t_terminal_account_manager tam on ts.create_user = tam.id
                                                        left join t_terminal_account_manager tamDivide on ts.terminal_divide_id = tamDivide.id
                                                        left join t_terminal_account_manager tamm on tamm.id = tamDivide.parent_id
                                                        left join t_terminal_account_manager tammm on tam.parent_id = tammm.id
        where (r.parent_id =#{dealerId} or r.parent_id in (select dealer_id from t_cloud_dealer_relation where parent_id=#{dealerId})) and i.account_type =5 and i.is_delete =1 and i.status =1
        <if test="type != null">
            and ts.shop_type = #{type}
        </if>
        <if test="status != null">
            and ms.status = #{status}
            and ts.merge_type &lt; 2
        </if>
        <if test="keyword != null and keyword != ''">
            and (ts.shop_name like concat('%', #{keyword}, '%')
            or ts.leader_name like concat('%', #{keyword}, '%')
            or ts.leader_phone like concat('%', #{keyword}, '%')
            )
        </if>
        and ms.is_member =0 and ts.id is not null
        order by ms.create_time desc
    </select>

    <select id="getBySuperiorid" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        select
        *
        from
            t_cloud_dealer_info
        <where>
            is_delete = 1 and status = 1
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="startTime!=null and startTime !='' ">
                and create_time > #{startTime}
            </if>
            <if test="endTime!=null and endTime !='' ">
                and create_time &lt;= #{endTime}
            </if>
            <if test="superiorId != null">
                and superior_id = #{superiorId}
            </if>
        </where>
    </select>

    <!-- 查询供货方列表 -->
    <select id="querySupplierList" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        select
        cdu.id,
        cdu.company_id,
        cdu.type,
        cdu.dealer_code,
        cdu.dealer_name,
        cdu.dealer_type,
        cdu.dealer_invite_code,
        cdu.shop_invite_code,
        cdu.dealer_area,
        cdu.linkman,
        cdu.phone,
        cdu.address,
        cdu.status,
        cdu.dealer_area_list,
        concat(cdu.provinces, cdu.city, cdu.district, cdu.street, cdu.address) as full_address,
        cdr.create_time,
        cdu.longitude,
        cdu.latitude
        FROM  t_cloud_dealer_relation cdr
        LEFT JOIN t_cloud_dealer_info cdu ON cdr.parent_id = cdu.id
        <where>
            cdu.is_delete = 1 and status = 1
            <if test="companyId != null">
                and cdu.company_id = #{companyId} and cdr.company_id = #{companyId}
            </if>
            <if test="distributorId != null">
                and cdr.dealer_id = #{distributorId}
            </if>
            <if test="dealerName != null and dealerName != ''">
                and cdu.dealer_name like concat('%', #{dealerName}, '%')
            </if>
            <if test="dealerCode != null and dealerCode != ''">
                and cdu.dealer_code like concat('%', #{dealerCode}, '%')
            </if>
            <if test="startTime!=null and startTime !='' ">
                and cdr.create_time >= #{startTime}
            </if>
            <if test="endTime!=null and endTime !='' ">
                and cdr.create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY cdr.create_time DESC
    </select>

    <select id="getByDealerCodeAndCompanyId" resultMap="baseResultMap">
        select
        <include refid="baseColumn" />
        from t_cloud_dealer_info
        where dealer_code = #{dealerCode} and company_id = #{companyId} and is_delete=1

    </select>

    <select id="getByDealerCode" resultMap="baseResultMap">
        select
        <include refid="baseColumn" />
        from t_cloud_dealer_info
        where dealer_code = #{dealerCode} and is_delete=1

    </select>


    <!-- 退出 -->
    <update id="logOut">
        update t_cloud_dealer_info set open_id = "" where id = #{id}
    </update>

    <select id="getByUnionId" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        SELECT
            a.id,
            a.score,
            s.id as staffId
        FROM
            t_cloud_dealer_info a left join t_cloud_dealer_staff s on a.id = s.dealer_id
        WHERE
        a.company_id = #{companyId}
          and  s.primary_account = 1
          AND s.type = 0
          AND s.id = #{customerId}
          AND s.phone = #{phone}
    </select>

    <update id="updateScore">
        update t_cloud_dealer_info set score = score-#{useScore} where id = #{id}
    </update>

    <select id="getDealerScoreDetail" resultType="com.intelliquor.cloud.shop.dealer.model.resp.DealerScoreDetailResp">
        SELECT
            a.id,
            a.score,
            (
                SELECT
                    sum( a.score )
                FROM
                    t_cloud_dealer_reward_record_#{companyId} a
                        left join t_cloud_dealer_staff s on a.dealer_id = s.dealer_id
                WHERE
                    a.is_delete = 1
                  AND a.prize_type = 2
                  AND a.earning_type = 1
                  AND s.id = #{customerId}
                  AND s.phone = #{phone}
                  AND a.company_id = #{companyId}) AS obtainScore,
            (
                SELECT
                    sum( a.score )
                FROM
                    t_cloud_dealer_reward_record_#{companyId} a
                        left join t_cloud_dealer_staff s on a.dealer_id = s.dealer_id
                WHERE
                    a.is_delete = 1
                  AND a.prize_type = 2
                  AND a.earning_type = 3
                  AND s.id = #{customerId}
                  AND s.phone = #{phone}
                  AND a.company_id = #{companyId}) AS consumeScore
        FROM
            t_cloud_dealer_info a
        WHERE
                a.id = (
                SELECT
                    dealer_id
                FROM
                    t_cloud_dealer_staff
                WHERE
                    primary_account = 1
                  AND type = 0
                  AND id = #{customerId}
                  AND phone = #{phone}
                  AND company_id = #{companyId})
    </select>

    <!-- 删除 -->
    <update id="delete">
        update t_cloud_dealer_info set open_id = "" and is_delete = 0 where id = #{id}
    </update>

    <!--根据经销商/分销商的编码 更改经销商/分销商的账户类型-->
    <update id="updateCloudDealerInfoByDealerCode">
        update
        t_cloud_dealer_info
        set account_type = #{accountType}
        where
        dealer_code = #{dealerCode}
    </update>

    <update id="updateDealerInfoByDealerCode" keyProperty="id" keyColumn="id" useGeneratedKeys="true">
        update t_cloud_dealer_info
        <set>
            <if test="dealerName != null"> dealer_name = #{dealerName}, </if>
            <if test="phone != null"> phone = #{phone}, </if>
            <if test="address != null"> address = #{address}, </if>
            <if test="shopWeChartGroupImgUrl != null"> shop_we_chart_group_img_url = #{shopWeChartGroupImgUrl}, </if>
            <if test="provinces != null"> provinces = #{provinces}, </if>
            <if test="city != null"> city = #{city}, </if>
            <if test="district != null"> district = #{district}, </if>
            <if test="licenseImg != null"> license_img = #{licenseImg}, </if>
        </set>
        where dealer_code = #{dealerCode}
    </update>

    <!-- 分销商销售排行 -->
    <select id="queryDistributorSaleRankList" resultType="com.intelliquor.cloud.shop.dealer.model.resp.DealerRankResp">
        select
            tcdi.dealer_name as name,
            (
                select
                    ifnull(sum(pay_amount), 0)
                from t_cloud_dealer_order
                where
                        tms.dealer_code = sender_code
                    AND sender_type = 2
                    AND order_status in (1, 2, 3)
                    AND company_id = #{companyId}
                    AND create_time >= #{startTime}
                    AND #{endTime} >= create_time
            ) amount
        from
             t_cloud_dealer_relation tcdr
        left join t_cloud_dealer_info tcdi on tcdr.dealer_id = tcdi.id
        left join t_member_shop tms on tms.dealer_code = tcdi.dealer_code
        where
            tcdi.is_delete = 1
            and tcdi.type = 2
            and tcdr.parent_id = #{superiorId}
            and tcdr.company_id = #{companyId}
            and tcdi.company_id = #{companyId}
            and tms.company_id = #{companyId}
        order by amount desc, name ASC
        limit 20
    </select>


    <!--获取分销商下的订货单数-->
    <select id="getOrderFormCount" resultType="int">
        select  count(1) from  t_shop_dealer_order  o
        left join t_cloud_dealer_info i on o.dealer_code=i.dealer_code
        where o.sender_code = #{senderCode} and o.company_id = #{companyId}
        <if test="startTime != null and startTime != ''">
            and o.create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and #{endTime} >= o.create_time
        </if>
        and i.account_type=3
        and o.order_status in (1,2,3)

    </select>

    <!-- pda查询分销商列表 -->
    <select id="queryPdaDistributorUserList" resultType="com.intelliquor.cloud.shop.dealer.model.resp.PdaTerminalResp">
        select
            tcdi.id,
            tcdi.dealer_name as name,
            tcdi.linkman as linkman,
            tcdi.phone as linkphone,
            tcdi.address as address
        from
            t_cloud_dealer_info tcdi
        left join t_cloud_dealer_relation tcdr on tcdi.id = tcdr.dealer_id
        <where>
            <if test="parentId != null">
                and tcdr.parent_id = #{parentId}
            </if>
            <if test="companyId != null">
                and tcdi.company_id = #{companyId} and tcdr.company_id = #{companyId}
            </if>
            <if test="name != null and name != ''">
                and tcdi.dealer_name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>
    <select id="getByStaffPhone" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        select i.*
        from t_cloud_dealer_info i
        left join t_cloud_dealer_staff s
        on i.id = s.dealer_id
        where s.is_delete = 1
          and i.is_delete = 1
          and s.status = 1
          and s.phone = #{phone}
          limit 1
    </select>
    <select id="getListByStaffPhone" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        select i.*
        from t_cloud_dealer_info i
        left join t_cloud_dealer_staff s
        on i.id = s.dealer_id
        where s.is_delete = 1
          and i.is_delete = 1
          and s.status = 1
          and s.phone = #{phone}
    </select>
    <select id="queryContractTypeList" resultType="com.intelliquor.cloud.shop.dealer.model.ContractTypeVO">
        SELECT
        dcr.dealer_code,
        dcr.contract_type AS value,
        ct.contract_name AS text
        FROM
        t_dealer_contract_rel dcr
        left join t_contract_type ct ON dcr.contract_type = ct.contract_type
        WHERE
        dcr.contract_status = 1
        AND dcr.dealer_code = #{dealerCode}
        AND dcr.contract_type IS NOT NULL
        GROUP BY
        dcr.dealer_code,
        dcr.contract_type
    </select>
    <select id="getPartnerListByDealerCodeAndContractCode" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        select id, dealer_name as dealerName, dealer_code as dealerCode, linkman, phone
        from t_cloud_dealer_info
        where id in
            (select dealer_id from t_cloud_dealer_relation where parent_id =
                (select id from t_cloud_dealer_info where dealer_code = #{dealerCode} and is_delete = 1 and status = 1
                order by create_time desc limit 1))
            and account_type = 4
            and contract_code = #{contractCode}
    </select>
    <select id="getSuperDealerTypeByDealerCode" resultType="integer">
        select account_type from t_cloud_dealer_info where id =
            (select parent_id from t_cloud_dealer_relation where dealer_id =
            (select id from t_cloud_dealer_info where dealer_code = #{dealerCode} limit 1))
            limit 1
    </select>


    <select id="selectTerminalShopById" resultType="com.intelliquor.cloud.shop.dealer.model.resp.TerminalShopResp">
        select
            ts.id,
            ts.shop_name,
            ts.leader_name,
            ts.leader_phone,
            ts.tag,
            ts.head_img,
            ts.remark,
            ts.province,
            ts.city,
            ts.district,
            ts.address,
            ts.longitude,
            ts.latitude,
            ts.type,
            ts.is_image,
            ts.image_head_picture,
            ts.keeper_name,
            ts.keeper_phone,
            ts.shop_area,
            ts.whether_license,
            ts.license_img,
            ts.license_code,
            ts.company_person,
            ts.company_name,
            ts.license_message,
            tsis.status,
            ts.is_delete,
            ts.create_time,
            ts.create_user,
            ts.update_time,
            ts.company_id,
            ts.member_shop_id,
            ts.shop_type,
            ts.enterprise_name,
            ts.food_business_license,
            ts.whether_proprietary_trading,
            ts.receiving_warehouse_province,
            ts.receiving_warehouse_city,
            ts.receiving_warehouse_district,
            ts.receiving_warehouse_address,
            ts.level_code,
            ts.contact_name,
            ts.contact_level,
            ts.contact_phone,
            ts.receiving_payment_type,
            ts.receiving_payment_name,
            ts.receiving_payment_account,
            ts.receiving_payment_bank,
            ts.receiving_payment_account_picture,
            ts.audit_user_id,
            ts.audit_result,
            ts.shop_agreement,
            ts.gender,
            ts.birthday,
            ts.age,
            ts.work_unit,
            ts.position,
            ts.personal_preference,
            ts.dealer_contact_type,
            ts.first_buy_goods_name,
            ts.first_buy_goods_number,
            ts.first_score,
            ts.registration_form_picture,
            ts.network_point_establish_reason,
            ts.status as activeStatus,
            ts.distributor_id,
            ts.is_prepare,
            ts.is_high_member,
            ts.copartner_id,
            ts.main_code,
            ts.deputy_code,
            ts.address_status,
            ts.protocol_update_status,
            tslc.level_short_name level_name,
            ts.is_branch,
            ts.merge_type,
            ts.merge_id,
            ts.license_result
        from
            t_terminal_shop ts
                left join
            t_member_shop ms
            on
                ts.member_shop_id = ms.id
                left join
            t_terminal_shop_level_config tslc
            on
                ms.level_code = tslc.level_code
                left join
            t_terminal_shop_info_schedule tsis
            on
                tsis.terminal_shop_id = ts.id
        where
            ts.id = #{id}
          and
            ts.is_delete = 0
    </select>



    <resultMap id="TerminalProtocolRespMap" type="com.intelliquor.cloud.shop.common.model.resp.TerminalProtocolResp">
        <id property="id" column="id" javaType="java.lang.Integer" />
        <result property="protocolType" column="protocol_type" javaType="java.lang.Integer" />
        <result property="terminalShopId" column="terminal_shop_id" javaType="java.lang.Integer" />
        <result property="productProtocolId" column="product_protocol_config_id" javaType="java.lang.Integer" />
        <result property="companyId" column="company_id" javaType="java.lang.Integer" />
        <result property="protocolImage" column="protocol_image" javaType="java.lang.String" />
        <result property="checkStatus" column="check_status" javaType="java.lang.Integer" />
        <result property="endStatus" column="end_status" javaType="java.lang.Integer" />
        <result property="isDeleted" column="delete_status" javaType="java.lang.Integer" />
        <result property="createTime" column="create_time" javaType="java.util.Date" />
        <result property="createUser" column="create_user" javaType="java.lang.Integer" />
        <result property="updateTime" column="update_time" javaType="java.util.Date" />
        <result property="updateUser" column="update_user" javaType="java.lang.Integer" />
        <result property="effectiveTime" column="effective_time" javaType="java.util.Date" />
        <result property="failureTime" column="failure_time" javaType="java.util.Date" />
        <result property="finishStatus" column="finish_status" javaType="java.lang.Integer" />
        <association property="productProtocolModel" javaType="com.intelliquor.cloud.shop.common.model.TerminalProductProtocolModel"
                     column="product_protocol_config_id" select="com.intelliquor.cloud.shop.dealer.dao.DealerInfoDao.selectOneById">
        </association>
    </resultMap>


    <select id="selectTerminalProtocolListByShopId" resultMap="TerminalProtocolRespMap">
        select * from t_terminal_protocol
        where terminal_shop_id = #{terminalShopId}
        and delete_status = 0
        order by protocol_type desc
        <if test="sortType == 0">
            , create_time desc
        </if>
        <if test="sortType == 1">
            , create_time asc
        </if>
    </select>


    <select id="selectOneById"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalProductProtocolModel">
        select p.*,l.level_name from t_terminal_product_protocol p
                                         left join t_terminal_shop_level l on p.level_id = l.id
        where p.is_delete = 0 and p.id = #{id}
    </select>



    <select id="selectTerminalShopContractResp" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopContractResp">
        select
            tsc.id,
            tsc.contract_type,
            tsc.dealer_code,
            cdi.dealer_name,
            cdi.id as dealerId,
            tsc.contract_code,
            tsc.contract_code_name,
            tsc.display_image,
            tsc.package_quantity_image,
            tsc.member_shop_id,
            tsc.terminal_shop_id,
            tsc.create_time,
            tsc.update_time,
            tsc.package_quantity_name,
            tsc.package_quantity_display_surface,
            tsc.package_quantity_display_cost,
            tsc.package_quantity_replenish_stock_quantity,
            tsc.display_name,
            tsc.display_display_surface,
            tsc.display_display_cost,
            tsc.display_replenish_stock_quantity,
            tsc.display_surface,
            tsc.month_scan_in_num,
            tsc.year_scan_in_num,
            tsc.display_amount,
            tsc.package_amount
        from
            t_terminal_shop_contract tsc
                left join
            t_cloud_dealer_info cdi
            on
                tsc.dealer_code = cdi.dealer_code
        where
            tsc.terminal_shop_id = #{tShopId}
        /* group by tsc.id*/
    </select>

    <select id="findAllListByDealer" resultType="com.intelliquor.cloud.shop.dealer.model.DealerInfoModel">
        SELECT
            id,
            dealer_code,
            dealer_name
        FROM
            t_cloud_dealer_info
        WHERE
            is_delete = 1
          AND account_type = 1
    </select>

    <select id="findContractRelListByDealerCode" parameterType="java.lang.String" resultType="com.intelliquor.cloud.shop.dealer.model.DealerContractRelModel">
        SELECT
            a.id,
            a.contract_code,
            a.contract_type,
            b.contract_name as contractTypeStr
        FROM
            t_dealer_contract_rel a
        left join t_contract_type b
        on a.contract_type = b.contract_type and b.status = 1
        WHERE
            contract_status = 1
          AND dealer_code = #{dealerCode}
    </select>
</mapper>
