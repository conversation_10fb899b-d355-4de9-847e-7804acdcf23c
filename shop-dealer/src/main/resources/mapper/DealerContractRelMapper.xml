<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.dealer.dao.DealerContractRelDao">
    <select id="selectDealerCodeByContractCode" resultType="java.lang.String">
        SELECT dealer_code
        FROM t_dealer_contract_rel
        WHERE contract_status = 1
          AND dealer_status = 1
          AND contract_code = #{contractCode}
        group by dealer_code
    </select>

    <select id="selectCodes" resultType="java.lang.String">
        SELECT contract_code
        FROM t_dealer_contract_rel
        GROUP BY contract_code
    </select>

    <select id="selectByCode" resultType="com.intelliquor.cloud.shop.dealer.model.DealerContractRelModel">
        SELECT *
        FROM t_dealer_contract_rel
        WHERE contract_code = #{difCode}
    </select>
</mapper>
