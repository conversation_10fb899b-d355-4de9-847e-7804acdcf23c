<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.dealer.dao.CloudDealerGtGwContractInfoDao">

    <sql id="baseColumn">
        id,
        apply_id,
        dealer_id,
        dealer_code,
        jxs_dealer_id,
        jxs_dealer_code,
        contract_code,
        task_num,
        contract_begindate,
        contract_enddate,
        contract,
        pay_voucher,
        attachment,
        create_op,
        create_time,
        update_op,
        update_time,
        status,
        is_delete,
        company_id,
        remark
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.dealer.model.CloudDealerGtGwContractInfoModel">
            <id property="id" column="id"/>
            <result property="applyId" column="apply_id"/>
            <result property="dealerId" column="dealer_id"/>
            <result property="dealerCode" column="dealer_code"/>
            <result property="jxsDealerId" column="jxs_dealer_id"/>
            <result property="jxsDealerCode" column="jxs_dealer_code"/>
            <result property="contractCode" column="contract_code"/>
            <result property="taskNum" column="task_num"/>
            <result property="contractBegindate" column="contract_begindate"/>
            <result property="contractEnddate" column="contract_enddate"/>
            <result property="contract" column="contract"/>
            <result property="payVoucher" column="pay_voucher"/>
            <result property="attachment" column="attachment"/>
            <result property="createOp" column="create_op"/>
            <result property="createTime" column="create_time"/>
            <result property="updateOp" column="update_op"/>
            <result property="updateTime" column="update_time"/>
            <result property="status" column="status"/>
            <result property="isDelete" column="is_delete"/>
            <result property="companyId" column="company_id"/>
            <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_contract_info
        <include refid="selectiveWhere"/>
        ORDER BY id desc
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.dealer.model.CloudDealerGtGwContractInfoModel">
        INSERT INTO t_cloud_dealer_gt_fxs_contract_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="applyId != null ">
                apply_id,
            </if>
            <if test="dealerId != null ">
            dealer_id,
            </if>
            <if test="dealerCode != null ">
            dealer_code,
            </if>
            <if test="jxsDealerId != null ">
            jxs_dealer_id,
            </if>
            <if test="jxsDealerCode != null ">
            jxs_dealer_code,
            </if>
            <if test="contractCode != null ">
            contract_code,
            </if>
            <if test="taskNum != null ">
            task_num,
            </if>
            <if test="contractBegindate != null ">
            contract_begindate,
            </if>
            <if test="contractEnddate != null ">
            contract_enddate,
            </if>
            <if test="contract != null ">
            contract,
            </if>
            <if test="payVoucher != null ">
            pay_voucher,
            </if>
            <if test="attachment != null ">
            attachment,
            </if>
            <if test="createOp != null ">
            create_op,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="updateOp != null ">
            update_op,
            </if>
            <if test="updateTime != null ">
            update_time,
            </if>
            <if test="status != null ">
            status,
            </if>
            <if test="isDelete != null ">
            is_delete,
            </if>
            <if test="companyId != null ">
            company_id,
            </if>
            <if test="remark != null ">
            remark
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="applyId != null ">
                #{applyId},
            </if>
            <if test="dealerId != null ">
            #{dealerId},
            </if>
            <if test="dealerCode != null ">
            #{dealerCode},
            </if>
            <if test="jxsDealerId != null ">
            #{jxsDealerId},
            </if>
            <if test="jxsDealerCode != null ">
            #{jxsDealerCode},
            </if>
            <if test="contractCode != null ">
            #{contractCode},
            </if>
            <if test="taskNum != null ">
            #{taskNum},
            </if>
            <if test="contractBegindate != null ">
            #{contractBegindate},
            </if>
            <if test="contractEnddate != null ">
            #{contractEnddate},
            </if>
            <if test="contract != null ">
            #{contract},
            </if>
            <if test="payVoucher != null ">
            #{payVoucher},
            </if>
            <if test="attachment != null ">
            #{attachment},
            </if>
            <if test="createOp != null ">
            #{createOp},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="updateOp != null ">
            #{updateOp},
            </if>
            <if test="updateTime != null ">
            #{updateTime},
            </if>
            <if test="status != null ">
            #{status},
            </if>
            <if test="isDelete != null ">
            #{isDelete},
            </if>
            <if test="companyId != null ">
            #{companyId},
            </if>
            <if test="remark != null ">
            #{remark}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.dealer.model.CloudDealerGtGwContractInfoModel">
        UPDATE t_cloud_dealer_gt_fxs_contract_info
        <set>
            <if test="applyId != null ">
                apply_id = #{applyId},
            </if>
            <if test="dealerId != null ">
            dealer_id = #{dealerId},
            </if>
            <if test="dealerCode != null ">
            dealer_code = #{dealerCode},
            </if>
            <if test="jxsDealerId != null ">
            jxs_dealer_id = #{jxsDealerId},
            </if>
            <if test="jxsDealerCode != null ">
            jxs_dealer_code = #{jxsDealerCode},
            </if>
            <if test="contractCode != null ">
            contract_code = #{contractCode},
            </if>
            <if test="taskNum != null ">
            task_num = #{taskNum},
            </if>
            <if test="contractBegindate != null ">
            contract_begindate = #{contractBegindate},
            </if>
            <if test="contractEnddate != null ">
            contract_enddate = #{contractEnddate},
            </if>
            <if test="contract != null ">
            contract = #{contract},
            </if>
            <if test="payVoucher != null ">
            pay_voucher = #{payVoucher},
            </if>
            <if test="attachment != null ">
            attachment = #{attachment},
            </if>
            <if test="createOp != null ">
            create_op = #{createOp},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
            <if test="updateOp != null ">
            update_op = #{updateOp},
            </if>
            <if test="updateTime != null ">
            update_time = #{updateTime},
            </if>
            <if test="status != null ">
            status = #{status},
            </if>
            <if test="isDelete != null ">
            is_delete = #{isDelete},
            </if>
            <if test="companyId != null ">
            company_id = #{companyId},
            </if>
            <if test="remark != null ">
            remark = #{remark},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_cloud_dealer_gt_fxs_contract_info
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_contract_info
        WHERE id = #{id}
    </select>

</mapper>