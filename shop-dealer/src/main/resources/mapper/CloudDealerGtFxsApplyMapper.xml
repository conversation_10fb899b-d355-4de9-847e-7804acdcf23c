<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.dealer.dao.CloudDealerGtFxsApplyDao">

    <sql id="baseColumn">
        id,
        contract_code,
        channel_code,
        distribution_code,
        distribution_apellation,
        distribution_name,
        type,
        branch_type,
        telephone,
        address,
        task_num,
        contract_begindate,
        contract_enddate,
        contract,
        pay_voucher,
        attachment,
        local_area,
        higher_type,
        higher_code,
        team_type,
        connect_name,
        create_op,
        create_time,
        update_op,
        update_time,
        status,
        audit_time,
        company_id,
        province,
        city,
        district,
        township,
        region,
        street,
        stores_img,
        remark,
        license_img,
        legal_code,
        license_code,
        area_code,
        contract_box,
        first_order_amount,
        deputy_code,
        before_change_id
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.common.model.CloudDealerGtFxsApplyModel">
        <id property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="channelCode" column="channel_code"/>
        <result property="distributionCode" column="distribution_code"/>
        <result property="distributionApellation" column="distribution_apellation"/>
        <result property="distributionName" column="distribution_name"/>
        <result property="type" column="type"/>
        <result property="branchType" column="branch_type"/>
        <result property="telephone" column="telephone"/>
        <result property="address" column="address"/>
        <result property="taskNum" column="task_num"/>
        <result property="contractBegindate" column="contract_begindate"/>
        <result property="contractEnddate" column="contract_enddate"/>
        <result property="contract" column="contract"/>
        <result property="payVoucher" column="pay_voucher"/>
        <result property="attachment" column="attachment"/>
        <result property="localArea" column="local_area"/>
        <result property="higherType" column="higher_type"/>
        <result property="higherCode" column="higher_code"/>
        <result property="teamType" column="team_type"/>
        <result property="connectName" column="connect_name"/>
        <result property="createOp" column="create_op"/>
        <result property="createTime" column="create_time"/>
        <result property="updateOp" column="update_op"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="companyId" column="company_id"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="township" column="township"/>
        <result property="region" column="region"/>
        <result property="street" column="street"/>
        <result property="storesImg" column="stores_img"/>
        <result property="remark" column="remark"/>
        <result property="licenseImg" column="license_img"/>
        <result property="legalCode" column="legal_code"/>
        <result property="licenseCode" column="license_code"/>
        <result property="areaCode" column="area_code"/>
        <result property="contractBox" column="contract_box"/>
        <result property="firstOrderAmount" column="first_order_amount"/>
        <result property="deputyCode" column="deputy_code"/>
    </resultMap>
    <sql id="selectiveWhere">
        <where>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="distributionCode != null and distributionCode != ''">
                and distribution_code = #{distributionCode}
            </if>
            <if test="distributionApellation != null and distributionApellation != ''">
                and distribution_apellation = #{distributionApellation}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="statusNotEq != null">
                and status != #{statusNotEq}
            </if>
            <if test="higherCode != null and higherCode != ''">
                and higher_code = #{higherCode}
            </if>
            <if test="licenseCode != null and licenseCode != ''">
                and license_code = #{licenseCode}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="deputyCode != null and deputyCode != ''">
                and deputy_code = #{deputyCode}
            </if>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>,
        case status when 4 then create_time when 3 then update_time when 2 then audit_time when 1 then audit_time end as showTime
        FROM t_cloud_dealer_gt_fxs_apply
        <include refid="selectiveWhere"/>
        order by id desc
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.common.model.CloudDealerGtFxsApplyModel" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO t_cloud_dealer_gt_fxs_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="contractCode != null ">
            contract_code,
        </if>
        <if test="channelCode != null ">
            channel_code,
        </if>
        <if test="distributionCode != null ">
            distribution_code,
        </if>
        <if test="distributionApellation != null ">
            distribution_apellation,
        </if>
        <if test="distributionName != null ">
            distribution_name,
        </if>
        <if test="type != null ">
            type,
        </if>
        <if test="branchType != null ">
            branch_type,
        </if>
        <if test="telephone != null ">
            telephone,
        </if>
        <if test="address != null ">
            address,
        </if>
        <if test="taskNum != null ">
            task_num,
        </if>
        <if test="contractBegindate != null ">
            contract_begindate,
        </if>
        <if test="contractEnddate != null ">
            contract_enddate,
        </if>
        <if test="contract != null ">
            contract,
        </if>
        <if test="payVoucher != null ">
            pay_voucher,
        </if>
        <if test="attachment != null ">
            attachment,
        </if>
        <if test="localArea != null ">
            local_area,
        </if>
        <if test="higherType != null ">
            higher_type,
        </if>
        <if test="higherCode != null ">
            higher_code,
        </if>
        <if test="teamType != null ">
            team_type,
        </if>
        <if test="connectName != null ">
            connect_name,
        </if>
        <if test="createOp != null ">
            create_op,
        </if>
        <if test="createTime != null ">
            create_time,
        </if>
        <if test="updateOp != null ">
            update_op,
        </if>
        <if test="updateTime != null ">
            update_time,
        </if>
        <if test="status != null ">
            status,
        </if>
        <if test="auditTime != null ">
            audit_time,
        </if>
        <if test="companyId != null ">
            company_id,
        </if>
        <if test="province != null ">
            province,
        </if>
        <if test="city != null ">
            city,
        </if>
        <if test="district != null ">
            district,
        </if>
        <if test="township != null ">
            township,
        </if>
        <if test="region != null ">
            region,
        </if>
        <if test="street != null ">
            street,
        </if>
        <if test="storesImg != null ">
            stores_img,
        </if>
        <if test="remark != null ">
            remark,
        </if>
        <if test="licenseImg != null and licenseImg !=''  ">
            license_img,
        </if>
        <if test="legalCode != null and legalCode !=''  ">
            legal_code,
        </if>
        <if test="licenseCode != null and licenseCode !=''  ">
            license_code,
        </if>
        <if test="areaCode != null and areaCode !=''  ">
            area_code,
        </if>
        <if test="contractBox != null">
            contract_box,
        </if>
        <if test="firstOrderAmount != null">
            first_order_amount,
        </if>
        <if test="beforeChangeId != null">
            before_change_id,
        </if>
        <if test="deputyCode != null">
            deputy_code
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="contractCode != null ">
            #{contractCode},
        </if>
        <if test="channelCode != null ">
            #{channelCode},
        </if>
        <if test="distributionCode != null ">
            #{distributionCode},
        </if>
        <if test="distributionApellation != null ">
            #{distributionApellation},
        </if>
        <if test="distributionName != null ">
            #{distributionName},
        </if>
        <if test="type != null ">
            #{type},
        </if>
        <if test="branchType != null ">
            #{branchType},
        </if>
        <if test="telephone != null ">
            #{telephone},
        </if>
        <if test="address != null ">
            #{address},
        </if>
        <if test="taskNum != null ">
            #{taskNum},
        </if>
        <if test="contractBegindate != null ">
            #{contractBegindate},
        </if>
        <if test="contractEnddate != null ">
            #{contractEnddate},
        </if>
        <if test="contract != null ">
            #{contract},
        </if>
        <if test="payVoucher != null ">
            #{payVoucher},
        </if>
        <if test="attachment != null ">
            #{attachment},
        </if>
        <if test="localArea != null ">
            #{localArea},
        </if>
        <if test="higherType != null ">
            #{higherType},
        </if>
        <if test="higherCode != null ">
            #{higherCode},
        </if>
        <if test="teamType != null ">
            #{teamType},
        </if>
        <if test="connectName != null ">
            #{connectName},
        </if>
        <if test="createOp != null ">
            #{createOp},
        </if>
        <if test="createTime != null ">
            #{createTime},
        </if>
        <if test="updateOp != null ">
            #{updateOp},
        </if>
        <if test="updateTime != null ">
            #{updateTime},
        </if>
        <if test="status != null ">
            #{status},
        </if>
        <if test="auditTime != null ">
            #{auditTime},
        </if>
        <if test="companyId != null ">
            #{companyId},
        </if>
        <if test="province != null ">
            #{province},
        </if>
        <if test="city != null ">
            #{city},
        </if>
        <if test="district != null ">
            #{district},
        </if>
        <if test="township != null ">
            #{township},
        </if>
        <if test="region != null ">
            #{region},
        </if>
        <if test="street != null ">
            #{street},
        </if>
        <if test="storesImg != null ">
            #{storesImg},
        </if>
        <if test="remark != null ">
            #{remark},
        </if>
        <if test="licenseImg != null and licenseImg !=''  ">
            #{licenseImg},
        </if>
        <if test="legalCode != null and legalCode !=''  ">
            #{legalCode},
        </if>
        <if test="licenseCode != null and licenseCode !=''  ">
            #{licenseCode},
        </if>
        <if test="areaCode != null and areaCode !=''  ">
            #{areaCode},
        </if>
        <if test="contractBox != null">
            #{contractBox},
        </if>
        <if test="firstOrderAmount != null">
            #{firstOrderAmount},
        </if>
        <if test="beforeChangeId != null">
            #{beforeChangeId},
        </if>
        <if test="deputyCode != null">
            #{deputyCode}
        </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.common.model.CloudDealerGtFxsApplyModel">
        UPDATE t_cloud_dealer_gt_fxs_apply
        <set>
            <if test="contractCode != null and contractCode != ''">
                contract_code = #{contractCode},
            </if>
            <if test="channelCode != null and channelCode != ''">
                channel_code = #{channelCode},
            </if>
            <if test="distributionCode != null and distributionCode != ''">
                distribution_code = #{distributionCode},
            </if>
            <if test="distributionApellation != null and distributionApellation != ''">
                distribution_apellation = #{distributionApellation},
            </if>
            <if test="distributionName != null and distributionName != ''">
                distribution_name = #{distributionName},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="branchType != null ">
                branch_type = #{branchType},
            </if>
            <if test="telephone != null and telephone != ''">
                telephone = #{telephone},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="taskNum != null and taskNum != ''">
                task_num = #{taskNum},
            </if>
            <if test="contractBegindate != null and contractBegindate != ''">
                contract_begindate = #{contractBegindate},
            </if>
            <if test="contractEnddate != null and contractEnddate != ''">
                contract_enddate = #{contractEnddate},
            </if>
            <if test="contract != null and contract != ''">
                contract = #{contract},
            </if>
            <if test="payVoucher != null and payVoucher != ''">
                pay_voucher = #{payVoucher},
            </if>
            <if test="attachment != null and attachment != ''">
                attachment = #{attachment},
            </if>
            <if test="localArea != null and localArea != ''">
                local_area = #{localArea},
            </if>
            <if test="higherType != null and higherType != ''">
                higher_type = #{higherType},
            </if>
            <if test="higherCode != null and higherCode != ''">
                higher_code = #{higherCode},
            </if>
            <if test="teamType != null and teamType != ''">
                team_type = #{teamType},
            </if>
            <if test="connectName != null and connectName != ''">
                connect_name = #{connectName},
            </if>
            <if test="createOp != null and createOp != ''">
                create_op = #{createOp},
            </if>
            <if test="createTime != null ">
                create_time = #{createTime},
            </if>
            <if test="updateOp != null and updateOp != ''">
                update_op = #{updateOp},
            </if>
            <if test="updateTime != null ">
                update_time = #{updateTime},
            </if>
            <if test="status != null ">
                status = #{status},
            </if>
            <if test="auditTime != null ">
                audit_time = #{auditTime},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId},
            </if>
            <if test="province != null and province != ''">
                province = #{province},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
            <if test="district != null and district != ''">
                district = #{district},
            </if>
            <if test="township != null and township != ''">
                township = #{township},
            </if>
            <if test="region != null and region != ''">
                region = #{region},
            </if>
            <if test="street != null and street != ''">
                street = #{street},
            </if>
            <if test="storesImg != null and storesImg != ''">
                stores_img = #{storesImg},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="licenseImg != null and licenseImg !=''  ">
                license_img = #{licenseImg},
            </if>
            <if test="legalCode != null and legalCode !=''  ">
                legal_code = #{legalCode},
            </if>
            <if test="licenseCode != null and licenseCode !=''  ">
                license_code = #{licenseCode},
            </if>
            <if test="areaCode != null and areaCode !=''  ">
                area_code = #{areaCode},
            </if>
            <if test="contractBox != null">
                contract_box = #{contractBox},
            </if>
            <if test="firstOrderAmount != null">
                first_order_amount = #{firstOrderAmount},
            </if>
            <if test="deputyCode != null">
                deputy_code = #{deputyCode}
            </if>
        </set>
        WHERE id = #{id}
    </update>
    <update id="audit">
        update t_cloud_dealer_gt_fxs_apply set status = #{status},audit_time = now()
        where status = 3 and distribution_code = #{distributionCode} and company_id = #{companyId}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_cloud_dealer_gt_fxs_apply
        WHERE id = #{id}
    </delete>

    <!--根据分销商编码删除记录-->
    <delete id="deleteCloudDealerGtFxsApplyByDealerCode">
        delete
        from
        t_cloud_dealer_gt_fxs_apply
        where
        distribution_code = #{dealerCode}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        WHERE id = #{id}
    </select>

    <select id="getInfoByPhone" resultMap="baseResultMap">
        select
            <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        <where>
            <if test="telephone!='' and telephone!=null">
                and telephone = #{telephone}
            </if>
            <if test="licenseCode!='' and licenseCode!=null">
                and license_code != #{licenseCode}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
            <if test="channelCode!='' and channelCode!=null">
                and channel_code = #{channelCode}
            </if>
        </where>
    </select>

    <select id="getInfo" resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        <where>
            is_delete = 0
            and status = 1
            <if test="telephone!='' and telephone!=null">
                and telephone = #{telephone}
            </if>
            <if test="licenseCode!='' and licenseCode!=null">
                and license_code = #{licenseCode}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
            <if test="channelCode!='' and channelCode!=null">
                and channel_code = #{channelCode}
            </if>
        </where>
    </select>

    <select id="getInfoByCode" resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        <where>
            <if test="telephone!='' and telephone!=null">
                and telephone != #{telephone}
            </if>
            <if test="licenseCode!='' and licenseCode!=null">
                and license_code = #{licenseCode}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
            <if test="channelCode!='' and channelCode!=null">
                and channel_code = #{channelCode}
            </if>
        </where>
    </select>

    <select id="selectByPhoneCode"  resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        <where>
            <if test="telephone!='' and telephone!=null">
                and telephone = #{telephone}
            </if>
            <if test="licenseCode!='' and licenseCode!=null">
                and license_code = #{licenseCode}
            </if>
            and status in (1,2)
            <if test="channelCode!='' and channelCode!=null">
                and channel_code = #{channelCode}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
        </where>
    </select>

    <select id="findByPhoneCode"  resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        <where>
            <if test="telephone!='' and telephone!=null">
                and telephone = #{telephone}
            </if>
            <if test="licenseCode!='' and licenseCode!=null">
                and license_code = #{licenseCode}
            </if>
            and status = 1
            <if test="channelCode!='' and channelCode!=null">
                and channel_code = #{channelCode}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
        </where>
    </select>

    <select id="selectByStatusCode"  resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        <where>
            <if test="telephone!='' and telephone!=null">
                and telephone = #{telephone}
            </if>
            <if test="licenseCode!='' and licenseCode!=null">
                and license_code = #{licenseCode}
            </if>
            <if test="status!='' and status!=null">
                and status = 1
            </if>
            <if test="channelCode!='' and channelCode!=null">
                and channel_code = #{channelCode}
            </if>
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
            and is_delete = 0
        </where>
    </select>

    <update id="updateDeleteFlag">
        update t_cloud_dealer_gt_fxs_apply
        set is_delete = #{isDelete}
        where id = #{id}
    </update>

    <select id="getInfoById" resultMap="baseResultMap">
        select
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_fxs_apply
        where before_change_id = #{beforeChangeId}
    </select>
</mapper>
