<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.dealer.dao.DealerRewardRecordDao">

    <sql id="baseColumn">
        id,
        transaction,
        purchase_balance_id,
        code_count,
        amount,
        score,
        is_delete,
        remark,
        source_order_code,
        activity_id,
        convert_time,
        prize_type,
        prize_name,
        prize_num,
        earning_type,
        dealer_user_id,
        dealer_id,
        status,
        company_id,
        create_time,
        do_time,
        reward_type
    </sql>

    <sql id="dealerBaseColumn">
        id,
        type,
        dealer_name,
        phone,
        company_id,
        provinces,
        city,
        district,
        superior_id
    </sql>

    <sql id="joinColumn">
        tcdrr.id,
        tcdrr.transaction,
        tcdrr.purchase_balance_id,
        tcdrr.code_count,
        tcdrr.amount,
        tcdrr.score,
        tcdrr.is_delete,
        tcdrr.remark,
        tcdrr.source_order_code,
        tcdrr.activity_id,
        tcdrr.convert_time,
        tcdrr.prize_type,
        tcdrr.prize_name,
        tcdrr.prize_num,
        tcdrr.earning_type,
        tcdrr.dealer_user_id,
        tcdrr.dealer_id,
        tcdrr.status,
        tcdrr.company_id,
        tcdrr.create_time,
        tcdi.dealer_name,
        tcdi.phone,
        a.goods_name,
        a.bottle_num,
        tcdbar.create_time as scan_date,
        tcdbar.bus_area_name,
        tcdt.type_name,
        tcda.name as activity_name
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.dealer.model.DealerRewardRecordModel">
            <id property="id" column="id"/>
            <result property="transaction" column="transaction"/>
            <result property="purchaseBalanceId" column="purchase_balance_id"/>
            <result property="codeCount" column="code_count"/>
            <result property="amount" column="amount"/>
            <result property="score" column="score"/>
            <result property="isDelete" column="is_delete"/>
            <result property="remark" column="remark"/>
            <result property="sourceOrderCode" column="source_order_code"/>
            <result property="activityId" column="activity_id"/>
            <result property="convertTime" column="convert_time"/>
            <result property="prizeType" column="prize_type"/>
            <result property="prizeName" column="prize_name"/>
            <result property="prizeNum" column="prize_num"/>
            <result property="earningType" column="earning_type"/>
            <result property="dealerUserId" column="dealer_user_id"/>
            <result property="dealerId" column="dealer_id"/>
            <result property="status" column="status"/>
            <result property="companyId" column="company_id"/>
            <result property="createTime" column="create_time"/>
            <result property="doTime" column="do_time"/>
    </resultMap>

    <resultMap id="joinResultMap" type="com.intelliquor.cloud.shop.dealer.model.resp.DealerRewardRecordResp">
        <result property="companyId" column="company_id"/>
        <result property="amount" column="amount"/>
        <result property="prizeType" column="prize_type"/>
        <result property="prizeName" column="prize_name"/>
        <result property="prizeNum" column="prize_num"/>
        <result property="dealerName" column="dealer_name"/>
        <result property="phone" column="phone"/>
        <result property="goodName" column="good_name"/>
        <result property="bottleNum" column="bottle_num"/>
        <result property="scanDate" column="scan_date"/>
        <result property="dealerArea" column="dealer_area"/>
        <result property="dealerAreaName" column="dealer_area_name"/>
        <result property="dealerTypeName" column="dealer_type_name"/>
        <result property="activityName" column="activity_name"/>
        <result property="activityCode" column="activity_code"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
            <if test="dealerId != null">
                and dealer_id = #{dealerId}
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="earningType != null">
                and earning_type = #{earningType}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="prizeType != null and prizeType == 1">
                and amount >0
            </if>
            <if test="prizeType != null and prizeType == 2">
                and score >0
            </if>
            <if test="earningTypeIn != null and earningTypeIn.size()>0">
                and earning_type in
                <foreach item="item" index="index" collection="earningTypeIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="selectiveJoinWhere">
        <where>
            <if test="isDelete != null">
                and tcdrr.is_delete = #{isDelete}
            </if>
            <if test="dealerId != null">
                and tcdrr.dealer_id = #{dealerId}
            </if>
            <if test="companyId != null">
                and tcdrr.company_id = #{companyId}
            </if>
            <if test="earningType != null">
                and tcdrr.earning_type = #{earningType}
            </if>
            <if test="prizeType != null">
                and tcdrr.prize_type = #{prizeType}
            </if>
            <if test="dealerName != null and dealerName != ''">
                and tcdi.dealer_name like concat('%', #{dealerName}, '%')
            </if>
            <if test="distributorName != null and distributorName != ''">
                and tcdi.dealer_name like concat('%', #{distributorName}, '%')
            </if>
            <if test="phone != null and phone != ''">
                and tcdi.phone like concat('%', #{phone}, '%')
            </if>
            <if test="activityName != null and activityName != ''">
                and tcda.name like concat('%', #{activityName}, '%')
            </if>
            <if test="activityCode != null and activityCode != ''">
                and tcda.code like concat('%', #{activityCode}, '%')
            </if>
            <if test="startScanDate != null and startScanDate != ''">
                 and tcdrsb.create_time >= #{startScanDate}
            </if>
            <if test="endScanDate != null and endScanDate != ''">
                and #{endScanDate} >= tcdrsb.create_time
            </if>
            <if test="busAreaId != null and busAreaId != '' ">
                and tcdi.dealer_area = #{busAreaId}
            </if>
            <if test="type != null">
                and tcdi.type = #{type}
            </if>
        </where>
    </sql>

    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_reward_record_#{companyId}
        <include refid="selectiveWhere"/>
        ORDER BY create_time desc
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.dealer.model.DealerRewardRecordModel"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_cloud_dealer_reward_record_#{companyId}
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="transaction != null ">
            transaction,
            </if>
            <if test="purchaseBalanceId != null ">
            purchase_balance_id,
            </if>
            <if test="codeCount != null ">
            code_count,
            </if>
            <if test="amount != null ">
            amount,
            </if>
            <if test="score != null ">
            score,
            </if>
            <if test="isDelete != null ">
            is_delete,
            </if>
            <if test="remark != null ">
            remark,
            </if>
            <if test="sourceOrderCode != null ">
            source_order_code,
            </if>
            <if test="activityId != null ">
            activity_id,
            </if>
            <if test="convertTime != null ">
            convert_time,
            </if>
            <if test="doTime != null ">
            do_time,
            </if>
            <if test="prizeType != null ">
            prize_type,
            </if>
            <if test="prizeName != null ">
            prize_name,
            </if>
            <if test="prizeNum != null ">
            prize_num,
            </if>
            <if test="earningType != null ">
            earning_type,
            </if>
            <if test="dealerUserId != null ">
            dealer_user_id,
            </if>
            <if test="dealerId != null ">
            dealer_id,
            </if>
            <if test="status != null ">
            status,
            </if>
            <if test="companyId != null ">
            company_id,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="rewardType !=null ">
                reward_type,
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="transaction != null ">
            #{transaction},
            </if>
            <if test="purchaseBalanceId != null ">
            #{purchaseBalanceId},
            </if>
            <if test="codeCount != null ">
            #{codeCount},
            </if>
            <if test="amount != null ">
            #{amount},
            </if>
            <if test="score != null ">
            #{score},
            </if>
            <if test="isDelete != null ">
            #{isDelete},
            </if>
            <if test="remark != null ">
            #{remark},
            </if>
            <if test="sourceOrderCode != null ">
            #{sourceOrderCode},
            </if>
            <if test="activityId != null ">
            #{activityId},
            </if>
            <if test="convertTime != null ">
            #{convertTime},
            </if>
            <if test="doTime != null ">
            #{doTime},
            </if>
            <if test="prizeType != null ">
            #{prizeType},
            </if>
            <if test="prizeName != null ">
            #{prizeName},
            </if>
            <if test="prizeNum != null ">
            #{prizeNum},
            </if>
            <if test="earningType != null ">
            #{earningType},
            </if>
            <if test="dealerUserId != null ">
            #{dealerUserId},
            </if>
            <if test="dealerId != null ">
            #{dealerId},
            </if>
            <if test="status != null ">
            #{status},
            </if>
            <if test="companyId != null ">
            #{companyId},
            </if>
            <if test="createTime != null ">
            #{createTime}
            </if>
            <if test="rewardType !=null ">
            #{rewardType},
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.dealer.model.DealerRewardRecordModel">
        UPDATE t_cloud_dealer_reward_record_#{companyId}
        <set>
            <if test="transaction != null ">
            transaction = #{transaction},
            </if>
            <if test="purchaseBalanceId != null ">
            purchase_balance_id = #{purchaseBalanceId},
            </if>
            <if test="codeCount != null ">
            code_count = #{codeCount},
            </if>
            <if test="amount != null ">
            amount = #{amount},
            </if>
            <if test="score != null ">
            score = #{score},
            </if>
            <if test="isDelete != null ">
            is_delete = #{isDelete},
            </if>
            <if test="remark != null ">
            remark = #{remark},
            </if>
            <if test="sourceOrderCode != null ">
            source_order_code = #{sourceOrderCode},
            </if>
            <if test="activityId != null ">
            activity_id = #{activityId},
            </if>
            <if test="convertTime != null ">
            convert_time = #{convertTime},
            </if>
            <if test="prizeType != null ">
            prize_type = #{prizeType},
            </if>
            <if test="prizeName != null ">
            prize_name = #{prizeName},
            </if>
            <if test="prizeNum != null ">
            prize_num = #{prizeNum},
            </if>
            <if test="earningType != null ">
            earning_type = #{earningType},
            </if>
            <if test="dealerUserId != null ">
            dealer_user_id = #{dealerUserId},
            </if>
            <if test="dealerId != null ">
            dealer_id = #{dealerId},
            </if>
            <if test="status != null ">
            status = #{status},
            </if>
            <if test="companyId != null ">
            company_id = #{companyId},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_cloud_dealer_reward_record
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_reward_record_#{companyId}
        WHERE id = #{id}
    </select>

    <!-- 获取经销商/分销商待结算收益 -->
    <select id="sumDealerUnsettledProfit" resultType="java.math.BigDecimal">
        select ifnull(sum(amount), 0) from t_cloud_dealer_reward_record_#{companyId}
        where dealer_id = #{dealerId} and is_delete = 1 and status = 0
    </select>

    <!-- 获取经销商/分销商待结算积分 -->
    <select id="sumDealerUnsettledScore" resultType="int">
        select ifnull(sum(score), 0) from t_cloud_dealer_reward_record_#{companyId}
        where dealer_id = #{dealerId} and is_delete = 1 and status = 0
    </select>

    <!-- 获取经销奖励列表 -->
    <select id="queryDealerRewardList" resultMap="joinResultMap">
        SELECT
            tcdi.company_id,
            tcdrr.amount,
            tcdrr.score,
            tcdrr.prize_type,
            (case prize_type when 1 then tcdrr.amount when 2 then tcdrr.score else tcdrr.prize_name end) prize_name,
            tcdrr.prize_num,
            tcdi.dealer_name,
            tcdi.phone,
            a.goods_name as good_name,
            a.bottle_num,
            tcdrsb.create_time as scan_date,
            tcdi.dealer_area,
            tcdt.type_name as dealer_type_name,
            tcda.name as activity_name,
            tcda.code as activity_code
        FROM
            t_cloud_dealer_reward_record_#{companyId} tcdrr
        left join t_cloud_dealer_info tcdi on tcdrr.dealer_id = tcdi.id and type = 1
        left join t_cloud_dealer_reward_scan_balance_#{companyId} tcdrsb on tcdrr.purchase_balance_id = tcdrsb.id
        left join (
            select
                d.balance_id,
                d.goods_code ,
                d.goods_name,
                IFNULL(sum(d.quantity) ,0)as bottle_num
            from  t_cloud_dealer_scan_code_detail_#{companyId} d
            group by d.balance_id
        ) a on tcdrr.purchase_balance_id = a.balance_id
        left join t_cloud_dealer_type tcdt on tcdi.dealer_type = tcdt.id
        left join t_cloud_dealer_activity tcda on tcdrr.activity_id = tcda.id
        <include refid="selectiveJoinWhere"/>
        ORDER BY tcdrr.create_time desc
    </select>

    <!-- 获取分销商奖励列表 -->
    <select id="queryDistributorRewardList" resultType="com.intelliquor.cloud.shop.dealer.model.resp.DistributorRewardRecordResp">
        SELECT
            tcdrr.id,
            tcdi.company_id,
            tcdrr.amount,
            tcdrr.score,
            tcdrr.prize_type,
            (case prize_type when 1 then tcdrr.amount when 2 then tcdrr.score else tcdrr.prize_name end) prize_name,
            tcdrr.prize_num,
            tcdi.dealer_name as distributorName,
            tcdi.phone,
            tcdrsb.create_time as scan_date,
            tcdi.dealer_area,
            tcdt.type_name as dealer_type_name,
            tcda.name as activity_name,
            tcda.code as activity_code
        FROM
            t_cloud_dealer_reward_record_#{companyId} tcdrr
        left join
        <choose>
            <when test="areaModelList != null">
                (
                <foreach collection="areaModelList" item="item" index="index" separator="union">
                    select <include refid="dealerBaseColumn"></include> from t_cloud_dealer_info
                    <where>
                        <if test="item.province != null and item.province != ''">
                            and provinces = #{item.province}
                        </if>
                        <if test="item.city != null and item.city != ''">
                            and city = #{item.city}
                        </if>
                        <if test="item.county != null and item.county != ''">
                            and district = #{item.county}
                        </if>
                    </where>
                </foreach>
                )
            </when>
            <otherwise>
                t_cloud_dealer_info
            </otherwise>
        </choose>
         tcdi on tcdrr.dealer_id = tcdi.id and type = 2
        left join t_cloud_dealer_info tcdip on tcdi.superior_id = tcdip.id
        left join t_cloud_dealer_reward_scan_balance_#{companyId} tcdrsb on tcdrr.purchase_balance_id = tcdrsb.id

        <!--left join (
            select
            d.balance_id,
            d.goods_code ,
            d.goods_name,
            IFNULL(sum(d.quantity) ,0)as bottle_num
            from t_cloud_dealer_scan_code_detail_#{companyId} d
            group by d.balance_id
        ) a on tcdrr.purchase_balance_id = a.balance_id-->
        left join t_cloud_dealer_type tcdt on tcdip.dealer_type = tcdt.id
        left join t_cloud_dealer_activity tcda on tcdrr.activity_id = tcda.id
        <where>
            <if test="type != null">
                and tcdi.type = #{type}
            </if>
            <if test="isDelete != null">
                and tcdrr.is_delete = #{isDelete}
            </if>
            <if test="companyId != null">
                and tcdrr.company_id = #{companyId}
            </if>
            <if test="earningType != null">
                and tcdrr.earning_type = #{earningType}
            </if>
            <if test="prizeType != null">
                and tcdrr.prize_type = #{prizeType}
            </if>
            <if test="distributorName != null and distributorName != ''">
                and tcdi.dealer_name like concat('%', #{distributorName}, '%')
            </if>
            <if test="phone != null and phone != ''">
                and tcdi.phone like concat('%', #{phone}, '%')
            </if>
            <if test="activityName != null and activityName != ''">
                and tcda.name like concat('%', #{activityName}, '%')
            </if>
            <if test="activityCode != null and activityCode != ''">
                and tcda.code like concat('%', #{activityCode}, '%')
            </if>
            <if test="startScanDate != null and startScanDate != ''">
                and tcdrsb.create_time >= #{startScanDate}
            </if>
            <if test="endScanDate != null and endScanDate != ''">
                and #{endScanDate} >= tcdrsb.create_time
            </if>
            <if test="busAreaId != null and busAreaId != '' ">
                and tcdi.dealer_area = #{busAreaId}
            </if>
        </where>
        ORDER BY tcdrr.create_time desc
    </select>

    <select id="selectByTime" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_reward_record_#{companyId}
        where status = 0 and do_time &lt;= #{time}
    </select>

    <select id="getSavourSingleList" resultType="com.intelliquor.cloud.shop.dealer.model.resp.SavourWineProductResp">
        SELECT
            b.id AS balanceId,
            count( 1 ) AS quantity,
            a.create_time as createTime,
            c.`name` AS staffName,
            c.id AS staffId,
            b.`transaction` as singleNumber
        FROM
            t_cloud_dealer_scan_code_detail_#{companyId} a
            left join t_cloud_dealer_reward_scan_balance_#{companyId} b on a.balance_id = b.id
            left join t_cloud_dealer_staff c on a.dealer_staff_id = c.id
        <where>
            a.is_tast = 1
            <if test="dealerId!=null and dealerId !='' ">
                and a.dealer_id = #{dealerId}
            </if>
            <if test="startTime!=null and startTime !='' ">
                and a.create_time >= #{startTime}
            </if>
            <if test="endTime!=null and endTime !='' ">
                and a.create_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY
            a.balance_id
        ORDER BY a.create_time desc
    </select>

    <resultMap id="wareDetailsCollection" type="com.intelliquor.cloud.shop.dealer.model.resp.WareDetailsResp">
        <id property="id" column="id" ></id>
        <result property="singleNumber" column="transaction" ></result>
        <collection property="savourWineProductModelList" ofType="com.intelliquor.cloud.shop.dealer.model.SavourWineProductModel" >
            <result property="goodsName" column="goods_name" ></result>
            <result property="quantity" column="quantity" ></result>
            <result property="balanceId" column="balance_id" ></result>
            <result property="codePing" column="code_ping" ></result>
        </collection>
    </resultMap>

    <select id="getWareDetails" resultMap="wareDetailsCollection">
        SELECT
            a.id as id,
            a.`transaction` ,
            b.goods_name ,
            b.quantity ,
            b.balance_id,
            b.code_ping
        FROM
            t_cloud_dealer_reward_scan_balance_1 a
            LEFT JOIN t_cloud_dealer_scan_code_detail_1 b ON  a.id = b.balance_id
        WHERE
            a.id = #{balanceId}
            and  b.company_id = #{companyId}
    </select>

    <select id="getDealerScoreDetails" resultType="com.intelliquor.cloud.shop.dealer.model.resp.DealerScoreResp">
        SELECT
            b.id as dealerId,
            b.dealer_name AS dealerName,
            s.phone AS phone,
            a.earning_type AS type,
            ( CASE a.earning_type WHEN 1 THEN "获取" WHEN 3 THEN "消耗" END ) AS typeStr,
            a.score AS score,
            a.create_time AS createTime
        FROM
            t_cloud_dealer_reward_record_#{companyId} a
                LEFT JOIN t_cloud_dealer_info b ON a.dealer_id = b.id
                left join t_cloud_dealer_staff s on a.dealer_id = s.dealer_id
        WHERE
            a.is_delete = 1
          AND a.prize_type = 2
          AND a.company_id = #{companyId}
          AND s.company_id = #{companyId}
          AND b.company_id = #{companyId}
          AND s.primary_account = 1
        <if test="dealerName!=null and dealerName !='' ">
            and b.dealer_name like concat('%', #{dealerName}, '%')
        </if>
        <if test="phone!=null and phone !='' ">
            and s.phone = #{phone}
        </if>
        <if test="type!=null and type !='' ">
            and a.earning_type = #{type}
        </if>
        <if test="startTime!=null and startTime !='' ">
            and a.create_time >= #{startTime}
        </if>
        <if test="endTime!=null and endTime !='' ">
            and a.create_time &lt;= #{endTime}
        </if>
        <if test="customerId!=null and customerId !='' ">
            and s.id = #{customerId}
        </if>

    </select>


    <!--查询数据-->
    <select id="getScanData" resultType="com.intelliquor.cloud.shop.dealer.model.resp.DealerScanDataResp">

        select
        d.reward_id,
        d.goods_name as goods_name,
        sum(d.quantity) as bottle_num

        from t_cloud_dealer_scan_code_detail_#{companyId} d
        where d.reward_id in(

        <foreach collection="list" item="item" index= "index" separator =",">
           #{item}

        </foreach>
        )

        group by d.reward_id,d.goods_name ,d.reward_id
    </select>

</mapper>