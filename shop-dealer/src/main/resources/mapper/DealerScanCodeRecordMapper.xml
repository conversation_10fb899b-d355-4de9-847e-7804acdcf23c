<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.dealer.dao.DealerScanCodeRecordDao">

    <sql id="baseColumn">
        id,
        qrcode,
        type,
        remark,
        create_time,
        code_type,
        lon,
        lat,
        company_id,
        dealer_id
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.dealer.model.DealerScanCodeRecordModel">
            <id property="id" column="id"/>
            <result property="qrcode" column="qrcode"/>
            <result property="type" column="type"/>
            <result property="remark" column="remark"/>
            <result property="createTime" column="create_time"/>
            <result property="codeType" column="code_type"/>
            <result property="lon" column="lon"/>
            <result property="lat" column="lat"/>
            <result property="companyId" column="company_id"/>
            <result property="dealerId" column="dealer_id"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="dealerId != null and dealerId != ''">
                and dealer_id = #{dealerId}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_scan_code_record
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.dealer.model.DealerScanCodeRecordModel">
        INSERT INTO t_cloud_dealer_scan_code_record_#{companyId}
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="qrcode != null ">
            qrcode,
            </if>
            <if test="type != null ">
            type,
            </if>
            <if test="remark != null ">
            remark,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="codeType != null ">
            code_type,
            </if>
            <if test="lon != null ">
            lon,
            </if>
            <if test="lat != null ">
            lat,
            </if>
            <if test="companyId != null ">
            company_id,
            </if>
            <if test="dealerId != null ">
            dealer_id,
            </if>
            <if test="dealerStaffId != null ">
            dealer_staff_id,
            </if>
        <if test="scanType != null ">
            scan_type,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="qrcode != null ">
            #{qrcode},
            </if>
            <if test="type != null ">
            #{type},
            </if>
            <if test="remark != null ">
            #{remark},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="codeType != null ">
            #{codeType},
            </if>
            <if test="lon != null ">
            #{lon},
            </if>
            <if test="lat != null ">
            #{lat},
            </if>
            <if test="companyId != null ">
            #{companyId},
            </if>
            <if test="dealerId != null ">
            #{dealerId},
            </if>
            <if test="dealerStaffId != null ">
                #{dealerStaffId}
            </if>
        <if test="scanType != null ">
            #{scanType},
        </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.dealer.model.DealerScanCodeRecordModel">
        UPDATE t_cloud_dealer_scan_code_record
        <set>
            <if test="qrcode != null ">
            qrcode = #{qrcode},
            </if>
            <if test="type != null ">
            type = #{type},
            </if>
            <if test="remark != null ">
            remark = #{remark},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
            <if test="codeType != null ">
            code_type = #{codeType},
            </if>
            <if test="lon != null ">
            lon = #{lon},
            </if>
            <if test="lat != null ">
            lat = #{lat},
            </if>
            <if test="companyId != null ">
            company_id = #{companyId},
            </if>
            <if test="dealerId != null ">
            dealer_id = #{dealerId},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_cloud_dealer_scan_code_record
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_scan_code_record
        WHERE id = #{id}
    </select>

</mapper>