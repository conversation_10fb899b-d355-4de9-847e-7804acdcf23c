<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.dealer.dao.CloudDealerGtGwExtraInfoDao">

    <sql id="baseColumn">
        id,
        apply_id,
        dealer_id,
        dealer_code,
        dealer_name,
        fxs_type,
        branch_type,
        local_area,
        team_type,
        create_op,
        create_time,
        update_op,
        update_time,
        status,
        is_delete,
        company_id,
        region,
        street,
        remark,
        legal_code,
        license_code,
        area_code
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.dealer.model.CloudDealerGtGwExtraInfoModel">
        <id property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="dealerId" column="dealer_id"/>
        <result property="dealerCode" column="dealer_code"/>
        <result property="dealerName" column="dealer_name"/>
        <result property="fxsType" column="fxs_type"/>
        <result property="branchType" column="branch_type"/>
        <result property="localArea" column="local_area"/>
        <result property="teamType" column="team_type"/>
        <result property="createOp" column="create_op"/>
        <result property="createTime" column="create_time"/>
        <result property="updateOp" column="update_op"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="isDelete" column="is_delete"/>
        <result property="companyId" column="company_id"/>
        <result property="region" column="region"/>
        <result property="street" column="street"/>
        <result property="remark" column="remark"/>
        <result property="legalCode" column="legal_code"/>
        <result property="licenseCode" column="license_code"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            is_delete = 1
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="dealerName != null and dealerName != ''">
                and dealer_name = #{dealerName}
            </if>
            <if test="licenseCode != null and licenseCode != ''">
                and license_code = #{licenseCode}
            </if>
            <if test="drinkCode != null and drinkCode != ''">
                and dealer_code = #{drinkCode}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{phone}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_gw_extra_info
        <include refid="selectiveWhere"/>
        ORDER BY id desc
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.dealer.model.CloudDealerGtGwExtraInfoModel">
        INSERT INTO t_cloud_dealer_gt_gw_extra_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="applyId != null ">
            apply_id,
        </if>
        <if test="dealerId != null ">
            dealer_id,
        </if>
        <if test="dealerCode != null ">
            dealer_code,
        </if>
        <if test="dealerName != null ">
            dealer_name,
        </if>
        <if test="fxsType != null ">
            fxs_type,
        </if>
        <if test="branchType != null ">
            branch_type,
        </if>
        <if test="localArea != null ">
            local_area,
        </if>
        <if test="teamType != null ">
            team_type,
        </if>
        <if test="createOp != null ">
            create_op,
        </if>
        <if test="createTime != null ">
            create_time,
        </if>
        <if test="updateOp != null ">
            update_op,
        </if>
        <if test="updateTime != null ">
            update_time,
        </if>
        <if test="status != null ">
            status,
        </if>
        <if test="isDelete != null ">
            is_delete,
        </if>
        <if test="companyId != null ">
            company_id,
        </if>
        <if test="region != null ">
            region,
        </if>
        <if test="street != null ">
            street,
        </if>
        <if test="remark != null ">
            remark,
        </if>
        <if test="legalCode != null and legalCode !=''  ">
            legal_code,
        </if>
        <if test="licenseCode != null and licenseCode !=''  ">
            license_code,
        </if>
        <if test="areaCode != null and areaCode !=''  ">
            area_code
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        <if test="applyId != null ">
            #{applyId},
        </if>
        <if test="dealerId != null ">
            #{dealerId},
        </if>
        <if test="dealerCode != null ">
            #{dealerCode},
        </if>
        <if test="dealerName != null ">
            #{dealerName},
        </if>
        <if test="fxsType != null ">
            #{fxsType},
        </if>
        <if test="branchType != null ">
            #{branchType},
        </if>
        <if test="localArea != null ">
            #{localArea},
        </if>
        <if test="teamType != null ">
            #{teamType},
        </if>
        <if test="createOp != null ">
            #{createOp},
        </if>
        <if test="createTime != null ">
            #{createTime},
        </if>
        <if test="updateOp != null ">
            #{updateOp},
        </if>
        <if test="updateTime != null ">
            #{updateTime},
        </if>
        <if test="status != null ">
            #{status},
        </if>
        <if test="isDelete != null ">
            #{isDelete},
        </if>
        <if test="companyId != null ">
            #{companyId},
        </if>
        <if test="region != null ">
            #{region},
        </if>
        <if test="street != null ">
            #{street},
        </if>
        <if test="remark != null ">
            #{remark},
        </if>
        <if test="legalCode != null and legalCode !=''  ">
            #{legalCode},
        </if>
        <if test="licenseCode != null and licenseCode !=''  ">
            #{licenseCode},
        </if>
        <if test="areaCode != null and areaCode !=''  ">
            #{areaCode}
        </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.dealer.model.CloudDealerGtGwExtraInfoModel">
        UPDATE t_cloud_dealer_gt_gw_extra_info
        <set>
            <if test="dealerId != null ">
                dealer_id = #{dealerId},
            </if>
            <if test="dealerCode != null ">
                dealer_code = #{dealerCode},
            </if>
<!--            <if test="contractName != null ">-->
<!--                contract_name = #{contractName},-->
<!--            </if>-->
            <if test="fxsType != null ">
                fxs_type = #{fxsType},
            </if>
            <if test="branchType != null ">
                branch_type = #{branchType},
            </if>
            <if test="localArea != null ">
                local_area = #{localArea},
            </if>
            <if test="teamType != null ">
                team_type = #{teamType},
            </if>
            <if test="updateOp != null ">
                update_op = #{updateOp},
            </if>
            <if test="status != null ">
                status = #{status},
            </if>
            <if test="isDelete != null ">
                is_delete = #{isDelete},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId},
            </if>
            <if test="region != null ">
                region = #{region},
            </if>
            <if test="street != null ">
                street = #{street},
            </if>
            <if test="remark != null ">
                remark = #{remark},
            </if>
            <if test="legalCode != null and legalCode !=''  ">
                legal_code = #{legalCode},
            </if>
            <if test="licenseCode != null and licenseCode !=''  ">
                license_code = #{licenseCode},
            </if>
            update_time = now()
        </set>
        WHERE dealer_id = #{dealerId} and company_id = #{companyId}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_cloud_dealer_gt_gw_extra_info
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_cloud_dealer_gt_gw_extra_info
        WHERE id = #{id}
    </select>

</mapper>