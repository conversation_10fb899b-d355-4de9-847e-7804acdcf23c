package com.intelliquor.cloud.shop.dealer.service;

import com.intelliquor.cloud.shop.dealer.model.PotentialCustomersModel;
import com.intelliquor.cloud.shop.dealer.model.resp.PotentialCustomersResp;
import com.intelliquor.cloud.shop.dealer.model.resp.VisitRecordResp;
import com.intelliquor.cloud.shop.dealer.model.resp.VisitingObjectResp;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 20201-03-18
 * 业务员客户
 */
public interface DealerSalesmanCustomerService {

    /**
     * 功能描述: 查询客户列表
     *
     * @param mapUtils
     * @return com.intelliquor.cloud.shop.dealer.model.PotentialCustomersModel
     */
    List<PotentialCustomersModel> queryCustomerList(MapUtils mapUtils);

    /**
     * 获取业务员下的(拜访对象)
     * @param map
     * @return
     */
    List<VisitingObjectResp> queryStaffVisitCustomerList(Map map);
    /**
     * 功能描述: 查询未绑定客户列表
     *
     * @param
     * @return com.intelliquor.cloud.shop.dealer.model.PotentialCustomersModel
     */
    List<PotentialCustomersModel> queryUnboundCustomerList(Map<String,Object> map);


    /**
     * 新增绑定
     * @param customersIds
     * @return
     */
    void addBind(String customersIds, Integer visitorId,Integer dealerId,Integer companyId);


    /**
     * 解除绑定
     * @param customersId
     * @param visitorId
     * @param companyId
     * @return
     */
    void Unbind(Integer customersId, Integer visitorId, Integer companyId);



    /**
     * 变更业务员
     * @param customersId
     * @param staffId
     * @return
     */
    void changeSalesman(Integer customersId, Integer staffId, Integer dealerId , Integer companyId);


    /**
     * 查询客户详情接口
     * @param id
     * @return
     */
    PotentialCustomersModel queryById(Integer id);

    /**
     * 修改客户详情接口
     * @param model
     * @return
     */
    Integer update(PotentialCustomersModel model);

    /**
     * 查询所有的客户
     * @param param
     * @return
     */
    List<PotentialCustomersResp> queryAllList(Map<String, Object> param);

    /**
     *
     * @param model
     * @return
     */
    Integer insert(PotentialCustomersModel model);


    /**
     * 查询客户拜访列表
     * @param customerId
     * @return
     */
    List<VisitRecordResp> getCustomersRecordsList(Integer customerId,Integer dealerId);
}
