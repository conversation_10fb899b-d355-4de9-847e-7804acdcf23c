package com.intelliquor.cloud.shop.dealer.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.intelliquor.cloud.shop.common.model.resp.PageInfoResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: MAX
 * @CreateTime: 2023-03-17  15:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromotionalOrderPageResp implements Serializable {

    private static final long serialVersionUID = 6187381974415441063L;
    @JsonProperty("data")
    private List<PromotionalOrderInfo> list;

    /**
     * 应上账金额
     */
    @JsonProperty("should_reward")
    private BigDecimal totalAmount;

    /**
     * 已上账金额
     */
    @JsonProperty("has_reward")
    private BigDecimal accountAmount;

    /**
     * 分页信息
     */
    @JsonProperty("__paging")
    private PageInfoResp page;
}
