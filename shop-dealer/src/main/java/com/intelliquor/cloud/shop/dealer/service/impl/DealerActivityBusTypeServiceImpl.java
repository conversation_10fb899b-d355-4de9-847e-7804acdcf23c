package com.intelliquor.cloud.shop.dealer.service.impl;

import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.dealer.dao.DealerActivityBusTypeDao;
import com.intelliquor.cloud.shop.dealer.model.DealerActivityBusTypeModel;
import com.intelliquor.cloud.shop.dealer.service.DealerActivityBusTypeService;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2021/01/05 17:29
 * @Description: 云商扫码活动-业务区域持久层
 */
@Service("dealerActivityBusTypeService")
public class DealerActivityBusTypeServiceImpl implements DealerActivityBusTypeService {

    @Autowired
    private DealerActivityBusTypeDao dealerActivityBusTypeDao;

    /**
     * 功能描述: 列表
     *
     * @param param
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.DealerActivityBusTypeModel>
     * @auther: tms
     * @date: 2021/01/05 17:57
     */
    @Override
    public List<DealerActivityBusTypeModel> queryList(MapUtils param) {
        return dealerActivityBusTypeDao.queryList(param);
    }

    /**
     * 功能描述: 根据条件查询详情
     *
     * @param param
     * @return com.intelliquor.cloud.shop.dealer.model.DealerActivityBusTypeModel
     * @auther: tms
     * @date: 2021/01/05 17:58
     */
    @Override
    public DealerActivityBusTypeModel getByParam(MapUtils param) {
        return dealerActivityBusTypeDao.getByParam(param);
    }

    /**
     * 功能描述: 根据id获取详情
     *
     * @param id
     * @return com.intelliquor.cloud.shop.dealer.model.DealerActivityBusTypeModel
     * @auther: tms
     * @date: 2021/01/05 18:01
     */
    @Override
    public DealerActivityBusTypeModel getById(Integer id) {
        return dealerActivityBusTypeDao.getById(id);
    }

    /**
     * 功能描述: 添加
     *
     * @param dealerActivityModel
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/01/05 17:59
     */
    @Override
    public Integer insert(DealerActivityBusTypeModel dealerActivityModel) {
        return dealerActivityBusTypeDao.insert(dealerActivityModel);
    }

    /**
     * 功能描述: 去添加
     *
     * @param activityId
     * @param dealerActivityBusTypeModels
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/01/06 10:16
     */
    @Override
    public Integer goInsert(Integer activityId, List<DealerActivityBusTypeModel> dealerActivityBusTypeModels) {
        if (ObjectUtil.isNotEmpty(dealerActivityBusTypeModels)) {
            for (DealerActivityBusTypeModel busTypeModel : dealerActivityBusTypeModels) {
                busTypeModel.setActivityId(activityId);
                if (StringUtils.isEmpty(busTypeModel.getBusAreaList())) {
                    throw new BusinessException("业务区域未选择");
                }
                String[] areaIdArr = busTypeModel.getBusAreaList().split(",");
                StringBuilder areaStr = new StringBuilder();
                for (String areaId : areaIdArr) {
                    areaStr.append(".").append(areaId);
                }
                busTypeModel.setBusAreaRelation(areaStr.toString());
            }
            return dealerActivityBusTypeDao.insertBatch(dealerActivityBusTypeModels);
        }
        return 0;
    }

    /**
     * 功能描述: 修改
     *
     * @param dealerActivityModel
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/01/05 17:59
     */
    @Override
    public Integer update(DealerActivityBusTypeModel dealerActivityModel) {
        return dealerActivityBusTypeDao.update(dealerActivityModel);
    }

    /**
     * 功能描述: 去修改
     *
     * @param activityId
     * @param dealerActivityBusTypeModels
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/01/06 10:16
     */
    @Override
    public Integer goUpdate(Integer activityId, List<DealerActivityBusTypeModel> dealerActivityBusTypeModels) {
        // 1.根据活动id删除
        this.deleteByActivityId(activityId);

        // 2.去添加
        return this.goInsert(activityId, dealerActivityBusTypeModels);
    }

    /**
     * 功能描述: 根据活动id删除
     *
     * @param activityId
     * @return void
     * @auther: tms
     * @date: 2021/01/05 18:06
     */
    @Override
    public void deleteByActivityId(Integer activityId) {
        dealerActivityBusTypeDao.deleteByActivityId(activityId);
    }
}
