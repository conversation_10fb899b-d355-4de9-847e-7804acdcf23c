package com.intelliquor.cloud.shop.dealer.controller.manage;


import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.ActivityCodeModel;
import com.intelliquor.cloud.shop.common.model.req.ActivityCodeReq;
import com.intelliquor.cloud.shop.common.service.ActivityCodeCommonService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


import javax.validation.Valid;
import java.util.List;


@RestController
@RequiredArgsConstructor
@RequestMapping("/activityCodeRule")
public class ActivityCodeController {

    private final ActivityCodeCommonService activityCodeCommonService;

    @PostMapping("/save")
    public RestResponse<String> save(@Valid @RequestBody ActivityCodeModel activityCodeModel) {
        try {
            activityCodeCommonService.addActivityCodeRule(activityCodeModel);
            return RestResponse.success("操作成功");
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }

    @PostMapping("/update")
    public RestResponse<String> update(@Valid @RequestBody ActivityCodeModel activityCodeModel) {
        try {
            activityCodeCommonService.updateActivityCodeRule(activityCodeModel);
            return RestResponse.success("操作成功");
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }

    @RequestMapping(value = "/getListByPage")
    public RestResponse getListByPage(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                           @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                           ActivityCodeReq req) {
        try {
            PageMethod.startPage(pageNum, pageSize);
            List<ActivityCodeModel> activityCodeList = activityCodeCommonService.getListByPage(req,pageNum, pageSize);
            PageInfo<ActivityCodeModel> pageInfo = new PageInfo(activityCodeList);
            return RestResponse.success(pageInfo);
        } catch (Exception e) {
            return RestResponse.error("码规则列表获取失败，请重试");
        }
    }
}
