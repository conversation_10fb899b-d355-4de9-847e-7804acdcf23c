package com.intelliquor.cloud.shop.dealer.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.dealer.model.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2020/12/17 09:11
 * @Description:
 */
@Data
public class DealerInfoResp extends DealerInfoModel {

    public DealerInfoResp() {
        this.distributorNum = 0;
        this.toDayScanCodeDistributorNum = 0;
        this.scanCodeBatchNum = 0;
        this.scanCodeBottleNum = 0;
        this.shopNum = 0;
        this.toDayScanCodeShopNum = 0;
        this.totalOrderForm = 0;
    }

    /**
     * 登陆成功签名
     */
    private String authLocal;

    /**
     * 分销商数
     */
    private Integer distributorNum;
    /**
     * 分销商订单数
     */
    private Integer distributorOrderNum;
    /**
     * 终端数量
     */
    private Integer terminalNum;
    /**
     * 终端订单数
     */
    private Integer terminalOrderNum;

    /**
     * 今日扫码分销商数
     */
    private Integer toDayScanCodeDistributorNum;

    /**
     * 终端店数
     */
    private Integer shopNum;

    /**
     * 今日扫码终端店数
     */
    private Integer toDayScanCodeShopNum;

    /**
     * 分销商用户列表信息
     */
    private List<DealerInfoModel> distributorUserList;

    /**
     * 扫码次数
     */
    private Integer scanCodeBatchNum;

    /**
     * 扫码瓶数
     */
    private Integer scanCodeBottleNum;

    /**
     * 扫码进货列表信息
     */
    private List<DealerGoodStockModel> dealerGoodStockModelList;

    /**
     * 最新的一次扫码详情
     */
    private DealerRewardScanBalanceModel newsDealerRewardScanBalanceModel;

    /**
     * 当前登录员工信息
     */
    private DealerStaffModel currStaffModel;

    /**
     * 经销商类型
     */
    private DealerTypeModel dealerTypeModel;

    /**
     * 业务区域路径集合
     */
    private String dealerAreaList;

    /**
     * 经销商列表信息
     */
    private List<DealerInfoModel> dealerUserList;

    /**
     * 待结算收益
     */
    private BigDecimal unsettledProfit;

    /**
     * 待结算积分
     */
    private Integer unsettledScore;

    /**
     * 最新的一次进货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newScanTime;

    /**
     * 国台核心分销商扫码进货列表信息
     */
    private List<DealerScanCodeDetailModel> dealerScanCodeDetailModels;

    /**
     * 订单数
     */
    private Integer orderNum;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订货单数
     */
    private Integer totalOrderForm;

    private Integer accountType;

    private List<Goods> goodsList;

    /**
     * 进货商品信息
     */
    @Data
    public static class Goods{
        /**
         * 商品名称
         */
        private String goodsName;
        /**
         * 进货数量
         */
        private Integer quantity;
    }
}
