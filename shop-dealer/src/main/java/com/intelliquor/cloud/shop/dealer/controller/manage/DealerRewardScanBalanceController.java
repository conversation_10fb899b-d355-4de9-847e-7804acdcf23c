package com.intelliquor.cloud.shop.dealer.controller.manage;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.FileItem;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.AreaModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.ExcelTools;
import com.intelliquor.cloud.shop.common.utils.FileTransformTools;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.dealer.controller.AbstractController;
import com.intelliquor.cloud.shop.dealer.model.DealerGoodStockModel;
import com.intelliquor.cloud.shop.dealer.model.DealerRewardScanBalanceModel;
import com.intelliquor.cloud.shop.dealer.model.req.DealerGoodStockReq;
import com.intelliquor.cloud.shop.dealer.model.req.DealerRewardScanBalanceReq;
import com.intelliquor.cloud.shop.dealer.service.DealerRewardScanBalanceService;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import com.intelliquor.cloud.shop.dealer.util.constants.ColumnConstant;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2020/12/24 15:13
 * @Description: 扫码批次控制层
 */
@Slf4j
@RestController
@RequestMapping(value="/system/rewardScanBalance")
public class DealerRewardScanBalanceController extends AbstractController {

    @Autowired
    private UserContext userContext;

    @Autowired
    private DealerRewardScanBalanceService dealerRewardScanBalanceService;

    @ApiOperation(value = "分销商进货统计", notes = "分销商进货统计", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "string"),
    })
    @GetMapping(value = "/distributorPurchaseStatistics")
    public RestResponse distributorPurchaseStatistics(@RequestParam(value = "page", required = true) Integer page,
                                  @RequestParam(value = "limit", required = true) Integer limit,
                                  @RequestParam(value = "startTime", required = false) String startTime,
                                  @RequestParam(value = "endTime", required = false) String endTime,
                                  DealerRewardScanBalanceModel dealerRewardScanBalanceModel) {
        try {
            if (!ColumnConstant.ADMIN.equals(getArea())) {
                List<AreaModel> areaModelList = JSONArray.parseArray(getArea(), AreaModel.class);
                dealerRewardScanBalanceModel.setAreaModelList(areaModelList);
            }


            PageHelper.startPage(page, limit);
            dealerRewardScanBalanceModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            if (ObjectUtil.isEmpty(dealerRewardScanBalanceModel.getType())) {
                dealerRewardScanBalanceModel.setType(2);
            }
            MapUtils param = new MapUtils(SearchUtil.getSearch(dealerRewardScanBalanceModel));
            param.put("startTime", startTime).put("endTime", endTime);
            List<DealerRewardScanBalanceModel> dealerRewardScanBalanceModels = dealerRewardScanBalanceService.distributorPurchaseStatistics(param);
            PageInfo pageInfo = new PageInfo(dealerRewardScanBalanceModels);
            return RestResponse.success(pageInfo);
        } catch (Exception e) {
            log.error("分销商进货统计失败：{}", e);
            return RestResponse.error("分销商进货统计失败，请重试");
        }
    }

    /**
     * 导出分销商进货统计数据
     */
    @ApiOperation(value = "导出分销商进货统计数据", notes = "导出", httpMethod = "POST")
    @PostMapping(value = "/exportPurchaseStatisticsData")
    public void exportPurchaseStatisticsData(HttpServletRequest request, HttpServletResponse response, @RequestBody DealerRewardScanBalanceReq model) throws IOException {
        log.info("销商进货统计数据导出start--------");
        FileItem fileItem = new FileItem();
        if(model == null) {
            model = new DealerRewardScanBalanceReq();
        }
        if (ObjectUtil.isEmpty(model.getType())) {
            model.setType(2);
        }
        model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        List<DealerRewardScanBalanceModel> dealerRewardScanBalanceModels= dealerRewardScanBalanceService.distributorPurchaseStatistics(new MapUtils(SearchUtil.getSearch(model)));
        try {
            if(CollectionUtils.isEmpty(dealerRewardScanBalanceModels)) {
                dealerRewardScanBalanceModels = new ArrayList<>();
                DealerRewardScanBalanceModel models = new DealerRewardScanBalanceModel();
                dealerRewardScanBalanceModels.add(models);

            }
            fileItem = ExcelTools.exportByFile(dealerRewardScanBalanceModels, 1);
            FileTransformTools.transformToExcel(fileItem, request, response);
            log.info("销商进货统计数据导出end----");
        } catch (Exception e) {
            log.info("导出失败----" + e.getMessage());
            throw new BusinessException(e.getMessage());
        }



    }
}
