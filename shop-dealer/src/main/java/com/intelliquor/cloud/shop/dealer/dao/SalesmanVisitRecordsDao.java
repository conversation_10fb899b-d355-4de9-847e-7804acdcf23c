package com.intelliquor.cloud.shop.dealer.dao;

import com.intelliquor.cloud.shop.dealer.model.SalesmanVisitPlanModel;
import com.intelliquor.cloud.shop.dealer.model.resp.SalesmanVisitDetailsResp;
import com.intelliquor.cloud.shop.dealer.model.resp.SalesmanVisitPlanResp;
import com.intelliquor.cloud.shop.dealer.model.resp.SalesmanVisitRecordsResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2021/4/2
 * @description
 */
public interface SalesmanVisitRecordsDao {
    List<SalesmanVisitRecordsResp> getSuddenListByPage(Map<String, Object> search);

    SalesmanVisitDetailsResp getVisitDetails(@Param("id") Integer id,@Param("recordType") Integer recordType);

    SalesmanVisitDetailsResp getLostVisitDetails(@Param("id") Integer id,@Param("recordType") Integer recordType);
}
