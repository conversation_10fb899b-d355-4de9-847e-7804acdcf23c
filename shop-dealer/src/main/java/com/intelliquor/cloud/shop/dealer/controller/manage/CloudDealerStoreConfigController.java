package com.intelliquor.cloud.shop.dealer.controller.manage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.dealer.model.CloudDealerSpecialStoreModel;
import com.intelliquor.cloud.shop.dealer.model.CloudDealerStoreConfigModel;
import com.intelliquor.cloud.shop.dealer.service.ICloudDealerStoreConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 国台经销商专卖店配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@RestController
@RequestMapping("/manage/storeConfig")
public class CloudDealerStoreConfigController {

    @Autowired
    private ICloudDealerStoreConfigService cloudDealerStoreConfigService;


    @Autowired
    private UserContext userContext;
    /**
     * 保存专卖店配置
     *
     * @param model
     * @return
     */
    @PostMapping(value = "/update")
    public Response<String> update(@RequestBody CloudDealerStoreConfigModel model) {
        UpdateWrapper<CloudDealerStoreConfigModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("company_id",userContext.getUserInfo().getIntCompanyId());
        model.setUpdateTime(LocalDateTime.now());
        cloudDealerStoreConfigService.update(model, updateWrapper);
        return Response.ok();
    }

    /**
     * 获取配置
     *
     * @return
     */
    @GetMapping(value = "/detail")
    public Response<CloudDealerStoreConfigModel> detail() {
        QueryWrapper<CloudDealerStoreConfigModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("company_id",userContext.getUserInfo().getIntCompanyId());
        CloudDealerStoreConfigModel config = cloudDealerStoreConfigService.getOne(queryWrapper);
        if(config == null){
            return Response.fail("未找到配置");
        }
        return Response.ok(config);
    }


}
