package com.intelliquor.cloud.shop.dealer.controller.applet;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Maps;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.dealer.dao.SalesmanVisitPlanDao;
import com.intelliquor.cloud.shop.dealer.model.SalesmanVisitPlanInstanceModel;
import com.intelliquor.cloud.shop.dealer.model.SalesmanVisitPlanModel;
import com.intelliquor.cloud.shop.dealer.model.req.SalesmanVisitPlanReq;
import com.intelliquor.cloud.shop.dealer.model.resp.SalesmanVisitPlanResp;
import com.intelliquor.cloud.shop.dealer.model.resp.SalesmanVisitPlanUserResp;
import com.intelliquor.cloud.shop.dealer.service.SalesmanVisitPlanInstanceService;
import com.intelliquor.cloud.shop.dealer.service.SalesmanVisitPlanService;
import com.intelliquor.cloud.shop.dealer.util.constants.ColumnConstant;
import com.intelliquor.cloud.shop.dealer.util.enums.VisitPlanSourceEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 拜访计划-小程序接口
 * todo 复制的 shop-salesman-manage项目
 *
 * <AUTHOR>
 * @date 2021-03-10
 * @module 云商拜访计划管理
 */
@RestController
@RequestMapping("/dealerApplet/salesmanVisitPlan")
public class SalesmanVisitPlanController {

    @Autowired
    private UserContext userContext;

    @Autowired
    private SalesmanVisitPlanService salesmanVisitPlanService;

    @Autowired
    private SalesmanVisitPlanInstanceService salesmanVisitPlanInstanceService;

    @Autowired
    private SalesmanVisitPlanDao salesmanVisitPlanDao;

    /**
     * 拜访计划列表（分页）
     * 分页查询计划列表数据
     *
     * @param page                 页码
     * @param limit                显示条数
     * @param salesmanVisitPlanReq 其他查询条件
     * @return com.intelliquor.cloud.shop.common.entity.PageResponse<java.util.List < com.intelliquor.cloud.shop.dealer.model.resp.SalesmanVisitPlanResp>>
     * @auther: tms
     * @date: 2021/03/17 11:12
     */
    @GetMapping(value = "/getListByPage")
    public RestResponse<PageInfo<SalesmanVisitPlanResp>> getListByPage(@RequestParam(value = "page", required = true) int page,
                                                                       @RequestParam(value = "limit", required = true) int limit,
                                                                       @RequestParam(value = "startTime", required = false) String startTime,
                                                                       @RequestParam(value = "endTime", required = false) String endTime,
                                                                       SalesmanVisitPlanReq salesmanVisitPlanReq) {
        salesmanVisitPlanReq.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        salesmanVisitPlanReq.setIsDelete(0);
        salesmanVisitPlanReq.setSource(VisitPlanSourceEnum.CLOUD_DEALER.getType());
        salesmanVisitPlanReq.setCreateUser(userContext.getUserInfo().getUserId());
        Map<String, Object> search = SearchUtil.getSearch(salesmanVisitPlanReq);
        if (StringUtils.isNotEmpty(startTime)) {
            search.put(ColumnConstant.START_TIME, startTime + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(endTime)) {
            search.put(ColumnConstant.END_TIME, endTime + " 23:59:59");
        }
        PageMethod.startPage(page, limit);
        List<SalesmanVisitPlanResp> list = salesmanVisitPlanService.selectList(search);
        //周期计划：查询完成次数(完成整个周期：比如这个周期是5家，必须拜访完这5家才算完成一个周期)
        if (CollectionUtils.isNotEmpty(list)) {
            List<Integer> planIds = list.stream().map(p -> p.getId()).collect(Collectors.toList());
            Map<Integer, Integer> map = salesmanVisitPlanInstanceService.queryInstanceCount(planIds, 3);
            if (MapUtils.isNotEmpty(map)) {
                list.stream().forEach(d -> {
                    if (map.containsKey(d.getId())) {
                        d.setCompleteCount(map.get(d.getId()));
                    }
                });
            }
        }
        PageInfo<SalesmanVisitPlanResp> pageInfo = new PageInfo<>(list);
        return RestResponse.success(pageInfo);
    }

    /**
     * 拜访计划列表（不分页）
     * 不分页查询计划列表数据
     *
     * @param salesmanVisitPlanReq 其他查询条件
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.util.List < com.intelliquor.cloud.shop.dealer.model.resp.SalesmanVisitPlanResp>>
     * @auther: tms
     * @date: 2021/03/17 11:15
     */
    @GetMapping(value = "/getList")
    public RestResponse<List<SalesmanVisitPlanResp>> getList(@RequestParam(value = "startTime", required = false) String startTime,
                                                             @RequestParam(value = "endTime", required = false) String endTime,
                                                             SalesmanVisitPlanReq salesmanVisitPlanReq) {
        salesmanVisitPlanReq.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        salesmanVisitPlanReq.setIsDelete(0);
        salesmanVisitPlanReq.setSource(VisitPlanSourceEnum.CLOUD_DEALER.getType());
        salesmanVisitPlanReq.setCreateUser(userContext.getUserInfo().getUserId());
        Map<String, Object> search = SearchUtil.getSearch(salesmanVisitPlanReq);
        if (StringUtils.isNotEmpty(startTime)) {
            search.put(ColumnConstant.START_TIME, startTime + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(endTime)) {
            search.put(ColumnConstant.END_TIME, endTime + " 23:59:59");
        }
        List<SalesmanVisitPlanResp> list = salesmanVisitPlanService.selectList(search);
        return RestResponse.success(list);
    }

    /**
     * 添加计划
     *
     * @param model 计划实体数据
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.lang.String>
     * @auther: tms
     * @date: 2021/03/17 11:16
     */
    @PostMapping(value = "/save")
    public RestResponse<String> save(@RequestBody SalesmanVisitPlanModel model) {
        if (model.getEndTimeFlag() != null && model.getEndTimeFlag() == 0 && model.getEndTime() == null) {
            return RestResponse.error("结束时间不能为空");
        }
        model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        model.setCreateUser(userContext.getUserInfo().getUserId());
        model.setSource(VisitPlanSourceEnum.CLOUD_DEALER.getType());
        salesmanVisitPlanService.insert(model);
        return RestResponse.success("保存成功");
    }

    /**
     * 更新计划
     *
     * @param model 计划实体数据
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.lang.String>
     * @auther: tms
     * @date: 2021/03/17 11:20
     */
    @PutMapping(value = "/update")
    public RestResponse<String> update(@RequestBody SalesmanVisitPlanModel model) {
        model.setUpdateUser(userContext.getUserInfo().getUserId());
        model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        salesmanVisitPlanService.update(model);
        return RestResponse.success("更新成功");
    }

    /**
     * 删除计划
     * 根据计划id删除计划
     *
     * @param id 计划id
     * @return com.intelliquor.cloud.shop.common.entity.RestResponse<java.lang.String>
     * @auther: tms
     * @date: 2021/03/17 11:20
     */
    @DeleteMapping(value = "/delete")
    public RestResponse<String> delete(@RequestParam(value = "id") Integer id) {
        salesmanVisitPlanService.delete(id);
        return RestResponse.success("删除成功");
    }

    /**
     * 计划详情
     * 根据计划id获取计划详情
     *
     * @param id 计划id
     * @return com.intelliquor.cloud.shop.common.entity.RestResponse<com.intelliquor.cloud.shop.dealer.model.SalesmanVisitPlanModel>
     * @auther: tms
     * @date: 2021/03/17 11:20
     */
    @GetMapping(value = "/getById")
    public RestResponse<SalesmanVisitPlanModel> getById(@RequestParam(value = "id") Integer id) {
        SalesmanVisitPlanModel model = salesmanVisitPlanService.getById(id);
        return RestResponse.success(model);
    }

    /**
     * 停止计划
     *
     * @param planId 计划id
     * @return com.intelliquor.cloud.shop.common.exception.RestResponse<java.lang.String>
     * @auther: tms
     * @date: 2021/03/17 14:45
     */
    @GetMapping("/stopPlan")
    public RestResponse<String> stopPlan(@RequestParam("planId") Integer planId) {
        salesmanVisitPlanService.stopPlan(planId);
        return RestResponse.success("计划停止成功");
    }

    /**
     * 计划完成情况
     * 计划进行中的情况，每天：直接显示日期，每周：取周期起止日期，每月：取周期月份, 每年：取年
     *
     * @param page      页码
     * @param limit     条数
     * @param planId    计划id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return com.intelliquor.cloud.shop.common.exception.RestResponse<com.github.pagehelper.PageInfo < com.intelliquor.cloud.shop.dealer.model.SalesmanVisitPlanInstanceModel>>
     * @auther: tms
     * @date: 2021/03/18 10:09
     */
    @GetMapping(value = "/getPlanInstanceComplete")
    public RestResponse<PageInfo<SalesmanVisitPlanInstanceModel>> getPlanInstanceComplete(@RequestParam(value = "page", required = true) int page,
                                                                                          @RequestParam(value = "limit", required = true) int limit,
                                                                                          @RequestParam(value = "planId", required = true) Integer planId,
                                                                                          @RequestParam(value = "startTime", required = false) String startTime,
                                                                                          @RequestParam(value = "endTime", required = false) String endTime) {
        PageHelper.startPage(page, limit);
        Map<String, Object> param = Maps.newHashMap();
        param.put("planId", planId);
        if (StringUtils.isNotEmpty(startTime)) {
            param.put(ColumnConstant.START_TIME, startTime + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(endTime)) {
            param.put(ColumnConstant.END_TIME, endTime + " 23:59:59");
        }
        List<SalesmanVisitPlanInstanceModel> instanceModels = salesmanVisitPlanInstanceService.queryInstanceDetail(param);
        PageInfo<SalesmanVisitPlanInstanceModel> pageInfo = new PageInfo<>(instanceModels);
        return RestResponse.success(pageInfo);
    }


    /**
     * 突击拜访列表
     * @param page      页码
     * @param limit     条数
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param salesmanVisitPlanReq 其他查询条件
     * @return
     */
    @GetMapping(value = "/getSuddenListByPage")
    public RestResponse<PageInfo<SalesmanVisitPlanResp>> getSuddenListByPage(@RequestParam(value = "page", required = true) int page,
                                                                       @RequestParam(value = "limit", required = true) int limit,
                                                                       @RequestParam(value = "startTime", required = false) String startTime,
                                                                       @RequestParam(value = "endTime", required = false) String endTime,
                                                                       SalesmanVisitPlanReq salesmanVisitPlanReq) {
        salesmanVisitPlanReq.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        salesmanVisitPlanReq.setDealerId(userContext.getUserInfo().getUid());
        salesmanVisitPlanReq.setPlanType(2);
        salesmanVisitPlanReq.setSource(VisitPlanSourceEnum.CLOUD_DEALER.getType());
        Map<String, Object> search = SearchUtil.getSearch(salesmanVisitPlanReq);
        if (StringUtils.isNotEmpty(startTime)) {
            search.put(ColumnConstant.START_TIME, startTime + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(endTime)) {
            search.put(ColumnConstant.END_TIME, endTime + " 23:59:59");
        }
        PageMethod.startPage(page, limit);
        List<SalesmanVisitPlanResp> list = salesmanVisitPlanService.assaultSelectList(search);
        if(CollectionUtils.isNotEmpty(list)){
            list.stream().forEach(d->{
                String userNames = "";
                List<Integer> ids= Arrays.asList(d.getVisitUser() .split(",")).stream().map(s -> Integer.parseInt(s)).collect(Collectors.toList());
                Map<String,Object> param = Maps.newHashMap();
                param.put("companyId",d.getCompanyId());
                param.put("idList",ids);
                param.put("visitType",d.getVisitType());
                List<SalesmanVisitPlanUserResp> customerList = salesmanVisitPlanDao.queryVisitPlanUserList(param);
                userNames = customerList.stream().map(SalesmanVisitPlanUserResp::getName).collect(Collectors.joining(","));
                d.setUserNames(userNames);
            });
        }
        PageInfo<SalesmanVisitPlanResp> pageInfo = new PageInfo<>(list);
        return RestResponse.success(pageInfo);
    }
}