package com.intelliquor.cloud.shop.dealer.controller.manage;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.dealer.model.NewsTypeModel;
import com.intelliquor.cloud.shop.dealer.service.NewsTypeService;
import com.intelliquor.cloud.shop.dealer.util.constants.StatusConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 描述：云商内容类型控制层
 *
 * <AUTHOR>
 * @date 2021-01-17
 */
@Api(tags = {"云商内容类型操作接口"}, description = "云商内容类型操作接口")
@RestController
@RequestMapping("/system/newsType")
public class NewsTypeController {

    @Autowired
    private NewsTypeService newsTypeService;

    @Autowired
    private UserContext userContext;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<NewsTypeModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                           @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                           NewsTypeModel model) {

        PageHelper.startPage(page, limit);
        model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        model.setIsDelete(StatusConstant.DELETE_STATUS.NO_DEL);
        List<NewsTypeModel> list = newsTypeService.selectList(SearchUtil.getSearch(model));
        PageInfo<NewsTypeModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<NewsTypeModel>> getList(NewsTypeModel model) {
        model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        model.setIsDelete(StatusConstant.DELETE_STATUS.NO_DEL);
        List<NewsTypeModel> list = newsTypeService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(@RequestBody NewsTypeModel model) {
        try {
            model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            if (ObjectUtil.isEmpty(model.getName())) {
                return Response.fail("请填写内容类型");
            }
            if (ObjectUtil.isEmpty(model.getSort())) {
                return Response.fail("请填写显示顺序");
            }
            newsTypeService.insert(model);
            return Response.ok("保存成功");
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "更新信息", notes = "更新信息", httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(@RequestBody NewsTypeModel model) {
        try {
            model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            if (ObjectUtil.isEmpty(model.getId())) {
                return Response.fail("参数有误，缺少ID");
            }
            if (ObjectUtil.isEmpty(model.getName())) {
                return Response.fail("请填写内容类型");
            }
            if (ObjectUtil.isEmpty(model.getSort())) {
                return Response.fail("请填写显示顺序");
            }
            newsTypeService.update(model);
            return Response.ok("更新成功");
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除信息", notes = "删除信息", httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
        try {
            newsTypeService.delete(id);
            return Response.ok("删除成功");
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息", httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<NewsTypeModel> getById(@RequestParam(value = "id") Integer id) {
        NewsTypeModel model = newsTypeService.getById(id);
        return Response.ok(model);
    }
}