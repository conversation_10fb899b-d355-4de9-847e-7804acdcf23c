package com.intelliquor.cloud.shop.dealer.service;

import com.intelliquor.cloud.shop.dealer.model.DealerScanCodeRecordModel;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020.12.17
 * 经销商扫码记录接口层
 */
public interface DealerScanCodeRecordService {

    /**
     * 功能描述: 获取扫码记录
     * @param param
     * @auther: tms
     * @date: 2020/12/17 19:13
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.DealerScanCodeRecordModel>
    */
    List<DealerScanCodeRecordModel> queryList(MapUtils param);

    void insert(Integer dealerStaffId,Integer dealerId, String qrcode, Integer companyId, Integer type, Integer codeType, Double lat, Double lon);

}
