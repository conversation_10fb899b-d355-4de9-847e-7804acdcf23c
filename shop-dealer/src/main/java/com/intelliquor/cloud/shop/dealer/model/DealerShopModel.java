package com.intelliquor.cloud.shop.dealer.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2021/01/11 10:24
 * @Description: 云商终端店返回详情
 */
@Data
public class DealerShopModel extends ShopModel {

    /**
     * 扫码次数
     */
    private Integer scanCodeBatchNum;

    /**
     * 扫码瓶数
     */
    private Integer scanCodeBottleNum;

    /**
     * 最新的一次扫码时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newScanDate;

    /**
     * 最后一次扫码距离现在天数
     */
    private Integer newScanDays;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品瓶数
     */
    private Integer goodsBottleNum;

    /**
     * 商品数据列表
     */
    List<DealerShopModel> goodDataList;

    /**
     * 最新的一次扫码时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date groupTime;

    /**
     * 最后拜访时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastVisitTime;


    
}
