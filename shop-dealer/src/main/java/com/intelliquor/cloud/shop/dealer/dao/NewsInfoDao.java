package com.intelliquor.cloud.shop.dealer.dao;

import com.intelliquor.cloud.shop.dealer.model.NewsInfoModel;
import java.util.Map;
import java.util.List;


/**
* 描述：云商内容管理 Dao接口
* <AUTHOR>
* @date 2021-01-17
*/
public interface NewsInfoDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<NewsInfoModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(NewsInfoModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(NewsInfoModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    NewsInfoModel getById(Integer id);

}