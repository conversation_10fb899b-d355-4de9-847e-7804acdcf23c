package com.intelliquor.cloud.shop.dealer.controller.open;

import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.req.CorkageRewardReq;
import com.intelliquor.cloud.shop.common.model.resp.CorkageRewardInfo;
import com.intelliquor.cloud.shop.common.utils.GuotaiUtil;
import com.intelliquor.cloud.shop.dealer.model.req.PromotionalRewardReq;
import com.intelliquor.cloud.shop.dealer.model.req.PromotionalScoreReq;
import com.intelliquor.cloud.shop.dealer.model.resp.*;
import com.intelliquor.cloud.shop.dealer.service.DealerRewardService;
import com.intelliquor.cloud.shop.dealer.service.PromotionalScoreDetailService;
import com.intelliquor.cloud.shop.dealer.service.PromotionalScoreService;
import com.intelliquor.cloud.shop.dealer.util.DesUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * @Author: MAX
 * @CreateTime: 2023-03-17  10:06
 */
@Slf4j
@RestController
@RequestMapping("/data-api/dealerReward")
public class DealerRewardController {

    @Resource
    private DealerRewardService dealerRewardService;
    @Resource
    private PromotionalScoreService promotionalScoreService;
    @Resource
    private PromotionalScoreDetailService promotionalScoreDetailService;

    /**
     * @description: 控盘分利-总览接口
     * @author: MAX
     * @date: 2023/3/17 10:09
     * @param: [contractCode]
     * @return: Response
     **/
    @GetMapping("/rewardOverview")
    public Response<RewardOverviewResp> rewardOverview(@RequestParam(name = "contract_code", required = false) String contractCode,
                                                       HttpServletRequest request) {

        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        if (StringUtils.isBlank(contractCode)) {
            throw new BusinessException("合同编码不能为空");
        }

        RewardOverviewResp resp = dealerRewardService.rewardOverview(contractCode);
        return Response.ok(resp);

    }


    /**
     * @description: 动销奖励查询接口
     * @author: MAX
     * @date: 2023/3/17 10:09
     * @param: [PromotionalRewardReq]
     * @return: Response
     **/
    @PostMapping("/promotionalReward")
    public Response<PromotionalOrderPageResp> promotionalReward(
            @Valid @RequestBody PromotionalRewardReq req,
            HttpServletRequest request) {
        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        PromotionalOrderPageResp resp = dealerRewardService.promotionalReward(req);
        return Response.ok(resp);

    }

    /**
     * 动销奖励明细接口
     *
     * @param id      订单id
     * @param request
     * @return Response
     */
    @GetMapping("/promotionalRewardDetailById")
    public Response<PromotionalOrderInfo> promotionalRewardDetailById(@RequestParam(name = "id", required = false) Long id,
                                                                      HttpServletRequest request) {

        if (Objects.isNull(id)) {
            throw new BusinessException("订单ID不能为空");
        }
        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        PromotionalOrderInfo resp = dealerRewardService.promotionalRewardDetailById(id);
        return Response.ok(resp);

    }


    /**
     * 消费者开瓶奖励接口
     *
     * @param req     请求参数
     * @param request request
     * @return Response
     */
    @PostMapping("/corkageReward")
    public Response<CorkageRewardInfo> corkageReward(@Valid @RequestBody CorkageRewardReq req,
                                                     HttpServletRequest request) {
        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        CorkageRewardInfo resp = dealerRewardService.corkageReward(req);
        return Response.ok(resp);

    }


    /**
     * 消费者开瓶奖励明细接口
     *
     * @param req     请求参数
     * @param request request
     * @return Response
     */
    @PostMapping("/corkageRewardDetail")
    public Response<CorkageRewardInfo> corkageRewardDetail(@Valid @RequestBody CorkageRewardReq req,
                                                           HttpServletRequest request) {
        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        CorkageRewardInfo resp = dealerRewardService.corkageRewardDetail(req);
        return Response.ok(resp);

    }


    /**
     *  @author: HLQ
     *  @Date: 2023/4/21 15:08
     *  @Description: 下单积分明细
     */
    @PostMapping("/promotionalScore")
    public Response<PromotionalScoreRtnResp> promotionalScore(@Valid @RequestBody PromotionalScoreReq req, HttpServletRequest request) {
        log.info("下单积分接口--promotionalScore的请求参数:{}", JSONObject.toJSONString(req));
        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        PromotionalScoreRtnResp promotionalScoreRtnResp = promotionalScoreService.promotionalScore(req);
        return Response.ok(promotionalScoreRtnResp);

    }


    /**
     *  @author: HLQ
     *  @Date: 2023/4/23 9:02
     *  @Description: 通过订单ID查询明细
     */
    @GetMapping("/promotionalScoreDetailById")
    public Response<PromotionalScoreOrderInfo> promotionalScoreDetailById(@RequestParam(name = "id", required = false) Long id,
                                                                      HttpServletRequest request) {
        log.info("下单积分明细接口--promotionalScoreDetailById的请求参数:{}", id);
        if (Objects.isNull(id)) {
            throw new BusinessException("订单ID不能为空");
        }
        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        PromotionalScoreOrderInfo resp = promotionalScoreDetailService.promotionalScoreDetailById(id);
        return Response.ok(resp);

    }
}
