package com.intelliquor.cloud.shop.dealer.controller.manage;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.dealer.model.resp.DealerSpecialStoreDetailRes;
import com.intelliquor.cloud.shop.dealer.service.CloudDealerSpecialStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 国台专卖店管理
 *
 * <AUTHOR>
 * @date 2021-09-27
 */
@RestController
@RequestMapping("/h5/nearbyStores")
public class DealerSpecialStoreDetailH5Controller {

    @Autowired
    private CloudDealerSpecialStoreService cloudDealerSpecialStoreService;

    /**
     * 根据ID查询信息
     */
    @GetMapping(value = "/getDetail")
    public Response<DealerSpecialStoreDetailRes> getDetail(@RequestParam(value = "id") Integer id,
                                                          @RequestParam(value = "storeType") Integer storeType,
                                                         @RequestParam(value = "dealerId") Integer dealerId,
                                                           @RequestParam(value = "companyId") Integer companyId,
                                                           @RequestParam(value = "page") Integer page,
                                                           @RequestParam(value = "limit") Integer limit) {
        DealerSpecialStoreDetailRes res = cloudDealerSpecialStoreService.getDetail(id,storeType,dealerId,companyId,page,limit);
        return Response.ok(res);
    }
}