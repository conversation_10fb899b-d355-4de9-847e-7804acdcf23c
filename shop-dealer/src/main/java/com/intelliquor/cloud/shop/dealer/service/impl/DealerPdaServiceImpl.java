package com.intelliquor.cloud.shop.dealer.service.impl;

import com.alibaba.fastjson.JSON;
import com.intelliquor.cloud.shop.dealer.dao.ReportScanCodeRecordDao;
import com.intelliquor.cloud.shop.dealer.model.ReportScanCodeRecordModel;
import com.intelliquor.cloud.shop.dealer.model.resp.DealerShopResp;
import com.intelliquor.cloud.shop.dealer.model.resp.PdaTerminalResp;
import com.intelliquor.cloud.shop.dealer.service.DealerInfoService;
import com.intelliquor.cloud.shop.dealer.service.DealerPdaService;
import com.intelliquor.cloud.shop.dealer.service.DealerShopService;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import com.intelliquor.cloud.shop.dealer.util.constants.ColumnConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: tianms
 * @Date: 2021/05/24 16:16
 * @Description: pda - service impl
 */
@Service
public class DealerPdaServiceImpl implements DealerPdaService {

    @Autowired
    private DealerInfoService dealerInfoService;

    @Autowired
    private DealerShopService dealerShopService;

    @Autowired
    private ReportScanCodeRecordDao reportScanCodeRecordDao;

    /**
     * 获取终端列表
     *
     * @param search
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.resp.PdaTerminalResp>
     * @auther: tms
     * @date: 2021/05/24 16:16
     */
    @Override
    public List<PdaTerminalResp> queryTerminalList(Map<String, Object> search) {

        if ("1".equals(String.valueOf(search.get("type")))) { // 分销商

            List<PdaTerminalResp> pdaTerminalRespList = dealerInfoService.queryPdaDistributorUserList(search);

            return pdaTerminalRespList;
        }

        if ("2".equals(String.valueOf(search.get("type")))) { // 终端

            // 查询所有上报的终端列表  有问题找测试  20210428
            search.put(ColumnConstant.DEALER_ID, search.get("parentId"));
            search.put(ColumnConstant.SHOP_NAME, search.get("name"));

            List<DealerShopResp> dealerTerminalList = dealerShopService.getDealerTerminalList(new MapUtils(search));

            if (CollectionUtils.isEmpty(dealerTerminalList)) {
                return new ArrayList<>();
            }

            List<PdaTerminalResp> pdaTerminalRespList = dealerTerminalList.stream().map(model -> {
                PdaTerminalResp pdaTerminalResp = new PdaTerminalResp();
                pdaTerminalResp.setAddress(model.getAddress());
                pdaTerminalResp.setId(Integer.parseInt(model.getShopId()));
                pdaTerminalResp.setLinkman(model.getLinkman());
                pdaTerminalResp.setLinkphone(model.getLinkphone());
                pdaTerminalResp.setName(model.getShopName());
                return pdaTerminalResp;
            }).collect(Collectors.toList());

            return pdaTerminalRespList;
        }

        return new ArrayList<>();
    }

    /**
     * 离线上报扫码信息
     *
     * @param reportScanCodeRecord
     * @param userId
     * @param companyId
     * @return void
     * @auther: tms
     * @date: 2021/05/24 17:27
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reportScanCode(ReportScanCodeRecordModel reportScanCodeRecord, Integer userId, Integer companyId) {

        reportScanCodeRecord.setCompanyId(companyId);
        reportScanCodeRecord.setOperator(userId);
        reportScanCodeRecord.setCreateTime(new Date());

        if (CollectionUtils.isNotEmpty(reportScanCodeRecord.getCodeJson())) {
            reportScanCodeRecord.setCodeStr(JSON.toJSONString(reportScanCodeRecord.getCodeJson()));
        }

        List<ReportScanCodeRecordModel> reportScanCodeRecords = new ArrayList<>();
        reportScanCodeRecords.add(reportScanCodeRecord);

        // 批量新增
        reportScanCodeRecordDao.insertBatch(reportScanCodeRecords);

    }
}
