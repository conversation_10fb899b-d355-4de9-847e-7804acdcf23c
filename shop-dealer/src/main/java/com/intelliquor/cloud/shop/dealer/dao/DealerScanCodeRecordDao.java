package com.intelliquor.cloud.shop.dealer.dao;
import com.intelliquor.cloud.shop.dealer.model.DealerScanCodeRecordModel;
import java.util.Map;
import java.util.List;
/**
* 描述：经销商扫码记录表 Dao接口
* <AUTHOR>
* @date 2020-12-16
*/
public interface DealerScanCodeRecordDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<DealerScanCodeRecordModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(DealerScanCodeRecordModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(DealerScanCodeRecordModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    DealerScanCodeRecordModel getById(Integer id);

}