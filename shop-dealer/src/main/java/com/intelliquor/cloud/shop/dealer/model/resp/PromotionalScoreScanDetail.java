package com.intelliquor.cloud.shop.dealer.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class PromotionalScoreScanDetail implements Serializable {
    private static final long serialVersionUID = 3090676725383495444L;

    /**
     * 产品名称
     */
    @JsonProperty("product_name")
    private String goodsName;

    /**
     * 规格
     */
    private String norms;

    /**
     * 数量（瓶），取消扫码入库的显示负数
     */
    @JsonProperty("num")
    private Integer goodsNum;
}
