package com.intelliquor.cloud.shop.dealer.service.impl;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.dealer.dao.DealerBusinessParamConfigDao;
import com.intelliquor.cloud.shop.dealer.model.DealerBusinessParamConfigModel;
import com.intelliquor.cloud.shop.dealer.service.DealerBusinessParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021.1.5
 * 业务参数实现类
 */
@Slf4j
@Service
public class DealerBusinessParamServiceImpl implements DealerBusinessParamService {

    @Autowired
    private DealerBusinessParamConfigDao dealerBusinessParamConfigDao;

    @Override
    public DealerBusinessParamConfigModel getId(Integer companyId) {
        return dealerBusinessParamConfigDao.getByCompanyId(companyId);
    }


    @Override
    public RestResponse insertAndUpdate(DealerBusinessParamConfigModel model) {
        try {
            if(model.getId()!=null) {
                dealerBusinessParamConfigDao.update(model);
            } else {

                dealerBusinessParamConfigDao.insert(model);
            }

        }catch (Exception e) {

            log.error("保存失败：{}",e.getMessage());
            return RestResponse.error("保存失败，请联系管理员");
        }

        return RestResponse.success("保存成功");

    }
}
