package com.intelliquor.cloud.shop.dealer.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Getter
@Setter
@TableName("t_contract_corkage_reward")
public class ContractCorkageRewardModel implements Serializable {

    private static final long serialVersionUID = 8416337240747880974L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 扫码时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date scanTime;

    /**
     * 扫码数量
     */
    private BigDecimal capNum;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 总奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 已上账金额
     */
    private BigDecimal reportReward;

    /**
     * 未上账金额
     */
    private BigDecimal remainReward;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 单瓶奖励金额
     */
    private BigDecimal unitPriceOfRewardAmount;


}
