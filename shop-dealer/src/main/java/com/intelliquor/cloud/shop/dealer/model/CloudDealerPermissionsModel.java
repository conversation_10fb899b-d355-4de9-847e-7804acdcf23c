package com.intelliquor.cloud.shop.dealer.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CloudDealerPermissionsModel {

    private Integer menuId;

    private Integer parentId;

    private String systemCode;

    private String name;

    private String url;

    private String permsServer;

    private String perms;

    /**
     * 类型   0：系统   1：目录  2：菜单   3：按钮
     */
    private Integer type;

    private String icon;
    /**
     * 排序
     */
    private Integer orderNum;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtCreate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmtModified;

    private String permsPath;
    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDel;
}
