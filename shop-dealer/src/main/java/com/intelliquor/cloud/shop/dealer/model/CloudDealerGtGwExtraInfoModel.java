package com.intelliquor.cloud.shop.dealer.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
* 描述：国台分销商信息补充表实体类
* <AUTHOR>
* @date 2021-06-28
*/
@Data
public class CloudDealerGtGwExtraInfoModel {


    @ApiModelProperty(value = "")
    private Integer id;

    private Integer applyId;

    @ApiModelProperty(value = "分销商Id,t_cloud_dealer_info表的id")
    private Integer dealerId;

    @ApiModelProperty(value = "分销商编码")
    private String dealerCode;

    private String dealerName;

    @ApiModelProperty(value = "分销商类型：1公司，2个人")
    private String fxsType;

    /**
     * 品牌类型 1-品牌酒 2-酱酒
     */
    private Integer branchType;

    @ApiModelProperty(value = "分销商所在区域")
    private String localArea;

    @ApiModelProperty(value = "团购类型 1-非团购 2-团购")
    private String teamType;

    @ApiModelProperty(value = "创建人")
    private String createOp;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateOp;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "状态 0-停用 1-启用")
    private Integer status;

    @ApiModelProperty(value = "是否删除 0-已删除 1-未删除")
    private Integer isDelete;

    @ApiModelProperty(value = "商户Id")
    private Integer companyId;

    @ApiModelProperty(value = "分销区域-使用.分隔")
    private String region;

    @ApiModelProperty(value = "分销街道")
    private String street;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 社会信用代码或个人身份证号
     */
    private String legalCode;


    /**
     * 营业执照编码
     */
    private String licenseCode;

    /**
     * 分销区域编码
     */
    private String areaCode;
}