package com.intelliquor.cloud.shop.dealer.dao;
import com.intelliquor.cloud.shop.dealer.model.DealerRewardRecordModel;
import com.intelliquor.cloud.shop.dealer.model.req.DealerScoreReq;
import com.intelliquor.cloud.shop.dealer.model.resp.*;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.List;
/**
* 描述：奖励记录表 Dao接口
* <AUTHOR>
* @date 2021-01-05
*/
public interface DealerRewardRecordDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<DealerRewardRecordModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(DealerRewardRecordModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(DealerRewardRecordModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    DealerRewardRecordModel getById(@Param("id") Integer id, @Param("companyId") Integer companyId);

    /**
     * 功能描述: 获取经销商待结算收益
     * @param dealerId
     * @param companyId
     * @auther: tms
     * @date: 2021/01/06 17:11
     * @return java.math.BigDecimal
    */
    BigDecimal sumDealerUnsettledProfit(@Param("dealerId") Integer dealerId, @Param("companyId") Integer companyId);

    /**
     * 功能描述: 获取经销商待结算积分
     * @param dealerId
     * @param companyId
     */
    Integer sumDealerUnsettledScore(@Param("dealerId") Integer dealerId, @Param("companyId") Integer companyId);

    /**
     * 功能描述: 经销商列表
     * @param param
     * @auther: tms
     * @date: 2021/01/08 18:18
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.resp.DealerRewardRecordResp>
    */
    List<DealerRewardRecordResp> queryDealerRewardList(MapUtils param);

    /**
     * 功能描述: 分销商列表
     * @param param
     * @auther: tms
     * @date: 2021/01/08 18:19
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.resp.DistributorRewardRecordResp>
    */
    List<DistributorRewardRecordResp> queryDistributorRewardList(MapUtils param);


    /**
     * 查询定时器
     * @param time
     * @return
     */
    List<DealerRewardRecordModel> selectByTime(@Param("time") Date time, @Param("companyId")Integer companyId);

    List<SavourWineProductResp> getSavourSingleList(MapUtils param);

    /**
     * 查询品鉴酒入库详情
     * @param balanceId 批次id
     * @param companyId 公司id
     * @return
     */
    WareDetailsResp getWareDetails(@Param("balanceId") Integer balanceId, @Param("companyId") Long companyId);

    /**
     * 查询积分详情列表
     * @param dealerScoreReq
     * @return
     */
    List<DealerScoreResp> getDealerScoreDetails(DealerScoreReq dealerScoreReq);


    List<DealerScanDataResp>  getScanData(@Param("list") List<Integer> ids,@Param("companyId")Integer companyId);

}