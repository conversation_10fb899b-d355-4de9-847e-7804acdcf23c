package com.intelliquor.cloud.shop.dealer.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
* 描述：国台拆箱率基础数据实体类
* <AUTHOR>
* @date 2021-12-15
*/
@Data
public class CloudDealerOpenBasicDataModel {


    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date day;

    @ApiModelProperty(value = "经销商Id")
    private Integer dealerId;

    @ApiModelProperty(value = "经销商编码")
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "经销商手机号")
    private String dealerPhone;

    @ApiModelProperty(value = "经销商类型1-经销商 2-分销商")
    private Integer dealerType;

    @ApiModelProperty(value = "订单数")
    private Integer orderNum=0;

    @ApiModelProperty(value = "入库瓶数")
    private Integer scanInBottleNum=0;

    @ApiModelProperty(value = "入库箱数")
    private Integer scanInBoxNum=0;

    @ApiModelProperty(value = "出库瓶数")
    private Integer scanOutBottleNum=0;

    @ApiModelProperty(value = "出库箱数")
    private Integer scanOutBoxNum=0;

    @ApiModelProperty(value = "拆箱数")
    private Integer openBoxNum=0;

    @ApiModelProperty(value = "消费者开瓶数")
    private Integer consumerOpenNum=0;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "")
    private Integer companyId;

    private String goodsCode;
    private String goodsName;
    /**
     * 入库瓶数-酒厂按瓶出数，也就是按箱出的瓶数，方便计算箱数
     */
    private Integer scanInBottleNumFinal=0;
}