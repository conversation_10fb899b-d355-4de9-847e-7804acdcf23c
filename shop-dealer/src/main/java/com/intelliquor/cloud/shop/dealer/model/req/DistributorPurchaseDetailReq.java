package com.intelliquor.cloud.shop.dealer.model.req;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-12-18
 * 进货明细请求参数
 */
@Data
public class DistributorPurchaseDetailReq implements Serializable {

    /**
     * 经销商名称
     */
    private  String companyName;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 商户Id
     */
    private Integer companyId;
    /**
     * 经销商Id
     */
    private Integer dealerId;
    /**
     * 类型 1 分销商进货 2我的进货明细
     */
    private Integer type;

}
