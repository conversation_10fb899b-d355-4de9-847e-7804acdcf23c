package com.intelliquor.cloud.shop.dealer.dao;

import com.intelliquor.cloud.shop.dealer.model.DealerScanOutBalanceModel;
import com.intelliquor.cloud.shop.dealer.model.resp.DealerScanOutBalanceResp;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2021/05/25 09:30
 * @Description: 云商扫码出库批次 - dao
 */
public interface DealerScanOutBalanceDao {

    /**
     * 查询列表
     *
     * @param param
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.resp.DealerScanOutBalanceResp>
     * @auther: tms
     * @date: 2021/05/25 09:40
     */
    List<DealerScanOutBalanceResp> queryList(MapUtils param);

    /**
     * 根据id查询详情
     *
     * @param id
     * @return com.intelliquor.cloud.shop.dealer.model.resp.DealerScanOutBalanceResp
     * @auther: tms
     * @date: 2021/05/25 09:40
     */
    DealerScanOutBalanceResp findById(@Param("id") Integer id, @Param("companyId") Integer companyId);

    /**
     * 根据条件查询详情
     *
     * @param param
     * @return com.intelliquor.cloud.shop.dealer.model.DealerScanOutBalanceModel
     * @auther: tms
     * @date: 2021/05/25 09:40
     */
    DealerScanOutBalanceModel findByParam(MapUtils param);

    /**
     * 添加
     *
     * @param dealerScanOutBalanceModel
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/05/25 09:41
     */
    Integer insert(DealerScanOutBalanceModel dealerScanOutBalanceModel);

    /**
     * 修改
     *
     * @param dealerScanOutBalanceModel
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/05/25 09:41
     */
    Integer update(DealerScanOutBalanceModel dealerScanOutBalanceModel);

    /**
     * 累计扫码数量
     *
     * @param balanceId 批次id
     * @param boxNum    箱数量
     * @param bottleNum 瓶数量
     * @param scanNum   扫码数量
     * @auther: tms
     * @date: 2021/05/26 13:53
     * @return void
    */
    void addScanNum(@Param("balanceId") Integer balanceId,
                    @Param("boxNum") int boxNum,
                    @Param("bottleNum") int bottleNum,
                    @Param("scanNum") int scanNum,
                    @Param("companyId") Integer companyId);
}
