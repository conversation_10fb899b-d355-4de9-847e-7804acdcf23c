package com.intelliquor.cloud.shop.dealer.factory;

import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class CompanyScanFactory  {

//    @Autowired
//    JZCompanyScan jzCompanyScan;
//    @Autowired
//    GbcCompanyScan gbcCompanyScan;
//
//    @Autowired
//    CommonCompanyScan commonCompanyScan;

    private static Map<Integer, CompanyScanService> services= new ConcurrentHashMap<>();
    /**
     * 根据类别获取扫码对象
     * @param type
     * @return
     */
  /*  public CompanyScan getCompanyScan(Integer type){

        if (type.equals(CompanyScanTypeEnum.JZ_SCAN_TYPE.getValue())) {
            return jzCompanyScan;
        } else if (type.equals(CompanyScanTypeEnum.GBC_SCAN_TYPE.getValue())) {
            return gbcCompanyScan;
        } else if (type.equals(CompanyScanTypeEnum.COMMON_SCAN_TYPE.getValue())) {
            return commonCompanyScan;
        }
        return null;
    }*/

      public static CompanyScanService getCompanyScan(Integer type){
          return services.get(type);
      }

    /**
     * 加入到currentMap中
     * @param type
     * @param companyScan
     */
    public static void register(Integer type, CompanyScanService companyScan) {
        Assert.notNull(type,"type");
        services.put(type,companyScan);
      }



}
