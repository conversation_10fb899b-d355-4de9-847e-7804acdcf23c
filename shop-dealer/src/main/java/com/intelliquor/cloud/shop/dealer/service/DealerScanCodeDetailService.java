package com.intelliquor.cloud.shop.dealer.service;

import com.intelliquor.cloud.shop.dealer.model.DealerScanCodeDetailModel;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;

import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2020/12/18 10:56
 * @Description: 扫码明细接口
 */
public interface DealerScanCodeDetailService {

    /**
     * 功能描述: 列表
     *
     * @param param
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.DealerScanCodeDetailModel>
     * @auther: tms
     * @date: 2020/12/18 10:58
     */
    List<DealerScanCodeDetailModel> queryList(MapUtils param);

    /**
     * 清除活动id
     *
     * @param dealerType
     * @param activityId
     * @param companyId
     * @return void
     * @auther: tms
     * @date: 2021/05/17 14:52
     */
    void clearActivityId(Integer dealerType, Integer activityId, Integer companyId);
}
