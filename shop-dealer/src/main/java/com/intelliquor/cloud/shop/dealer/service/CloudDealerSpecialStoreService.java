package com.intelliquor.cloud.shop.dealer.service;

import com.intelliquor.cloud.shop.dealer.model.CloudDealerSpecialStoreModel;
import com.intelliquor.cloud.shop.dealer.model.resp.DealerSpecialStoreDetailRes;

import java.util.List;
import java.util.Map;

public interface CloudDealerSpecialStoreService {

    /**
     * 查询数据
     *
     * @return
     */
    List<CloudDealerSpecialStoreModel> selectList(Map<String, Object> searchMap);


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(CloudDealerSpecialStoreModel model);

    /**
     * 中台专卖店同步保存
     * @param model
     */
    void insertGtSpecial(List<CloudDealerSpecialStoreModel> model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(CloudDealerSpecialStoreModel model);
    void businessStatus(CloudDealerSpecialStoreModel model);

    /**
     * 更新门头照
     * @param model
     */
    void updateStoresImg(CloudDealerSpecialStoreModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    CloudDealerSpecialStoreModel getById(Integer id);

    /**
     * 获取所有门店的市列表
     */
    List<Map<String,Object>> getStoreCity();


    DealerSpecialStoreDetailRes getDetail(Integer id, Integer storeType, Integer dealerId, Integer companyId, Integer page, Integer limit);

    /**
     * 获取门店坐标
     */
    List<Map<String,Object>> getLatAndLon(CloudDealerSpecialStoreModel model);

    /**
     * 获取门店信息列表
     */
    List<CloudDealerSpecialStoreModel> getStoreInfo(CloudDealerSpecialStoreModel model);

    /**
     * 审核门店信息
     * @param model
     */
    void checkStore(CloudDealerSpecialStoreModel model);

    CloudDealerSpecialStoreModel getInfoById(Integer id);
}
