package com.intelliquor.cloud.shop.dealer.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 描述：云商关联关系实体类
 *
 * <AUTHOR>
 * @date 2021-01-06
 */
public class CloudDealerRelationModel {


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "关联类型，1：供货关联")
    private Integer type;

    @ApiModelProperty(value = "经销商/分销商id")
    private Integer dealerId;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "邀请码")
    private String inviteCode;

    @ApiModelProperty(value = "公司id")
    private Integer companyId;

    @ApiModelProperty(value = "关联类型，1：直接关联(以邀请码注册),2：间接关联(以邀请码建立关联)")
    private Integer relationType;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "建立关联的员工号id")
    private Integer createRelationStaffId;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "id";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getDealerId() {
        return dealerId;
    }

    public void setDealerId(Integer dealerId) {
        this.dealerId = dealerId;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateRelationStaffId() {
        return createRelationStaffId;
    }

    public void setCreateRelationStaffId(Integer createRelationStaffId) {
        this.createRelationStaffId = createRelationStaffId;
    }

    public String getSortRole() {
        return sortRole;
    }

    public void setSortRole(String sortRole) {
        this.sortRole = sortRole;
    }

    public String getSortCode() {
        return sortCode;
    }

    public void setSortCode(String sortCode) {
        this.sortCode = sortCode;
    }
}