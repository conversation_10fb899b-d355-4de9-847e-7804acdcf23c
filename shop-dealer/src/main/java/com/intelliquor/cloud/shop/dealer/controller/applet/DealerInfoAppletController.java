package com.intelliquor.cloud.shop.dealer.controller.applet;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.dealer.model.CloudDealerSpecialStoreModel;
import com.intelliquor.cloud.shop.dealer.model.DealerBusinessParamConfigModel;
import com.intelliquor.cloud.shop.dealer.model.DealerInfoModel;
import com.intelliquor.cloud.shop.dealer.model.req.DealerInfoReq;
import com.intelliquor.cloud.shop.dealer.model.resp.DealerDataOverviewResp;
import com.intelliquor.cloud.shop.dealer.model.resp.DealerInfoResp;
import com.intelliquor.cloud.shop.dealer.model.resp.DealerRankResp;
import com.intelliquor.cloud.shop.dealer.model.resp.TerminalShopResp;
import com.intelliquor.cloud.shop.dealer.service.DealerBusinessParamService;
import com.intelliquor.cloud.shop.dealer.service.DealerInfoService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: tianms
 * @Date: 2020/12/16 16:03
 * @Description: 云商用户信息控制层
 */
@Slf4j
@RestController
@RequestMapping("/dealerApplet/user")
public class DealerInfoAppletController {

    @Autowired
    private DealerInfoService dealerInfoService;

    @Autowired
    private DealerBusinessParamService dealerBusinessParamService;

    @Autowired
    private UserContext userContext;

    @ApiOperation(value = "云商小程注册", notes = "云商小程注册", httpMethod = "POST")
    @PostMapping(value = "/register")
    public Response<DealerInfoReq> register(@RequestBody DealerInfoReq dealerInfoReq) {
        return dealerInfoService.register(dealerInfoReq);
    }

    @ApiOperation(value = "云商小程序用户首页数据", notes = "云商小程序用户首页数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "云商用户id", required = true, paramType = "query", dataType = "int")
    })
    @GetMapping(value = "/getHomeData")
    public Response<DealerInfoResp> getHomeData() {

        try {
            log.info("开始执行getHomeData");
            Integer companyId = userContext.getUserInfo().getCompanyId().intValue();
            Integer id = userContext.getUserInfo().getUid();
            Response homeData = dealerInfoService.getHomeData(id, companyId);
            log.info("执行成功getHomeData");
            return homeData;
        } catch (Exception e) {
            log.error("云商小程序用户首页数据获取失败：{}", e);
            return Response.fail("数据获取失败，请重试");
        }
    }

    @ApiOperation(value = "获取商户详情", notes = "获取商户详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "云商用户id", required = true, paramType = "query", dataType = "int")
    })
    @GetMapping(value = "/getDealerUserData")
    public Response<DealerInfoResp> getDealerUserData(@RequestParam("id") Integer id) {
        try {
            Integer companyId = userContext.getUserInfo().getCompanyId().intValue();
            DealerInfoResp dealerInfoResp = dealerInfoService.getUserRecord(id, companyId);
            return Response.ok(dealerInfoResp);
        } catch (Exception e) {
            log.error("获取商户详情失败：{}", e);
            return Response.fail("获取商户详情失败，请重试");
        }
    }

    /**
     * 获取分销商列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param dealerInfoModel 其他查询条件
     * @return
     */
    @GetMapping(value = "/queryDistributorUserList")
    public Response<DealerInfoResp> queryDistributorUserList(@RequestParam(name = "startTime", required = false) String startTime,
                                                             @RequestParam(name = "endTime", required = false) String endTime,
                                                             DealerInfoModel dealerInfoModel) {
        try {
            // 公司id
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            // 经销商id
            dealerInfoModel.setId(userContext.getUserInfo().getUid());
            DealerInfoResp dealerInfoResp = dealerInfoService.queryDistributorUserList(dealerInfoModel,startTime,endTime);
            return Response.ok(dealerInfoResp);
        } catch (Exception e) {
            log.error("分销商列表数据获取失败：{}", e);
            return Response.fail("获取分销商列表失败，请重试");
        }
    }

    /**
     * 获取终端列表
     * @param keyword
     * @param status
     * @param type
     * @return
     */
    @GetMapping(value = "/queryTerminalList")
    public PageResponse<List<TerminalShopResp>> queryTerminalList(
            @RequestParam(name = "page", defaultValue = "1") Integer page,
            @RequestParam(name = "limit", defaultValue = "10") Integer limit,
            @RequestParam(name = "keyword", required = false) String keyword,
                                                             @RequestParam(name = "status", required = false) Integer status,
                                                             @RequestParam(name = "type", required = false) Integer type) {

            Map<String,Object> search = new HashMap<>();
            search.put("companyId", userContext.getUserInfo().getCompanyId().intValue());
            search.put("dealerId", userContext.getUserInfo().getUid());
            search.put("keyword", keyword);
            search.put("status", status);
            search.put("type", type);
            PageHelper.startPage(page, limit);
            List<TerminalShopResp> terminalList = dealerInfoService.queryTerminalList(search);
            PageInfo<TerminalShopResp> pageInfo = new PageInfo<>(terminalList);
            return PageResponse.ok(pageInfo);

    }

    /**
     * 查询终端信息用来生成发货对象或者关联合同接口
     */
    @GetMapping("/selectTerminalShopByShopId")
    public RestResponse<TerminalShopResp> selectTerminalShopByShopId(@RequestParam("id") Integer id,
                                                                     @RequestParam(value = "isCollection", required = false) Integer isCollection) {
        try {
            //获取当前
            TerminalShopResp selectData = dealerInfoService.selectTerminalShopByShopId(id,isCollection);
            //返回数据
            return RestResponse.success("查询成功", selectData);
        } catch (BusinessException e) {
            return RestResponse.error(Integer.parseInt(e.getCode()), e.getMessage());
        }
    }

    @ApiOperation(value = "修改信息", notes = "修改信息", httpMethod = "POST")
    @PostMapping(value = "/update")
    public RestResponse<String> update(@RequestBody DealerInfoModel dealerInfoModel) {
        try {
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            dealerInfoModel.setStaffId(userContext.getUserInfo().getUserId());
            return dealerInfoService.update(dealerInfoModel);
        } catch (Exception e) {
            log.error("云商小程序更新失败：{}", e);
            return RestResponse.error("云商小程序更新失败，请重试");
        }
    }

    @ApiOperation(value = "获取供货方列表", notes = "获取供货方列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "云商用户id", required = true, paramType = "query", dataType = "int"),
    })
    @GetMapping(value = "/querySupplierList")
    public Response<List<DealerInfoModel>> querySupplierList(DealerInfoModel dealerInfoModel) {
        try {
            // 公司id
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            // 分销商id
            dealerInfoModel.setId(userContext.getUserInfo().getUid());
            List<DealerInfoModel> supplierList = dealerInfoService.querySupplierList(dealerInfoModel);
            return Response.ok(supplierList);
        } catch (Exception e) {
            log.error("供货方列表数据获取失败：{}", e);
            return Response.fail("获取供货方列表失败，请重试");
        }
    }

    @ApiOperation(value = "添加供货方", notes = "保存信息", httpMethod = "POST")
    @PostMapping(value = "/saveSupplier")
    public RestResponse saveSupplier(@RequestBody DealerInfoModel dealerInfoModel) {
        try {
            if (dealerInfoModel == null) {
                return RestResponse.error("缺少参数");
            }
            Integer companyId = userContext.getUserInfo().getCompanyId().intValue();
            DealerBusinessParamConfigModel paramConfigModel = dealerBusinessParamService.getId(companyId);
            if (ObjectUtil.isNotEmpty(paramConfigModel) && paramConfigModel.getAddSupplier() == 0) {
                return RestResponse.error("目前不能自主添加供货方");
            }
            if (StringUtils.isEmpty(dealerInfoModel.getDealerInviteCode())) {
                return RestResponse.error("请输入供货方邀请码");
            }

            return dealerInfoService.saveSupplier(dealerInfoModel, userContext.getUserInfo());
        } catch (Exception e) {
            log.error("添加供货方失败：{}", e);
            return RestResponse.error(e.getMessage());
        }
    }

    @ApiOperation(value = "添加分销商", notes = "添加分销商", httpMethod = "POST")
    @PostMapping(value = "/insertDistributor")
    public RestResponse<String> insertDistributor(@RequestBody DealerInfoModel dealerInfoModel) {
        try {
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            dealerInfoModel.setSuperiorId(userContext.getUserInfo().getUid());
            return dealerInfoService.insertDistributor(dealerInfoModel);
        } catch (Exception e) {
            log.error("添加分销商失败：{}", e);
            return RestResponse.error("添加分销商失败，请重试");
        }
    }

    @ApiOperation(value = "更新经纬度信息", notes = "更新经纬度信息", httpMethod = "POST")
    @PostMapping(value = "/updateLongLat")
    public RestResponse<String> updateLongLat(@RequestBody DealerInfoModel dealerInfoModel) {
        try {
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            return dealerInfoService.updateLongLat(dealerInfoModel);
        } catch (Exception e) {
            log.error("更新经纬度信息失败：{}", e);
            return RestResponse.error("更新经纬度信息失败，请重试");
        }
    }

    @ApiOperation(value = "数据概览", notes = "数据概览", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, paramType = "query", dataType = "string")
    })
    @GetMapping(value = "/dataOverview")
    public RestResponse<DealerDataOverviewResp> dataOverview(@RequestParam(name = "startTime", required = true) String startTime,
                                                             @RequestParam(name = "endTime", required = true) String endTime,
                                                             DealerInfoModel dealerInfoModel) {
        try {
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            dealerInfoModel.setId(userContext.getUserInfo().getUid());
            startTime = startTime + " 00:00:00";
            endTime = endTime + " 23:59:59";
            DealerDataOverviewResp dataOverview = dealerInfoService.dataOverview(dealerInfoModel, startTime, endTime);
            return RestResponse.success(dataOverview);
        }  catch (Exception e) {
            log.error("数据概览获取失败：{}", e);
            return RestResponse.error("数据查询失败，请稍后重试");
        }
    }

    @ApiOperation(value = "验证分销商手机号是否存在", notes = "验证分销商手机号是否存在", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, dataType = "string")
    })
    @GetMapping(value = "/checkDistributorPhoneExist")
    public RestResponse<String> checkDistributorPhoneExist(DealerInfoModel dealerInfoModel) {
        try {
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            dealerInfoModel.setSuperiorId(userContext.getUserInfo().getUid());
            dealerInfoModel.setStaffId(userContext.getUserInfo().getUserId());
            return dealerInfoService.checkDistributorPhoneExist(dealerInfoModel);
        } catch (BusinessException e) {
            log.error("验证分销商手机号失败：{}", e);
            return RestResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("验证分销商手机号失败：{}", e);
            return RestResponse.error("验证分销商手机号失败，请重试");
        }
    }


    @ApiOperation(value = "分销商销售排行", notes = "分销商销售排行", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始日期", required = true, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束日期", required = true, paramType = "query", dataType = "string"),
    })
    @GetMapping("/queryDistributorSaleRankList")
    public RestResponse<List<DealerRankResp>> queryDistributorSaleRankList (@RequestParam(value = "startTime", required = true) String startTime,
                                                                            @RequestParam(value = "endTime", required = true) String endTime) {
        if (ObjectUtil.isEmpty(startTime) || ObjectUtil.isEmpty(endTime)) {
            return RestResponse.error("查询时间未传入");
        }
        startTime = startTime + " 00:00:00";
        endTime = endTime + " 23:59:59";
        Integer superiorId = userContext.getUserInfo().getUid();
        Integer companyId = userContext.getUserInfo().getCompanyId().intValue();
        List<DealerRankResp> rankList = dealerInfoService.queryDistributorSaleRankList(startTime, endTime, superiorId, companyId);
        return RestResponse.success(rankList);
    }


    @ApiOperation(value = "获取核心分销商详情", notes = "获取核心分销商详情", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "分销商id", required = true, paramType = "query", dataType = "int"),
    })
    @GetMapping(value = "/getDistributorRecord")
    public Response<DealerInfoResp> getDistributorRecord(@RequestParam(value = "id", required = true) Integer id) {
        try {
            DealerInfoModel dealerInfoModel = new DealerInfoModel();
            // 公司id
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            // 分销商id
            dealerInfoModel.setId(id);
            // 当前登录的经销商id
            dealerInfoModel.setSuperiorId(userContext.getUserInfo().getUid());
            DealerInfoResp distributorInfo = dealerInfoService.getDistributorRecord(dealerInfoModel);
            return Response.ok(distributorInfo);
        } catch (Exception e) {
            log.error("获取核心分销商详情失败：{}", e);
            return Response.fail("获取核心分销商详情失败，请重试");
        }
    }

    @ApiOperation(value = "申报分销商", notes = "申报分销商", httpMethod = "POST")
    @PostMapping(value = "/insertDistributor_V2")
    public RestResponse<String> insertDistributor_V2(@RequestBody DealerInfoModel dealerInfoModel) {
        try {
            dealerInfoModel.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
            dealerInfoModel.setSuperiorId(userContext.getUserInfo().getUid());
            dealerInfoModel.setStaffId(userContext.getUserInfo().getUserId());
            return dealerInfoService.insertDistributor_V2(dealerInfoModel);
        } catch (BusinessException e) {
            log.error("申报分销商_V2失败：{}", e);
            return RestResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("申报分销商_V2失败：{}", e);
            return RestResponse.error("申报分销商失败，请重试");
        }
    }

    @ApiOperation(value = "根据经销商编码和合同编码获取合伙人信息", notes = "根据经销商编码和合同编码获取合伙人信息", httpMethod = "GET")
    @GetMapping("/getPartnerListByDealerCodeAndContractCode")
    public RestResponse<List<DealerInfoModel>> getPartnerListByDealerCodeAndContractCode(@RequestParam String dealerCode,
                                                                                         @RequestParam String contractCode) {
        List<DealerInfoModel> list = dealerInfoService.getPartnerListByDealerCodeAndContractCode(dealerCode, contractCode);
        return RestResponse.success(list);
    }

    @ApiOperation(value = "根据经销商编码查询合伙人的上级是否是体验中心", notes = "根据经销商编码查询合伙人的上级是否是体验中心", httpMethod = "GET")
    @GetMapping("/getSuperDealerTypeByDealerCode")
    public Response<Boolean> getSuperDealerTypeByDealerCode(@RequestParam String dealerCode) {
        Boolean result = false;
        Integer accountType =  dealerInfoService.getSuperDealerTypeByDealerCode(dealerCode);
        if (accountType != null && accountType == 2) {
            result = true;
        }
        return Response.ok(result);
    }
}
