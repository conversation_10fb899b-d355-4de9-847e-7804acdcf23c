package com.intelliquor.cloud.shop.dealer.model.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2024/8/15 16:17
 */
@Data
public class CodeRuleCheckReq implements Serializable {

    /**
     * 奖励类型
     */
    @NotBlank(message = "奖励类型不能为空")
    private String rewardType;

    /**
     * 和酒瓶相关的码（包含箱码，盒码，瓶码等）
     */
    @NotEmpty(message = "相关的码不能为空")
    private List<String> codes;
}
