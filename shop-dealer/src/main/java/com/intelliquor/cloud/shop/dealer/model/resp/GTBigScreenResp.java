package com.intelliquor.cloud.shop.dealer.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GTBigScreenResp {

    //经销商
    @ApiModelProperty(value = "经销商类型Id")
    private String dealerTypeId;

    @ApiModelProperty(value = "经销商类型名称")
    private String dealerTypeName;

    @ApiModelProperty(value = "经销商注册数")
    private Integer registerNum = 0;

    @ApiModelProperty(value = "经销商进货数")
    private Integer scanNum = 0;

    //分销商
    @ApiModelProperty(value = "分销商注册总数")
    private Integer fxsRegisterTotalNum = 0;

    @ApiModelProperty(value = "分销商进货总数")
    private Integer fxsScanTotalNum = 0;

    //国粉
    @ApiModelProperty(value = "国粉今日注册数")
    private Integer memberRegisterDayNum = 0;

    @ApiModelProperty(value = "国粉累计注册数")
    private Integer memberRegisterTotalNum = 0;

    //注册分布图
    @ApiModelProperty(value = "类型1-经销商 2-分销商")
    private Integer type;

    @ApiModelProperty(value = "省")
    private String provinces;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区")
    private String district;

    @ApiModelProperty(value = "时间")
    private String day;
}
