package com.intelliquor.cloud.shop.dealer.factory;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.dealer.model.DealerScanCodeDetailModel;
import com.intelliquor.cloud.shop.dealer.model.DealerInfoModel;
import com.intelliquor.cloud.shop.dealer.model.DealerStaffModel;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020.09.10
 * 抽象接口
 */
public interface CompanyScanService {

    /**
     * 商户扫码接口层
     * @param companyId
     * @param qrcode
     * @param balanceId
     * @param cycleBalanceId
     * @param longitude
     * @param latitude
     * @param memberUserId
     * @param payType
     * @param userModel
     * @return
     * @throws Exception
     */
    Map<String, String>  scanCode(Integer companyId, String qrcode, Integer balanceId, Integer cycleBalanceId, Double longitude, Double latitude, Integer memberUserId, Integer payType, DealerStaffModel userModel) throws Exception;

}
