package com.intelliquor.cloud.shop.dealer.model.resp;

import lombok.Data;
import org.apache.kafka.common.protocol.types.Field;

/**
 * 国台终端同步返回实体
 */
@Data
public class CloudDealerGtTerminalResp {
    /**
     * 中台的id
     */
    private String zhogntaiId;
    /**
     * 终端编码
     */
    private String terminalCode;
    /**
     * 副编码
     */
    private String deputyCode;
    /**
     * 营业执照编码
     */
    private String licenseCode;
    /**
     * 终端负责人联系电话
     * 如果智盈根据营业执照查到终端信息，这里返回智盈的电话
     * 如果查不到返回额就是中台提供的电话
     */
    private String phone;

}
