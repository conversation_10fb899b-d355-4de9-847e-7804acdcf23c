package com.intelliquor.cloud.shop.dealer.service;

import com.intelliquor.cloud.shop.dealer.model.DealerShopModel;
import com.intelliquor.cloud.shop.dealer.model.req.ShopModelReq;
import com.intelliquor.cloud.shop.dealer.model.resp.*;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2021/01/11 10:26
 * @Description: 云商终端店接口
 */
public interface DealerShopService {

    /**
     * 功能描述: 查询终端列表
     *
     * @param mapUtils
     * @return com.intelliquor.cloud.shop.dealer.model.DealerShopModel
     * @auther: tms
     * @date: 2021/01/11 11:16
     */
    List<DealerShopModel> queryShopList(MapUtils mapUtils);

    /**
     * 功能描述: 查询终端点详情
     *
     * @param param
     * @return com.intelliquor.cloud.shop.dealer.model.DealerShopModel
     * @auther: tms
     * @date: 2021/01/11 13:27
     */
    DealerShopModel getShopDetailed(MapUtils param);

    /**
     * 功能描述: 终端店进货明细
     *
     * @param param
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.util.MapUtils>
     * @auther: tms
     * @date: 2021/01/12 10:29
     */
    List<MapUtils> queryPurchaseList(MapUtils param);

    /**
     * 功能描述: 获取我的终端数
     *
     * @param dealerCode
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/01/12 20:28
     */
    Integer totalShopNum(String dealerCode);

    /**
     * 功能描述: 获取我的终端扫码数
     *
     * @param dealerCode
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/01/12 20:28
     */
    Integer totalShopScanCodeNum(String dealerCode);

    /**
     * 终端店信息编辑
     * <AUTHOR>
     * @createTime 2021/3/16 17:36
     * @return
     */
    Integer dealerShopUpdate(ShopModelReq model);

    /**
     * 变更业务员
     * @param customersId
     * @param visitorId
     * @return
     */
    Integer changeSalesman(Integer customersId, Integer visitorId);

    /**
     * 解除绑定
     * @param customersId
     * @param visitorId
     * @return
     */
    Integer Unbind(Integer customersId, Integer visitorId);

    /**
     * 新增绑定
     * @param customersIds
     * @return
     */
    void addBind(String customersIds, Integer visitorId);

    /**
     * 终端店详情 (业务数据)
     * @param param
     * @return
     */
    ScanCodeResp getTerminalStoreDetailsa(MapUtils param);

    /**
     * 查询业务员对应终端列表
     * @param param
     * @return
     */
    List<DealerShopResp> getSalesmanTerminalList(MapUtils param);

    /**
     * 查询经销商对应终端列表
     * @param param
     * @return
     */
    List<DealerShopResp> getDealerTerminalList(MapUtils param);

    /**
     * 查询经销商未绑定业务员终端列表
     * @param param
     * @return
     */
    List<DealerShopResp> getDealerUnboundTerminalList(MapUtils param);

    /**
     * 拜访列表
     * @param param
     * @return
     */
    List<VisitRecordResp> getVisitRecordList(MapUtils param);

    /**
     * 扫码进货列表
     * @param param
     * @return
     */
    List<ScanCodePurchaseResp> getPurchaseList(MapUtils param, Integer page, Integer limit);

    /**
     * 拜访对象（终端）
     * @param param
     * @return
     */
    List<VisitingObjectResp> getVisitingObjectList(MapUtils param);

    /**
     * 终端基础信息
     * @param shopId
     * @return
     */
    ShopResp getShopBasicInfo(Integer shopId,Integer dealerId);

    Integer getPlan(Integer customersId, Integer visitorId);

    ShopPurchNum getPurchNum(MapUtils param);
}
