package com.intelliquor.cloud.shop.dealer.controller.open;

import com.alibaba.fastjson.JSON;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.GuotaiUtil;
import com.intelliquor.cloud.shop.dealer.model.req.GtCloudBanquetOrderModel;
import com.intelliquor.cloud.shop.dealer.service.GtCloudBanquetOrderService;
import com.intelliquor.cloud.shop.dealer.util.DesUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 国台宴席单同步接口
 * 溯源发货后，会将扫码出库的内码传过来
 */
@Slf4j
@RestController
@RequestMapping("/open/banquetOrder")
public class GtCloudBanquetOrderController {

    @Autowired
    private GtCloudBanquetOrderService gtCloudBanquetOrderService;

    @PostMapping("/send")
    public Response<String> send(@RequestBody GtCloudBanquetOrderModel gtCloudBanquetOrderModel, HttpServletRequest request){

        log.info("国台云宴回调参数=={}", JSON.toJSONString(gtCloudBanquetOrderModel));

        String token = request.getHeader("token");
        String timestampStr = request.getHeader("timestamp");
        log.info("国台云宴回调token：{},timestamp:{}",token,timestampStr);
        //校验Token
        try{
            GuotaiUtil.verifyToken(token,timestampStr);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }

        try{
            gtCloudBanquetOrderService.order(gtCloudBanquetOrderModel);
        }catch (BusinessException e){
            return Response.fail(e.getMessage());
        }


        return Response.ok("成功");
    }

}
