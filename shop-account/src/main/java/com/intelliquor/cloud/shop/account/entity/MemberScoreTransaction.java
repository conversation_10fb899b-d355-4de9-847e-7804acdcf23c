package com.intelliquor.cloud.shop.account.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员积分交易表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_member_score_transaction")
public class MemberScoreTransaction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 事项类型
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 交易积分
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 事项发生时间
     */
    @TableField("transaction_time")
    private LocalDateTime transactionTime;

    /**
     * 总账id
     */
    @TableField("score_account_id")
    private Long scoreAccountId;

    /**
     * 事项描述
     */
    @TableField("description")
    private String description;

    /**
     * 业务单据号 可能是reward中的id 可能是display_id
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 业务来源
     */
    @TableField("business_source")
    private String businessSource;

    @TableField("product_sku")
    private String productSku;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    @TableLogic
    private Integer deleteFlag;

    /**
     * 事项流水号
     */
    @TableField("transaction_no")
    private String transactionNo;

}