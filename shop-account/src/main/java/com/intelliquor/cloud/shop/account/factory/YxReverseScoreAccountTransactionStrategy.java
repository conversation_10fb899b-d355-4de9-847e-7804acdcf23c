package com.intelliquor.cloud.shop.account.factory;

import com.intelliquor.cloud.shop.account.command.CreateMemberScoreTransactionCommand;
import com.intelliquor.cloud.shop.account.command.YxReverseScoreAccountCommand;
import com.intelliquor.cloud.shop.account.drools.bo.ScoreIncomeAndExpensesAvailableAccountBO;
import com.intelliquor.cloud.shop.account.drools.service.MemberScoreAccountRevenueAndExpenditureRuleService;
import com.intelliquor.cloud.shop.account.dto.ProductInfo;
import com.intelliquor.cloud.shop.account.dto.transaction.resp.MemberScoreTransactionRespDTO;
import com.intelliquor.cloud.shop.account.entity.MemberScoreDetailAccount;
import com.intelliquor.cloud.shop.account.entity.MemberScoreEntry;
import com.intelliquor.cloud.shop.account.entity.MemberScoreTransaction;
import com.intelliquor.cloud.shop.account.enums.MemberScoreTransactionTypeEnum;
import com.intelliquor.cloud.shop.account.enums.ScoreOperateEnums;
import com.intelliquor.cloud.shop.account.service.IMemberScoreAccountService;
import com.intelliquor.cloud.shop.account.service.IMemberScoreEntryService;
import com.intelliquor.cloud.shop.account.service.IMemberScoreTransactionService;
import com.intelliquor.cloud.shop.common.basequery.lambda.LambdaUtils;
import com.intelliquor.cloud.shop.common.basequery.utils.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("YX_REVERSE_SCORE_ACCOUNT")
public class YxReverseScoreAccountTransactionStrategy extends ScoreTransactionFactory<YxReverseScoreAccountCommand> {

    @Autowired
    private IMemberScoreAccountService memberScoreAccountService;

    @Autowired
    private IMemberScoreTransactionService memberScoreTransactionService;
    @Autowired
    private IMemberScoreEntryService memberScoreEntryService;

    @Autowired
    private MemberScoreAccountRevenueAndExpenditureRuleService memberScoreAccountRevenueAndExpenditureRuleService;

    private static final ScoreOperateEnums operateType=ScoreOperateEnums.EXPENSES;


    @Override
    @Transactional
    public MemberScoreTransactionRespDTO createTransaction(CreateMemberScoreTransactionCommand<YxReverseScoreAccountCommand> command) {
        YxReverseScoreAccountCommand transactionDetail = command.getTransactionDetail();
        if(transactionDetail!=null){
            ScoreIncomeAndExpensesAvailableAccountBO scoreIncomeAndExpensesAvailableAccount = memberScoreAccountRevenueAndExpenditureRuleService.getMemberScoreAccountByScoreOperate(
                    command.getMemberId()
                    , ListUtils.list2Dot(ListUtils.property2Array(transactionDetail.getProduct(), LambdaUtils.property(ProductInfo::getProductSku)))
                    , "", command.getTransactionType(), operateType);

            //创建事项
            MemberScoreDetailAccount scoreDetailAccount = scoreIncomeAndExpensesAvailableAccount.getMemberScoreDetailAccount();
            MemberScoreTransaction memberScoreTransaction= MemberScoreBuilder.buildMemberScoreTransaction(command,transactionDetail,scoreDetailAccount, operateType);
            memberScoreTransactionService.save(memberScoreTransaction);

            MemberScoreEntry memberScoreEntry=MemberScoreBuilder.buildMemberScoreEntry(memberScoreTransaction,command,transactionDetail,scoreDetailAccount,operateType);
            MemberScoreBuilder.initRule(memberScoreEntry,scoreIncomeAndExpensesAvailableAccount);
            memberScoreEntry.setProductSku(ListUtils.list2Dot(ListUtils.property2Array(transactionDetail.getProduct(),ProductInfo::getProductSku)));

            memberScoreEntryService.save(memberScoreEntry);
            memberScoreEntryService.memberScoreChange(memberScoreEntry,operateType);
        }

        return MemberScoreTransactionRespDTO.newInstance(Boolean.TRUE,String.format("%s支出成功", MemberScoreTransactionTypeEnum.fromName(command.getTransactionType()).getDescription()));
    }
}
