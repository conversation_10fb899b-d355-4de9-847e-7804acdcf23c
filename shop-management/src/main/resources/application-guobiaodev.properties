#å¬å±æ¨¡åéç½®
spring.profiles.include=gbstest
# å½å°æå¡å¨æµè¯ç¯å¢
server.port=8888
server.servlet.context-path=/guotai-shop

#æ°æ®åºéç½®
spring.datasource.url=*****************************************************************************************************************************************************
spring.datasource.username=jiyouai
spring.datasource.password=JiYouAi@)20
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.initialSize=50
spring.datasource.minIdle=5
spring.datasource.maxActive=1000
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.filters=stat,slf4j
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.mvc.throw-exception-if-no-handler-found=true
spring.resources.add-mappings=false
logging.level.com.intelliquor.cloud.shop=info
logging.path=/home/<USER>/guotai-shop


#pagehelper
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql
pagehelper.page-size-zero=true
spring.mvc.favicon.enabled=false
swagger.title=\uFFFD\u01B5\uFFFD
swagger.version=0.0.1
swagger.contact.name=\uFFFD\u01B5\uFFFD
swagger.base-package=com.intelliquor.cloud.shop
swagger.base-path=/**

# å¼æ¾å¹³å°éç½®
wechat.open.componentAppId=wxae44190677cf893c
wechat.open.componentSecret=34fe77ee49e3a9c022968b0641751c2b
wechat.open.componentToken=intelliquorcom
wechat.open.componentAesKey=abcdefghijkluvwxmnopqrstyz0123456789ABCDEFG
wechat.redis.host=************
wechat.redis.password=guotai2020
wechat.redirect.url=http://testgbsszhqd.guotaijiu.com/cloudshop/base-setting/wechat-mini
wechat.referer.url=testgbsszhqdapi.guotaijiu.com/guotai-shop

#å°ç¨åº
APP_ID_NEW=wxc496d270e7c090ba
APP_SECRET_NEW=6c28f7fb942295f6dc6d4bbef8ec4fcc
SESSION_KEY_URL=https://api.weixin.qq.com/sns/jscode2session

#oss
aliyunoss.folder =cloud-shop
aliyunoss.folder.order=cloud-shop-orderExcel
aliyunoss.key=LTAIMbj56ZgbgDPP
aliyunoss.secret=yoEW6KNqanLGTaThshn4bK9FIVdWL6
aliyunoss.bucket=dealer-saas
aliyunoss.domain=oss-cn-beijing.aliyuncs.com
local.order.folder=orderFolder

#æºèµ¢ç»å½æ¥å£
zhiying.url=http://testgf.guotaijiu.com/customer/base/login
#ä¼å£«éæéé
finance.channel=1003
#ä¼å¸éæ
finance.queryInformation=http://*************:19080/mbx/rest/resource/fy/queryInformation/0/v1
finance.queryYcInformation=http://*************:19080/mbx/rest/resource/yc/listUserInfo/0/v1
finance.getVirtualCards=http://*************:19080/mbx/rest/resource/virtualCard/getVirtualCards/0/v1

#ä¼å¸éææ¯ä»å°å
finance.payurl=http://*************:7000/suning/order/pay

#äºä»åè®®å¡æç»
finance.listVirtualCardUrl = http://*************:19080/mbx/rest/resource/yc/listVirtualCard/0/v1
#ä¼å¸éæåè°å°å
finance.payCallBackUrl=http://cloudshopapi.baijiuyun.com/wechat/customerPaymentRecord/payCallBack
#ä¼å£«éæè®¢åéç¥æ¥å£
finance.youshiNotifyOrderUrl = https://mbx.1532mall.net/mbx/rest/resource/retailer/consume/0/v1
#ä¼å£«éæåç¥¨å®¡æ ¸æ¥å£
finance.youshiInvoiceAuditUrl = https://mbx.1532mall.net/mbx/rest/resource/dealer/consume/0/v1
#æ¥è¯¢éåæ°æ®
finance.youshi.display.url=http://*************:9088/mbx/rest/resource/platFormAccess/ycshow/0/v1

#é¢å®å¡åå¼æ¥å£
pre.sale.finance.pay=https://youmkt.1532mall.net/suning/event/agent_yc_apply
#é¢å®å¡åè°å°å
pre.sale.finance.payCallBackUrl=https://cloudshopapi.baijiuyun.com/preSale/wechat/payCallBack
#é¢å®å¡è·åæä¿¡é¢åº¦æ¹å¼
pre.sale.credit.env=2
#ç­¾ç½²åè®®åè°æ¥å£
agreement.sign.payCallBackUrl = http://39.106.0.169:8888/agreement/wechat/payCallBack
#æ¥è¯¢ç­¾è®¢åè®®å»ç»éé¢
agreement.youshi.show.url = http://*************:9088/mbx/rest/resource/platFormAccess/ycjzxs/0/v1

#äºä»¶å°å
data.center.url=http://************:7001/manage/data-api/event/w/add
data.center.token=1121711460351737946
data.center.modelId=216
data.center.userId=14
data.center.sourceSystem=jz
data.center.baseurl=http://************:7001/manage
data.center.businessId=14


#æºçåç½å°å
#intelliquor.url=http://api.baijiuyun.com
intelliquor.url=http://************
intelliquor.token=9Lps+Ux4nbekCOB16gXyAtDmAMpRdAzZM41wtYXkRLnjaF9PIwGos8tE6Nd3G/QC


#å°äº«åè°å°å
zx.pay.notifyUrl=http://cloudshopapi.baijiuyun.com/wechat/orderZx/payNotify
zx.server.url=http://union.intelliquor.com
zx.server.token=23c32669-b92c-44cf-84c6-e10e9961450c

# Redis
spring.redis.database=0
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=guotai2020
spring.redis.timeout=10000

#jest setting
#spring.elasticsearch.jest.uris=http://************:9200,http://************:9200,http://************:9200
spring.elasticsearch.jest.uris=http://************:9200
spring.elasticsearch.jest.read-timeout=10s
spring.elasticsearch.jest.connection-timeout=10s
spring.elasticsearch.jest.multi-threaded=true
spring.elasticsearch.jest.username=elastic
spring.elasticsearch.jest.password=JiYouAi@)19

#kafka
spring.kafka.bootstrap-servers=************:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.consumer.group-id=guotai-cloud-shop
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.max-poll-records=30
spring.kafka.listener.ack-mode=manual_immediate

kafka.system.order.topic=systemOrderTopic

#æºçæ°æ®åºéç½®
dmp.datasource.url=*************************************************************************************************************************
dmp.datasource.username=market
dmp.datasource.password=market_2019_802
dmp.datasource.driver=com.mysql.jdbc.Driver



#æ¯èå¬å¸ID
companyId.default=14

#æ¯èå¼ç¥¨å®¡æ¹éç¥å·ç 
dealer.phone=17806249386


server.host.url=http://cloudshopapi.baijiuyun.com
buycard.wechatpaymentcallback.url=${server.host.url}/buycardRechargeOrder/wechatPaymentCallback


#éå®è®¢åæ¯ä»åè°
order.pay.notifyUrl=${server.host.url}/order/payNotify


#äºå¢Hostå°å
yuntuan.appid=wxdaafdea51c10fa73
yuntuan.host.url=http://yt.baijiuyun.com
yuntuan.share.head.url=http://dealer-saas.oss-cn-beijing.aliyuncs.com/cloud-shop/*************.png

#å®¡æ ¸ç»æéç¥æ¨¡æ¿ID
APP_CHECKRESULT_MESSAGE_TEMPLATEID=KLJajW38Dulz074fqI9ohSfU77x6ya1Q9bUHwtO0MrM
#æ¶çå°è´¦éç¥æ¨¡æ¿ID
APP_INCOMETOACCOUNT_MESSAGE_TEMPLATEID=Lh1J2Ic2tQK8BAf7xNUsdKqsLMIfCzGH0jgWOpPyjGU


#å¯¹æ¥äºä¼
shopMember.baseUrl=http://testgf.guotaijiu.com/member-api
shopMember.addScanOrderUrl=${shopMember.baseUrl}/data-api/memberOpen/addOrder
shopMemebr.updScanOrderUrl=${shopMember.baseUrl}/data-api/memberOpen/updateOrder
shopMember.addShopInternalUrl=${shopMember.baseUrl}/data-api/memberOpen/addShopIntegral
shopMemebr.groupOrderRefundUrl=${shopMember.baseUrl}/data-api/memberOpen/groupOrderRefund
shopMemebr.getInfoByCompanyUrl=${shopMember.baseUrl}/data-api/member/applet/getInfoByCompany
shopMemebr.getDefaultModelIdUrl=${shopMember.baseUrl}/data-api/memberOpen/getDefaultModelId
shopMemebr.getLiveActivityUrl=${shopMember.baseUrl}/data-api/memberOpen/findLiveByShop
shopMemebr.getLiveOrderListUrl=${shopMember.baseUrl}/data-api/member/liveOrder/searchPageListByShop
shopMemebr.getLiveOrderDetailUrl=${shopMember.baseUrl}/data-api/member/liveOrder/getLiveOrderDetail/{orderCode}
shopMemebr.updateLiveOrderUrl=${shopMember.baseUrl}/data-api/member/liveOrder/updateLiveOrder
shopMemebr.ladderGroupOrderRefundUrl=${shopMember.baseUrl}/data-api/memberOpen/ladderOrderRefund

data.center.model-event-save-url=http://manage-api.baijiuyun.com/gt/data-api/event/w/add

#æ¯è-éè¿å¸çç è·åå¯¹åºåç çæ°éæ¥å£
data.center.scanBoxCodeNumUrl=${data.center.baseurl}/data-api/code/r/ticketBoxInnerCodeQty/{code}

encryptJWTKey: U0JBUElKV1RkV2FuZzkyNjQ1NA==

groupBuy.payUrL=http://api.baijiuyun.com/thirdpart/wechat/wechatPay
groupBuy.sendTemplateMessageSubscribeUrL=http://api.baijiuyun.com/thirdpart/Wechatminiprogram/sendTemplateMessageSubscribe

#æ´»å¨ç»æãæ¼å¢è¿æ åèµ·éæ¬¾
cloud-shop.teamFaild.scheduled=0 0/10 * * * ?
#ä¸å¤©èªå¨æ¶è´§
cloud-shop.autoConfirmReceived.scheduled=0 0 1 1/1 * ?
cloud-shop.autoConfirmReceivedFlag=N
#æ¨¡ææå¢
cloud-shop.autoComplete.scheduled=0 0/10 * * * ?
#æ¼å¢è®¢åç¶æèªå¨å¤ç
cloud-shop.handelOrderStatus.scheduled=0 2/10 * * * ?
#æ´»å¨å¼å§åéè®¢ééç¥
cloud-shop.activityStartMessage.scheduled=0 3/10 * * * ?
#20åè°
20zhi.finance.payCallBackUrl=http://cloudshopapi.baijiuyun.com/dealerOrder/payCallBack
dealer.url=http://20zhiapi.intelliquor.com
20zhi.finance.supplierId=5

#ç´¯è®¡å¾è§£éæ¥å£
sendUnlockUrl=${intelliquor.url}/market-api/draw/cumulk/sendUlkPrize

#éå¶ä¸ä¼ æä»¶å¤§å°
spring.servlet.multipart.max-file-size = 50MB
spring.servlet.multipart.max-request-size = 50MB
server.tomcat.max-swallow-size = 50MB
#æ¯å¦å¼å¯å®æ¶ä»»å¡
cloud-shop.task.enable = N

#å¤è´æ¥ä½¿ç¨codeè¯·æ±
code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfoByType
#ç¶åç è·åæ°æ®
internal.code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfo/
#æ ¹æ®ç¶åç æ¥è¯¢ä¿¡æ¯ä»æ°æ®ä¸­å°(ææ°çä»£ç )
code_info_inner.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfoV2
#æ¯èçcompanyId
jz-company.id=14
#æ¯é±companyId
JJ-company.id=55
#å¤è´æ¥companyId
GBC-company.id=47
#çº¢æcompanyId
Hx-company.id=54
#ççå°companyId
lyt-company.id = 83
#éé±¼å°companyId
dyt-company.id=82
#æµ·å³æ¥ç¨companyId
haiguan-company.id = 56
#æµ·å³æ¥ç¨apiå°å7
haiguan-api=https://api.dm.intelliquor.com/ch-company-api/
#å½å°companyId
guo.tai.company.id=50

#åæ­¥ç»éåæ°æ®
cloud-shop.dealerAccount.scheduled=0 0 3 * * ?

#å¤è´æ¥æ¥æ
reward.config.date=2020-08-03 21:00:00

#å¤è´æ¥æ ¹æ®ç¶ç è·åå¥å±
reward.bottle.url=http://172.16.99.33:8081/gbc/clothAwardByVft

#å¤è´æ¥æ ¹æ®ç®±ç  è·åå¥å±
reward.box.url=http://172.16.99.33:8081/gbc/clothAwardByVfb

#æ¥è¯¢æºèµ¢å¥å±ç±»å1
dm.reward.type.url=http://************:7009/market-v2/prize/page

#æ¥è¯¢æºèµ¢åºåºæ°é
dm.out.stock.url=https://api.baijiuyun.com/market-v2/hxbox-api/outStockAndOpenBottleQty

#æ¥è¯¢æºèµ¢ååä¸åæ°é
dm.goods.num.url=https://api.baijiuyun.com/market-v2/hxbox-api/prdtOutStock

#æ¥è¯¢ç®±ä¸é¢çç¶ç 
box_code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfoMap/

#æ¥è¯¢çº¢æç®±ç ä¸çç¶ç æ°æ®
hx_box_code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfoMapV2

#æ¯å¦å¼å¯å¾®ä¿¡åéä¿¡æ¯å®æ¶ä»»å¡
wx-send.task.enable = N

#æ¯å¦å¼å¯é¶æ¢¯å¢è®¢å7å¤©èªå¨æ¶è´§å®æ¶ä»»å¡
ladder-order.autoConfirm.task.enable = N

#é¶æ¢¯å¢è®¢å7å¤©èªå¨æ¶è´§å®æ¶ä»»å¡1:20
cloud-shop.ladder-order.autoConfirmReceived.scheduled=0 20 1 1/1 * ?
#é¶æ¢¯å¢æ¯å¦èªå¨æ¶è´§
cloud-shop.ladder-order.autoConfirmReceivedFlag=N
#ç»ä¸å¢æ¯å¦èªå¨æ¶è´§
ladder-order.handelOrderStatus.task.enable = N
#æ¼å¢è®¢åç¶æèªå¨å¤ç
cloud-shop.ladder-order.handelOrderStatus.scheduled=0 2/10 * * * ?
#é¶æ¢¯æ¼å¢å¢å®æåå åéå»å¤ç è®¢åç¶æ
complate-groupon-time = 2

#é¶æ¢¯æ¼å¢è®¢åéæ¬¾å®æ¶æ¶é´
laddder-order.refund.scheduled=0 0/5 * * * ?

#é¶æ¢¯æ¼å¢è®¢åæå¢å®æ¶æ§è¡æ¶é´
ladder-order.groupon.complete.scheduled=0 0/1 * * * ?

#é¶æ¢¯æ¼å¢è®¢åå¤çæå¢è®¢åå®æ¶æ§è¡æ¶é´
ladder-order.order.complete.scheduled=0 0/2 * * * ?

#åæ­¥çº¢ææ°æ®
hx.scan.scheduled=0 0/15 * * * ?
#å¾®ä¿¡ç¬¬ä¸æ¹å¹³å°æå¡å¨åå
wechat.requestdomain.url = https://api.baijiuyun.com;https://cloudshopapi.baijiuyun.com;https://manage-api.baijiuyun.com
wechat.wsrequestdomain.url = wss://api.baijiuyun.com
wechat.uploaddomain.url = https://api.baijiuyun.com;https://cloudshopapi.baijiuyun.com
wechat.downloaddomain.url = https://api.baijiuyun.com;https://thirdwx.qlogo.cn;https://wx.qlogo.cn;https://dealer-saas.oss-cn-beijing.aliyuncs.com
#å°ç¨åºå·²å³èç±»ç®ä¸è·åæ¨¡æ¿å³é®å­çurl-get
wechat.getKeyWordsByTemplateUrlRefCate = https://api.weixin.qq.com/wxaapi/newtmpl/getpubtemplatekeywords?access_token=
#å°ç¨åºææç±»ç®ä¸è·åæ¨¡æ¿å³é®å­çurl-post
wechat.getKeyWordsByTemplateUrl = https://mp.weixin.qq.com/wxamp/cgi/newtmpl/get_pubtmplbytid?access_token=
#æ·»å è®¢éæ¶æ¯æ¨¡æ¿url-post
wechat.addTemplateUrl = https://api.weixin.qq.com/wxaapi/newtmpl/addtemplate?access_token=

#æ¯é±äºéæ«ç åéç¥æºççæ¥å£
jj.zy.outToTerminal.url = http://manage-api.baijiuyun.com/gt/hxcode-sync-api/outToTerminalV2
#æ¯é±äºéæ«ç åéç¥æºçæ¥å£çå¬é¥
jj.zy.public.key = FD25C171C0726CA6B4E56C48B59D55B2

#æ³°å®-æ¥è¯¢ç çåºåºæ¶é´
tai.bao.websrv_qrcode = http://************:6010/websrv_qrcode.asmx/GetOutInfolist
tai.bao.pass = aHMzNWFrZWpka2lxdXJncnRoZ3l3d2l2OHh2anAyaWs1Z2h4Nm4xNHQ2YjhtMjY3djc=

#çº¢æè§£æç 
hx-code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/hxCodeInfoByType

#é®ç®±éç½®
spring.mail.host = smtp.exmail.qq.com
spring.mail.username = <EMAIL>
spring.mail.password = d4ypUCvHtdJe6bWb
spring.mail.properties.mail.smtp.port = 465
spring.mail.sender = <EMAIL>
spring.mail.receivers = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
default-encoding = utf-8
spring.mail.properties.mail.smtp.starttls.enable = true
spring.mail.properties.mail.smtp.starttls.required = true
spring.mail.properties.mail.smtp.ssl.enable = true
spring.mail.default-encoding = utf-8


lyt.model.data.save.url = http://manage-api.baijiuyun.com/gt/data-api/v2/model/w/save
lyt.delaer.model.id = 383
lyt.shop.model.id = 381
lyt.sync.enable = N

lyt.token = 1343510632359526444
#åä¿¡ç­ä¿¡è¯·æ±å°å
huaxin.sms.url=http://sh2.ipyy.com/smsJson.aspx
huaxinbj.sms.url=https://dx.ipyy.net/smsJson.aspx

#ä¼å¸åè®®åè´¦æ¯ä¾
youshi.agreement.ledger.ratio=30

#æ¯èèå èåè¡¨t_member_function
jz_menu_ids=2,4,5,6,7,11,13,37,38,41,42,48,49

yz.getEntrepotOutToTerminalUrl=http://api.dm.intelliquor.com/data/data-api/entrepotOutToTerminal/getListByPage

#äºååå¼åè®®æ¥å£
ys.agreement.recharge.url = https://t-mbx.1532mall.net/mbx/rest/resource/dealer/recharge/0/v1
#äºååå¼åè®®åè°æ¥å£
ys.agreement.recharge.payCallBackUrl = http://cloud-shop-api.intelliquor.com/agreement/business/payCallBack
#ç»ç«¯åå¼åè®®æ¥å£
zd.agreement.recharge.url = https://t-mbx.1532mall.net/mbx/rest/resource/customer/recharge/0/v1
#ç»ç«¯åå¼åè®®åè°æ¥å£
zd.agreement.recharge.payCallBackUrl = http://cloud-shop-api.intelliquor.com/agreement/wechat/cloudStorePayCallBack

#ç¨äºå°ç¨åºåç,æ¹ææºå·å¯ä»¥å¡«åä»»æéªè¯ç 
gtsyn.gtDealerUrl=http://xzt.jscssui.com:9527/jeecg-boot/web/api/analyze/customer/pageList?pageNo={pageNo}&pageSize={pageSize}
gtsyn.gtOrderDeleteUrl=https://gtsytest.tasly.com/enterprisewx/wx/zy/delivery/order/delete/{code}
gtsyn.gtEsSynUrl=http://api.dm.intelliquor.com/data/data-api/v2/model/w/save
gtsyn.gtEsSynUserId=219
gtsyn.gtEsSynModelId=697
gtsyn.gtEsSynToken=1343736924237463614
#å½å°åæ­¥éç½®
gtsyn.accessKey=GZDCs8ko
gtsyn.accessSecret=i5qqr7OyTXwsB83CINtDc7xUfw6H67pv
#æ ¹æ®ææºå·è·åç»éå/åéåä¿¡æ¯
#gtsyn.checkPhoneUrl=https://gtsytest.tasly.com/enterprisewx/auth/zytrace/queryDistributionByConnectPhone
gtsyn.checkPhoneUrl=https://gtsytest.tasly.com/enterprisewx/auth/zytrace/queryDistributionByConnectPhone
#è·åtoken
gtsyn.accessTokenUrl=https://gtsytest.tasly.com/enterprisewx/auth/zytrace/accessToken

gtsyn.synOrderUrl = https://gtsytest.tasly.com/enterprisewx/auth/zytrace/syncOrderList
#è¿ç»´ç¨
gtsyn.synOrderOpsUrl = https://gtsytest.tasly.com/enterprisewx/auth/zytrace/syncOrderListOps

#å¥åº·é¥®éä¿±ä¹é¨
drink_club_manage_api.url=http://10.32.176.20:7111/drink_club_manage_api

#è¿éçwxAppIdåagentIdæ¯æº¯æºåç«¯ç¨ç
#https://www.yuque.com/docs/share/1eae9f92-eca2-4e2f-88a2-8a0e83c8983b å¯ç lg0c
guotai.suyuan.wxAppId=ww923d07042ff2a178
guotai.suyuan.agentId=1000002

# æºç,è·åä¸å¡åºå
zy.api.getBusinessAreaUrl=http://************:7002/v2/zy/company/getBusinessArea

#è·åè§æ ¼
goods.queryByCode.url = http://manage-api.baijiuyun.com/product-api/findOneByCode

# æºç,è·åä¸å¡åºå
zy.api.businessAreaListUrl=http://api.baijiuyun.com/v2/businessArea/list4Leader

#ç¨äºå°ç¨åºåç,æ¹ææºå·å¯ä»¥å¡«åä»»æéªè¯ç 
special_phone = ***********


gt_company_id=50



wechat.getPhoneNumberUrl = https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=

#ç»ç«¯å¯ç å å¯å¯é¥
terminal_sign_key=1658459771

#ä»æº¯æºè·åç ä¿¡æ¯
xuanwu.getCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/
#ä»æº¯æºè·åè¿è´§åä¸çææç ä¿¡æ¯
xuanwu.getOrderCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/receive/
#æåæ¶è´§åæ­¥è³æº¯æº
xuanwu.sendOrderCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/confirm-receive
#æç æ¶è´§åæ­¥è³æº¯æº
xuanwu.sendCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/scan-receive

#åæº¯æºåæ­¥æ«ç åºåºæ°æ®
xuanwu.sendCodeInfo2Sy.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/confirm-delivery
#åæº¯æºåæ­¥åéååè´§ä¿¡æ¯
xuanwu.sendScanOut.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/confirm-delivery

#å¨æå¥å±ä»»å¡å¼å³
cloud-shop.cycle.task.enable = Y
#å½æ æ¾åç¬éç½®ï¼è¦çcommonä¸­çéç½®
xuanwu.accountinfocode = 1733052964442083328
xuanwu.opentypecode = guobiaoshi-data-server
xuanwu.opentypesecret = gbs8888
xuanwu.clienttypecode = 1
xuanwu.getContractMessage = http://121.37.206.131:7000/api/teapi/dy-biz/1508478852203155529/1531126788330229859
xuanwu.getContractListFromXwUrl = http://121.37.206.131:7000/api/teapi/dy-biz/1404623815841026146/1412217234285269071
#åçæ­¦åæ­¥æ«ç åºå¥åºæ°æ®
xuanwu.sendScanInfo.url = http://121.37.206.131:7000/api/teapi/dy-biz/1399909626723569761/1567070368311677027

#é¦åæ´»å¨æ¥æ å¹´-æ-æ¥ æ¶:å:ç§
first_order_date = 2022-10-19 00:00:00

#å½å°ç»ç«¯çæç¼ç åç¼
gt.terminal.code = 321
#å½å°å¢è´­å®¢æ·çæç¼ç åç¼
gt.team.shopping.code = 322
#å½å°åéåçæç¼ç åç¼
gt.distributor.code = 323
#å½å°åä¼äººçæç¼ç åç¼
gt.partnership.code = 324
#å½æ æ¾çæ è¯
gt.type = gbs

#å½å°åéæ§çåå© appId
send_red_packet_appId = wxd736d44be5751250
