package com.intelliquor.cloud.shop.management;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.dao.DealerInfoCommonDao;
import com.intelliquor.cloud.shop.common.model.CloudDealerInfoModel;
import com.intelliquor.cloud.shop.common.model.CloudDealerOutBalance;
import com.intelliquor.cloud.shop.common.model.resp.CloudDealerOutBalanceResp;
import com.intelliquor.cloud.shop.common.model.resp.CodeInfoResp;
import com.intelliquor.cloud.shop.common.model.resp.PaymentRecordPCResp;
import com.intelliquor.cloud.shop.common.service.ICloudDealerOutBalanceService;
import com.intelliquor.cloud.shop.common.service.ITerminalCodeRuleService;
import com.intelliquor.cloud.shop.common.service.WebCodeCommonComponent;
import com.intelliquor.cloud.shop.common.utils.*;
import com.intelliquor.cloud.shop.system.dao.*;
import com.intelliquor.cloud.shop.system.model.*;
import com.intelliquor.cloud.shop.system.model.req.PaymentRecordPCReq;
import com.intelliquor.cloud.shop.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.jws.Oneway;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestMain {


    @Autowired
    private ShopCountDaysService shopCountDaysService;

    @Autowired
    private RewardScanCodeDetailDao rewardScanCodeDetailDao;

    @Autowired
    private DataCenterService dataCenterService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private YoushiPaymentRecordService youshiPaymentRecordService;

    @Autowired
    private YoushiPaymentRecordDao youshiPaymentRecordDao;

    @Autowired
    private YoushiPaymentLogsDao youshiPaymentLogsDao;

    @Autowired
    private DealerDao dealerDao;

    @Autowired
    private DealerConvertInfoDao dealerConvertInfoDao;

    @Autowired
    private DealerBillRecordService dealerBillRecordService;

    @Test
    public void synData() throws InterruptedException {

        PageHelper.startPage(1, 2000);
        List<ShopCountDaysModel> shopCountDaysModels = shopCountDaysService.queryShopRecord(null, null);

        TreeSet<Integer> list = new TreeSet<>();
        shopCountDaysModels.forEach(model -> list.add(model.getShopId()));
        System.err.println(JSONObject.toJSONString(list));

    }

    @Test
    public void test1 () {
        int codeType = 1;
        int num = 0;
        String qrcode = "6547284568719747";
        String codeIn = "6547284568719747";

        int companyId = 1;
        if (2 == codeType) {
            //如果是盒码，需要验证该盒码 和 该盒码所属于的箱码是否被扫过
            Map<String, Object> searchMap = new HashMap<>();
            searchMap.put("qrCode", qrcode);
            searchMap.put("comId", companyId);
            num = rewardScanCodeDetailDao.queryCount_V2(searchMap);
            if (num > 0) {
                System.out.println("盒码已使用");
            }
            Map<String, String> codeInfo = dataCenterService.getCodeInfo(qrcode, "2", companyId);
            if (null == codeInfo) {
                System.out.println("扫码异常");
            }
            String boxCode = codeInfo.get("codeBox");
            //检查箱码
            searchMap = new HashMap<>();
            searchMap.put("qrCode", boxCode);
            searchMap.put("comId", companyId);
            num = rewardScanCodeDetailDao.queryCount_V2(searchMap);
            if (num > 0) {
                System.out.println("盒码已使用");
            }
        } else if (1 == codeType) {

            //如果是箱码，需要验证该箱码 和 该箱码下面的所有的盒码是否被扫过
            Map<String, Object> searchMap = new HashMap<>();
            searchMap.put("qrCode", qrcode);
            searchMap.put("comId", companyId);
            num = rewardScanCodeDetailDao.queryCount_V2(searchMap);
            if (num > 0) {
                System.out.println("箱码已使用");
            }
            String[] codeIns = codeIn.split(",");
            //qrCode置空
            searchMap.put("qrCode", "");
            searchMap.put("codeInArray", codeIns);
            searchMap.put("comId", companyId);
            num = rewardScanCodeDetailDao.queryCount_V2(searchMap);
            if (num > 0) {
                System.out.println("箱码下的盒码已使用,请扫描盒码");
            }
        }
    }

    @Test
    public void test2 () {
        Map<String, Object> stringObjectMap = messageService.checkVerificationCode("18353684013", "149759");
        System.out.println(stringObjectMap);
    }


    @Test
    public void test3 () {

        String[] arr = new String[]{"YS16115394744153960", "YS16115404881566453", "YS16115418662248481", "YS16115588357353211", "YS16116201592804068", "YS16116253448622795", "YS16116275685217710"};

        for (String orderCode : arr) {
            ttt(orderCode);
        }


    }

    private void ttt (String orderCode) {
        YoushiPaymentLogsModel log = new YoushiPaymentLogsModel();
        log.setType(1);
        String content = "";
        YoushiPaymentRecordModel youshiPaymentRecordModel = youshiPaymentRecordDao.getByTransaction2(orderCode);
        if (ObjectUtil.isEmpty(youshiPaymentRecordModel)) {
            content = content + "  return：无此订单";
//            youshiPaymentLogsDao.insert(log);
        }

        YoushiPaymentRecordModel recordModel = new YoushiPaymentRecordModel();
        recordModel.setId(youshiPaymentRecordModel.getId());
        recordModel.setPayStatus(1);
        recordModel.setPayTime(new Date());
        youshiPaymentRecordDao.update(recordModel);

        //生成经销商账单交易记录
        //优市订单
        if (youshiPaymentRecordModel.getOrderType() == 1) {
            //获取经销商信息
            List<DealerModel> dealerList = dealerDao.lockByDealerName(youshiPaymentRecordModel.getCompanyName());
            DealerModel dealerModel = new DealerModel();
            if (ListUtils.notEmpty(dealerList)) {
                dealerModel = dealerList.get(0);
            }
            DealerBillRecordModel dealerBillRecordModel = new DealerBillRecordModel();
            dealerBillRecordModel.setShopId(youshiPaymentRecordModel.getShopId());
            dealerBillRecordModel.setTransaction("SR" + System.currentTimeMillis() + (int) ((Math.random() * 9 + 1) * 1000));
            dealerBillRecordModel.setOrderCode(orderCode);
            // dealerBillRecordModel.setDealerCode(dealerModel.getDealerCode());
            // dealerBillRecordModel.setDealerName(youshiPaymentRecordModel.getCompanyName());
            // 云店经销商名字更换云商经销商名字
            DealerConvertInfoModel convertInfoModel1=null;
            if(!StringUtils.isEmpty(youshiPaymentRecordModel.getCompanyName())){


                List<DealerConvertInfoModel> infoModelList=dealerConvertInfoDao.getByName(youshiPaymentRecordModel.getCompanyName());
                if(!CollectionUtils.isEmpty(infoModelList)){
                    convertInfoModel1=infoModelList.get(0);
                }
            }
            //("交易凭证:"+dealerBillRecordModel.getTransaction()+",云仓授信查询对应的关系:"+convertInfoModel1);

            if(convertInfoModel1==null){
                dealerBillRecordModel.setDealerCode(dealerModel.getDealerCode());
                dealerBillRecordModel.setDealerName(youshiPaymentRecordModel.getCompanyName());
                dealerBillRecordModel.setBalanceAmount(dealerModel.getLineOfCredit().add(youshiPaymentRecordModel.getPayAmount()));

            }else{
                dealerBillRecordModel.setDealerName(convertInfoModel1.getMerchantDealerName());
                dealerBillRecordModel.setDealerCode(convertInfoModel1.getMerchantDealerCode());
                //更新交易后的金额
                DealerModel model=null;
                if(!StringUtils.isEmpty(convertInfoModel1.getMerchantDealerCode())){
                    model= dealerDao.lock(convertInfoModel1.getMerchantDealerCode());
                }
                if(model==null){
                    System.out.println("查询不到经销商");
                }else{
                    dealerBillRecordModel.setBalanceAmount(model.getLineOfCredit().add(youshiPaymentRecordModel.getPayAmount()));
                }
            }

            //收支类型：1 收入   2支出
            dealerBillRecordModel.setPaymentType(1);
            //交易来源：1订单收入  2 异常调整  3提现  4开票
            dealerBillRecordModel.setTradeSources(1);
            //费用类型：1 云仓授信  2 云仓保证金  3 授信额度  4 产品返利
            dealerBillRecordModel.setCostType(3);
            dealerBillRecordModel.setAmount(youshiPaymentRecordModel.getPayAmount());
            //dealerBillRecordModel.setBalanceAmount(dealerModel.getLineOfCredit().add(youshiPaymentRecordModel.getPayAmount()));
//            dealerBillRecordModel.setCreateTime(new Date());
            dealerBillRecordModel.setCreateTime(youshiPaymentRecordModel.getCreateTime());
            dealerBillRecordModel.setRemark("订单：" + orderCode + "入账");
            dealerBillRecordModel.setCompanyId(youshiPaymentRecordModel.getCompanyId());
            dealerBillRecordModel.setIsDelete(0);
            //使用状态：是否已计入到余额，  1 是   0 否
            dealerBillRecordModel.setUseStatus(0);
            dealerBillRecordService.insert(dealerBillRecordModel);
            System.out.println("22222:inserr====" + JSON.toJSON(dealerBillRecordModel));
        }


        //发送短信给经销商start todo
//        ShopModel shopModel = shopDao.getById(youshiPaymentRecordModel.getShopId());
//        youshiPaymentDetailDao.getByRecordId(youshiPaymentRecordModel.getId());
        //发送短信给经销商end
        content = content + "  return：success";
        log.setContent(content);
        youshiPaymentLogsDao.insert(log);
        System.out.println("33333:insert::" + JSON.toJSON(log));
    }
    @Autowired
    private WebCodeComponent webCodeComponent;
    @Autowired
    private DealerInfoCommonDao dealerInfoCommonDao;

    @Autowired
    private ITerminalCodeRuleService terminalCodeRuleService;

    //开瓶奖励
    @Test
    public void test5 () {
        CodeInfoResp  codeInfo = webCodeComponent.dealCodeInfo("7wq4XP0wqxu");
        CloudDealerInfoModel dealerInfo = dealerInfoCommonDao.selectDealerIdByDealerCode("********");
        // 账号类型(1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端)
        Integer accountType = dealerInfo.getAccountType();
        Boolean isJudge = false;
        if(accountType == 3){
            isJudge = terminalCodeRuleService.getJudgeGrantReward(codeInfo, 3);
            log.info("【分销商开瓶奖励】黑名单码信息判断:扫的码:{},箱码:{},是否发放奖励:{}", codeInfo.getQrcode(), codeInfo.getCodeXiang(), isJudge ? "发放奖励" : "不发放奖励");
        }else if(accountType == 5){
            Integer isMember = 1;
            if(isMember == 0){
                isJudge = terminalCodeRuleService.getJudgeGrantReward(codeInfo, 5);
            }else if(isMember == 1 || isMember == 2){
                isJudge = terminalCodeRuleService.getJudgeGrantReward(codeInfo, 7);
            }else{
                log.info("【终端、会员开瓶奖励】不发放奖励扫的码:{},箱码:{},终端的IS_MEMEBER字段有误:{}", codeInfo.getQrcode(), codeInfo.getCodeXiang(), "");
            }
        }
        if(!isJudge){
            log.info("【开瓶奖励】不发放奖励扫的码:{},箱码:{}=={}", codeInfo.getQrcode(), codeInfo.getCodeXiang(), "1");
        }
    }

    @Autowired
    ConsumerScanDetailService consumerScanDetailService;
    @Test
    public void test6 () {
        consumerScanDetailService.dealConsumerReward(147121);
    }
    @Autowired
    private ICloudDealerOutBalanceService cloudDealerOutBalanceService;

    @Test
    public void test7 () {
        CloudDealerOutBalance model = new CloudDealerOutBalance();
        // 1114375602
        model.setToDealerCode("1114375602");
        model.setReceivingStatus(0);
       /* model.setTransaction("86894441448210841623");*/
        //20230829155244872531936  20230829155202189625187  89218335785170124815,89204227649333248016
        PageInfo<CloudDealerOutBalanceResp> listByPage = cloudDealerOutBalanceService.getListByPage(model, 3, 2);
        List<CloudDealerOutBalanceResp> list = listByPage.getList();
        if(list.size()>0){
            log.info(listByPage.getTotal()+"<><>"+JSONObject.toJSONString(list));
            String collect = list.stream().map(e -> e.getTransaction()).collect(Collectors.joining(","));
            log.info(JSONObject.toJSONString(collect));
        }
    }
    @Autowired
    private PaymentRecordService paymentRecordService;

    @Test
    public void test8 () {
        PaymentRecordPCReq paymentRecordPCReq = new PaymentRecordPCReq();
        paymentRecordPCReq.setPage(1);
        paymentRecordPCReq.setLimit(10);
        List<PaymentRecordPCResp> selectData = paymentRecordService.selectPaymentRecordPCList(paymentRecordPCReq);
        log.info("-->{}", selectData.size());
    }

    @Autowired
    private WebCodeCommonComponent webCodeCommonComponent;
    @Test
    public void test9(){
//        CodeInfoResp  codeInfo = webCodeComponent.dealCodeInfo("222104647623");
//        log.info("codeInfo:" + codeInfo);
        CodeInfoResp  codeInfoResp = webCodeCommonComponent.simplifyDealCodeInfo("122111715701","1343299998","1343299998","111102998");
        log.info("codeInfo:" + codeInfoResp.toString());

        // 222111455586
    }


}
