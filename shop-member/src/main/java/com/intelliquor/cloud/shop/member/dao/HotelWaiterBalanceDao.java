package com.intelliquor.cloud.shop.member.dao;


import com.intelliquor.cloud.shop.member.model.HotelWaiterBalanceModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
* public
* <AUTHOR> 2018-12-17
*/
@Component
public interface HotelWaiterBalanceDao {

    /**
    * 新增
    */
    public int insert(HotelWaiterBalanceModel model);

    /**
    * 删除
    */
    public int delete(@Param("id") int id);

    /**
    * 更新
    */
    public int update(@Param("hotelWaiterBalanceModel") HotelWaiterBalanceModel hotelWaiterBalanceModel);

    /**
    * Load查询
    */
    public HotelWaiterBalanceModel load(@Param("id") int id);

    /**
    * 分页查询Data
    */
	public List<HotelWaiterBalanceModel> pageList(@Param("offset") int offset,
                                                  @Param("pagesize") int pagesize);

    /**
    * 分页查询Count
    */
    public int pageListCount(@Param("offset") int offset,
                             @Param("pagesize") int pagesize);


    public List<HotelWaiterBalanceModel> getWaiterInfoById(Long waiterId);



    public List<HotelWaiterBalanceModel> selectByCapOutCode(@Param("capOutCode") String capOutCode);

    public Integer updateWaiterBalanceAmount(@Param("waiterId") Integer waiterId, @Param("amount") BigDecimal amount);

}
