package com.intelliquor.cloud.shop.member.model;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
*  终端酒店代金券流水操作Model
* <AUTHOR>  2018-12-12
*/
public class HotelCouponDetailsFlowModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
    * id
    */
    private Long id;

    /**
    * hotel_id
    */
    private Long hotelId;

    /**
    * coupon_code
    */
    private String couponCode;

    /**
    * coupon_amount
    */
    private BigDecimal couponAmount;

    /**
    * type 流水类型 生成-0、已领取-1、线上核销-2
    */
    private Integer type;

    @ApiModelProperty(value = "结算金额")
    private BigDecimal settleAmount;

    @ApiModelProperty(value = "核销比例")
    private BigDecimal verificatRatio;

    /**
    * operator_time
    */
    private Date operatorTime;

    /**
    * is_delete
    */
    private Integer isDelete;

    /**
    * coupon_details_id
    */
    private Long couponDetailsId;

    /**
    * cstmer_openid
    */
    private String cstmerOpenid;

    /**
    * cstmer_mobile
    */
    private String cstmerMobile;

    @ApiModelProperty(value = "结算人id")
    private Integer settleUserId;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHotelId() {
        return hotelId;
    }

    public void setHotelId(Long hotelId) {
        this.hotelId = hotelId;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getOperatorTime() {
        return operatorTime;
    }

    public void setOperatorTime(Date operatorTime) {
        this.operatorTime = operatorTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Long getCouponDetailsId() {
        return couponDetailsId;
    }

    public void setCouponDetailsId(Long couponDetailsId) {
        this.couponDetailsId = couponDetailsId;
    }

    public String getCstmerOpenid() {
        return cstmerOpenid;
    }

    public void setCstmerOpenid(String cstmerOpenid) {
        this.cstmerOpenid = cstmerOpenid;
    }

    public String getCstmerMobile() {
        return cstmerMobile;
    }

    public void setCstmerMobile(String cstmerMobile) {
        this.cstmerMobile = cstmerMobile;
    }

    public BigDecimal getSettleAmount() {
        return settleAmount;
    }

    public void setSettleAmount(BigDecimal settleAmount) {
        this.settleAmount = settleAmount;
    }

    public BigDecimal getVerificatRatio() {
        return verificatRatio;
    }

    public void setVerificatRatio(BigDecimal verificatRatio) {
        this.verificatRatio = verificatRatio;
    }

    public Integer getSettleUserId() {
        return settleUserId;
    }

    public void setSettleUserId(Integer settleUserId) {
        this.settleUserId = settleUserId;
    }
}