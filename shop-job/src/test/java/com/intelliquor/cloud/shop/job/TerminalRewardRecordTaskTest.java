package com.intelliquor.cloud.shop.job;

import com.intelliquor.cloud.shop.job.task.TerminalRewardRecordTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description：TODO
 * @Date：2023-04-26 10:31
 * @author：Panys
 * @version：1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TerminalRewardRecordTaskTest {

    @Autowired
    private TerminalRewardRecordTask terminalRewardRecordTask;

    @Test
    public void testSendMessageForScanCodeOrOpenBottle() {
        terminalRewardRecordTask.sendMessageForScanCodeOrOpenBottle();
    }

    @Test
    public void testSelectTodayTotalTerminalRewardRecord() {
        terminalRewardRecordTask.sendMessageForTodayRewardRecord();
    }

    @Test
    public void testUpdateIsSendMessageByShopIds() {
        terminalRewardRecordTask.updateIsSendMessageByShopIds();
    }
}
