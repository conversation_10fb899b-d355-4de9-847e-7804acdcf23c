package com.intelliquor.cloud.shop.job.task;

import com.intelliquor.cloud.shop.job.service.executor.PromotionManagerSyncCmdExe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 同步中台推广经理到我们系统
 * 每天凌晨1点执行
 * <AUTHOR>
 */
@Component
@Slf4j
public class SyncPromotionManagerTask {

    @Resource
    private PromotionManagerSyncCmdExe promotionManagerSyncCmdExe;

    @Scheduled(cron = "0 0 1 * * ?")
    public void syncPromotionManager() {
        log.info("同步中台推广经理到我们系统开始======");
        promotionManagerSyncCmdExe.execute();
        log.info("同步中台推广经理到我们系统结束======");
    }
}
