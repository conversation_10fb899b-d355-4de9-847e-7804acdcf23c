package com.intelliquor.cloud.shop.job.model.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.job.util.converter.DateConverter;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class TerminalVisitRecordAdminResp {

    /**
     * 主键id
     */
    @ExcelProperty(value = "序号",index = 0)
    private Integer id;

    /**
     * 终端Id
     */
    @ExcelIgnore
    private Integer shopId;

    /**
     * 终端名称
     */
    @ExcelProperty(value = "终端名称",index = 1)
    private String shopName;

    /**
     * 拜访人名称
     */
    @ExcelIgnore
    private String createUserName;
    /**
     * 拜访人手机号
     */
    @ExcelIgnore
    private String createUserPhone;


    @ExcelProperty(value = "拜访人",index = 2)
    private String createUserInfo;

    /**
     * 岗位Id
     */
    @ExcelIgnore
    private Integer postId;
    /**
     * 岗位名称
     */
    @ExcelProperty(value = "岗位名称",index = 3)
    private String postName;

    /**
     * 客户经理姓名
     */
    @ExcelIgnore
    private String accountManagerName;

    /**
     * 客户经理账号
     */
    @ExcelIgnore
    private String accountManagerPhone;

    /**
     * 客户经理信息
     */
    @ExcelProperty(value = "客户经理",index = 4)
    private String managerUserInfo;

    /**
     * 拜访时长（分），签退时间-签到时间
     */
    @ExcelProperty(value = "拜访时长",index = 5)
    private Integer duration;
    /**
     * 签到省
     */
    @ExcelIgnore
    private String signProvince;

    /**
     * 签到市
     */
    @ExcelIgnore
    private String signCity;

    /**
     * 签到区
     */
    @ExcelIgnore
    private String signDistinct;

    /**
     * 签到详细地址
     */
    @ExcelProperty(value = "进店地址",index = 6)
    private String signAddress;

    /**
     * 签到时间
     */
    @ExcelProperty(value = "进店时间",index = 7, converter = DateConverter.class)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 签退详情地址
     */
    @ExcelProperty(value = "离店地址",index = 8)
    private String signOutAddress;

    @ExcelProperty(value = "离店时间",index = 9, converter = DateConverter.class)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signOutTime;

    /**
     * 拜访总结
     */
    @ExcelProperty(value = "总结",index = 10)
    private String summary;

    /**
     * 签退省
     */
    @ExcelIgnore
    private String signOutProvince;

    /**
     * 签退市
     */
    @ExcelIgnore
    private String signOutCity;

    /**
     * 签退区
     */
    @ExcelIgnore
    private String signOutDistinct;

    /**
     * 业代编码
     */
    @ExcelProperty(value = "业代编码",index = 11)
    private String agentCode;

    /**
     * 审核人
     */
    @ExcelIgnore
    private String checkUser;
    /**
     * 审核意见
     */
    @ExcelIgnore
    private String checkRemark;
    /**
     * 拜访状态 0-未提交 1-已提交 2审核通过 3审核失败
     */
    private Integer status;

    /**
     * 审核时间
     */
    @ExcelIgnore
    private String checkTime;
    /**
     * 等级id
     */
    private String levelCode;
    /**
     * 等级名称
     */
    private String levelName;
}
