package com.intelliquor.cloud.shop.job.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端收货统计
 * @TableName t_receipt_goods_total
 */
@Data
@TableName(value ="t_receipt_goods_total")
public class ReceiptGoodsTotal implements Serializable {
    /**
     * 主键
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 负责人ID
     */
    @ExcelIgnore
    private Integer directorId;

    /**
     * 负责人名称
     */
    @ExcelProperty(value = "负责人名称",index = 0)
    private String directorName;

    /**
     * 负责人手机号
     */
    @ExcelProperty(value = "负责人手机号",index = 1)
    private String directorPhone;

    /**
     * 负责人角色
     */
    @ExcelIgnore
    private Integer directorPost;
    @TableField(exist = false)
    @ExcelProperty(value = "负责人角色",index = 2)
    private String directorPostName;

    /**
     * 采集人角色类型
     */
    @TableField(exist = false)
    private Integer directorPostType;
    /**
     * 采集人角色类型描述
     */
    @TableField(exist = false)
    private String directorPostTypeStr;

    /**
     * 客户经理ID
     */
    @ExcelIgnore
    private Integer managerId;

    /**
     * 客户经理名称
     */
    @ExcelIgnore
    private String managerName;

    /**
     * 客户经理电话
     */
    @ExcelIgnore
    private String managerPhone;
    @TableField(exist = false)
    @ExcelProperty(value = "上级客户经理",index = 3)
    private String managerNamePhone;

    /**
     * 国标
     */
    @ExcelProperty(value = "国标收货数量",index = 4)
    private Integer goodsGb;

    @ExcelProperty(value = "国标退货数量",index = 5)
    private Integer goodsGbRtn;

    /**
     * 酱酒
     */
    @ExcelProperty(value = "酱酒收货数量",index = 6)
    private Integer goodsJj;

    @ExcelProperty(value = "酱酒退货数量",index = 7)
    private Integer goodsJjRtn;

    /**
     * 高端酒
     */
    @ExcelProperty(value = "高端酒收货数量",index = 8)
    private Integer goodsGdj;
    @ExcelProperty(value = "高端酒退货数量",index = 9)
    private Integer goodsGdjRtn;

    /**
     * 十五年
     */
    @ExcelProperty(value = "十五年收货数量",index = 10)
    private Integer goodsSwn;
    @ExcelProperty(value = "十五年退货数量",index = 11)
    private Integer goodsSwnRtn;

    /**
     * 统计日期
     */
    @ExcelIgnore
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date totalDate;


    @TableField(exist = false)
    @ExcelProperty(value = "统计日期",index = 12)
    private String totalDateStr;

    /**
     * 年月
     */
    @ExcelIgnore
    private String monthYear;

    /**
     * 年
     */
    @ExcelIgnore
    private String totalYear;

    /**
     * 月
     */
    @ExcelIgnore
    private Integer totalMonth;

    /**
     * 执行时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date createTime;

    /**
     * 关联ID,t_terminal_scan_detail的id
     */
    @ExcelIgnore
    private String detailIds;

    /**
     * 是否删除
     */
    @ExcelIgnore
    private Integer isDelete;

    /**
     * 备注
     */
    @ExcelIgnore
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
