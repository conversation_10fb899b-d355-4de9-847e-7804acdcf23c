package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 业代任务每日会议完成详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Data
@Accessors(chain = true)
@TableName("t_terminal_task_meeting_complete_day")
@ApiModel(value = "TerminalTaskMeetingCompleteDay对象", description = "业代任务每日会议完成详情")
public class TerminalTaskMeetingCompleteDayModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("日期")
    private LocalDate day;

    @ApiModelProperty("t_terminal_task_package主键")
    private Integer tpId;

    @ApiModelProperty("t_terminal_meeting_clock_in主键")
    private Integer mcId;

    @ApiModelProperty("会议类型 1晨会 2周会 3月会 4其他会议")
    private Integer meetingType;

    @ApiModelProperty("签到时间")
    private LocalDateTime signInTime;

    @ApiModelProperty("签退时间")
    private LocalDateTime signOutTime;

    @ApiModelProperty("系数")
    private BigDecimal ratio;

    @ApiModelProperty("计算后的任务点数")
    private BigDecimal calculateNum;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("删除标志(0:未删除;1:已删除)")
    @TableLogic
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Integer createBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateBy;

    @ApiModelProperty("是否是执行iob拉取的数据0: 不是1: 是")
    private Integer isJob;

}
