package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 任务-月度任务详情-收获设置详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@TableName("t_terminal_task_monthly_detail")
@ApiModel(value = "TerminalTaskMonthlyDetailModel对象", description = "任务-月度任务详情-收获设置详情")
public class TerminalTaskMonthlyDetailModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("t_terminal_task_monthly主键")
    private Integer tmId;

    @ApiModelProperty("t_terminal_broker_task_ratio_config表主键")
    private Integer btrcId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品编码")
    private String goodsCode;

    @ApiModelProperty("系数")
    private BigDecimal ratio;

    @ApiModelProperty("商品任务数量")
    private BigDecimal goodsNum;

    @ApiModelProperty("删除标志(0:未删除;1:已删除)")
    @TableLogic
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
//    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private Integer createBy;

    @ApiModelProperty("更新时间")
//    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateBy;


}
