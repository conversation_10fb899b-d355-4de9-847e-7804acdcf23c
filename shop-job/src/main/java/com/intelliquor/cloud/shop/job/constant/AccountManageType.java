package com.intelliquor.cloud.shop.job.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum AccountManageType {
//    账户类型(0:客户经理,1创建的下级经销商人员,2创建的下级终端人员,3创建的下级采集人员,4创建的下级业务代表,5:经销商业代,6高端酒业代（团购市场推广）)
    ACCOUNT_MANAGER(0,"客户经理"),
    DEALER_PERSONNEL(1,"经销商人员"),
    TERMINAL_PERSONNEL(2,"终端人员"),
    TERMINAL_ACQUISITION(3,"采集人员"),
    BUSINESS_REPRESENTATIVE(4,"业务代表"),
    DEALER_AGENT(5,"经销商业代"),
    HIGH_AGENT(6,"高端酒业代");

    private Integer code;
    private String name;

    AccountManageType(Integer code, String msg) {
        this.code = code;
        this.name = msg;
    }

    public static String getName(Integer code){
        for (AccountManageType contract : AccountManageType.values()) {
            if (contract.getCode().equals(code)){
                return contract.getName();
            }
        }
        return "";
    }
}
