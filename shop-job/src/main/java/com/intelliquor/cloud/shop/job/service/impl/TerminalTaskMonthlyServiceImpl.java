package com.intelliquor.cloud.shop.job.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.job.dao.TerminalTaskMonthlyDao;
import com.intelliquor.cloud.shop.job.model.TerminalTaskMonthlyModel;
import com.intelliquor.cloud.shop.job.service.ITerminalTaskMonthlyService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 任务-月度任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
public class TerminalTaskMonthlyServiceImpl extends ServiceImpl<TerminalTaskMonthlyDao, TerminalTaskMonthlyModel> implements ITerminalTaskMonthlyService {

    private final TerminalTaskMonthlyDao terminalTaskMonthlyDao;

    public TerminalTaskMonthlyServiceImpl(TerminalTaskMonthlyDao terminalTaskMonthlyDao) {
        this.terminalTaskMonthlyDao = terminalTaskMonthlyDao;
    }

    @Override
    public List<TerminalTaskMonthlyModel> queryByTpIdAndMonth(int tpId, int month) {
        return terminalTaskMonthlyDao.selectList(Wrappers.lambdaQuery(TerminalTaskMonthlyModel.class)
                .eq(TerminalTaskMonthlyModel::getTpId, tpId)
                .eq(TerminalTaskMonthlyModel::getMonth, month));
    }
}
