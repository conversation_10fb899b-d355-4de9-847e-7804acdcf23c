package com.intelliquor.cloud.shop.job.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.page.PageMethod;
import com.intelliquor.cloud.shop.job.dao.CloudDealerInfoCheckBalanceDao;
import com.intelliquor.cloud.shop.job.model.CloudDealerInfoCheckBalanceModel;
import com.intelliquor.cloud.shop.job.model.resp.CloudDealerInfoCheckBalanceResp;
import com.intelliquor.cloud.shop.job.service.ICloudDealerInfoCheckBalanceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Service
public class CloudDealerInfoCheckBalanceServiceImpl extends ServiceImpl<CloudDealerInfoCheckBalanceDao, CloudDealerInfoCheckBalanceModel> implements ICloudDealerInfoCheckBalanceService {

    @Resource
    private CloudDealerInfoCheckBalanceDao cloudDealerInfoCheckBalanceDao;

    @Override
    public List<CloudDealerInfoCheckBalanceResp> getList(Long dealerId, Integer createUserId, Integer page, Integer limit) {
        PageMethod.startPage(page, limit, true, false, null);
        List<CloudDealerInfoCheckBalanceResp> list = cloudDealerInfoCheckBalanceDao.getListByDealerIdAndCreateUserId(dealerId, createUserId);
        return Optional.of(list).orElse(new ArrayList<>());
    }

}
