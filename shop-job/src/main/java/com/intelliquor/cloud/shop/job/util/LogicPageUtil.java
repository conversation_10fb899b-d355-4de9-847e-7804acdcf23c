package com.intelliquor.cloud.shop.job.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.PageUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 10/26/22
 */
public class LogicPageUtil {

    private LogicPageUtil() {
    }

    public static <T> PageInfo<T> logicPage(List<T> list, Integer pageNum, Integer pageSize) {
        PageInfo<T> pageResult = new PageInfo<>();
        if (CollectionUtils.isEmpty(list)) {
            setPageInfo(0, 0, pageResult, Lists.newArrayList(), 0, 0);
            return pageResult;
        }
        List<T> pageList = Lists.newArrayList();

        // 计算总页数
        int totalSize = list.size();
        int totalPage = PageUtil.totalPage(totalSize, pageSize);
        PageUtil.setFirstPageNo(1);
        // 分页，索引小于等于总页数，才返回列表.
        if (pageNum <= totalPage) {
            // 分页
            pageList = CollUtil.page(pageNum, pageSize, list);
        }
        // 返回结果
        setPageInfo(pageNum, pageSize, pageResult, pageList, totalSize, totalPage);
        return pageResult;
    }

    private static <T> void setPageInfo(Integer pageNum, Integer pageSize, PageInfo<T> pageResult, List<T> pageList, int totalSize, int totalPage) {
        // 当前页
        pageResult.setPageNum(pageNum);
        // 每页条数
        pageResult.setPageSize(pageSize);
        // 总记录数
        pageResult.setPages(totalPage);
        // 总页数
        pageResult.setTotal(totalSize);
        // 数据列表
        pageResult.setList(pageList);
    }
}
