package com.intelliquor.cloud.shop.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.job.model.TerminalSkuCheck;
import com.intelliquor.cloud.shop.job.model.req.TerminalSkuCheckReq;

import java.util.Map;

public interface ITerminalDisplayService extends IService<TerminalSkuCheck> {


    void approvalDisplay (TerminalSkuCheckReq req);

    Map<String, Object> getTerminalSkuCheckDetailById(Integer id);

    Map<String, Object> getTerminalSkuCheckDetailById2(Integer id);

}
