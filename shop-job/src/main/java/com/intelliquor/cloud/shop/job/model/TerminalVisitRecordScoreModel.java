package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业代-终端拜访得分表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-19
 */
@Getter
@Setter
@TableName("t_terminal_visit_record_score")
public class TerminalVisitRecordScoreModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * t_terminal_visit_record表主键
     */
    private Integer vrId;

    /**
     * 终端Id，t_terminal_shop表的member_shop_id字段 ，t_member_shop表的主键
     */
    private Integer shopId;

    /**
     * 签到省
     */
    private String signProvince;

    /**
     * 签到市
     */
    private String signCity;

    /**
     * 签到区
     */
    private String signDistinct;

    /**
     * 签到详细地址
     */
    private String signAddress;

    /**
     * 签到经度
     */
    private Double signLongitude;

    /**
     * 签到纬度
     */
    private Double signLatitude;

    /**
     * 现场拍照，多个逗号分割
     */
    private String signImg;

    /**
     * 店内陈列拍照，多个逗号分割
     */
    private String shopImg;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDelete;

    private Integer createUserId;

    /**
     * 创建人类型0-客户经理1-经销商人员2-终端人员3-采集人员4-业务代表
     */
    private Integer createUserType;

    /**
     * 签到时间 年月日时分秒
     */
    private LocalDateTime createTime;

    /**
     * 签到日期 年月日
     */
    private LocalDate createDate;

    private Integer companyId;

    /**
     * 签到门头照
     */
    private String signHeadImg;

    /**
     * 签退时间
     */
    private LocalDateTime signOutTime;

    /**
     * 签退省
     */
    private String signOutProvince;

    /**
     * 签退市
     */
    private String signOutCity;

    /**
     * 签退区
     */
    private String signOutDistinct;

    /**
     * 签退详情地址
     */
    private String signOutAddress;

    /**
     * 签退经度
     */
    private Double signOutLongitude;

    /**
     * 签退纬度
     */
    private Double signOutLatitude;

    /**
     * 签退门头照
     */
    private String signOutHeadImg;

    /**
     * 拜访总结
     */
    private String summary;

    /**
     * 拜访时长（分），签退时间-签到时间
     */
    private Integer duration;

    /**
     * 拜访状态 0-未提交 1-已提交 2审核通过 3审核失败
     */
    private Integer status;

    /**
     * 审核时间
     */
    private LocalDateTime checkTime;

    /**
     * 审核人
     */
    private Integer checkUser;

    /**
     * 审核意见
     */
    private String checkRemark;

    /**
     * 陈列检查拍照
     */
    private String displayImg;

    /**
     * 政策海报照片
     */
    private String policyPosterImg;

    /**
     * 政策台卡照片
     */
    private String policyCardImg;

    /**
     * 政策宣讲照片
     */
    private String policyExplainImg;

    /**
     * 对账单照片
     */
    private String verifyAccountImg;

    /**
     * 竞品对策内容
     */
    private String competePolicy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 客户经理Id
     */
    private Integer accountManagerId;

    /**
     * 同步标识(0:未同步;1:已同步;2:同步失败)
     */
    private Integer sysState;

    /**
     * 同步时间
     */
    private LocalDateTime sysDate;

    /**
     * 同步备注
     */
    private String sysRemark;

    /**
     * 现场视频
     */
    private String liveVideo;

    /**
     * 进店后经度
     */
    private Double inStoreLongitude;

    /**
     * 进店后纬度
     */
    private Double inStoreLatitude;

    /**
     * 系数
     */
    private BigDecimal ratio;

    /**
     * 计算后的任务点数
     */
    private BigDecimal calculateNum;

    /**
     * 是否有效 0无效 1有效
     */
    private Integer isValid;


}
