package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 终端店的合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Getter
@Setter
@TableName("t_terminal_shop_contract")
public class TerminalShopContractModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 合同类型(0:主品合同,1:酱酒合同)
     */
    private Integer contractType;

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 合同编码对应的名称-->名称在本地没有
     */
    private String contractCodeName;

    /**
     * 陈列协议照片
     */
    private String displayImage;

    /**
     * 包量协议照片
     */
    private String packageQuantityImage;

    /**
     * t_terminal_shop | 采集终端表的主键id
     * */
    private Integer terminalShopId;

    /**
     * t_member_shop | 终端表的主键id
     * */
    private Integer memberShopId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 包量的产品名称
     * */
    private String packageQuantityName;

    /**
     * 包量的陈列面数量
     * */
    private Integer packageQuantityDisplaySurface;

    /**
     * 包量的陈列费用
     * */
    private BigDecimal packageQuantityDisplayCost;

    /**
     * 包量的全年进货量
     * */
    private Integer packageQuantityReplenishStockQuantity;

    /**
     * 陈列的产品名称
     * */
    private String displayName;

    /**
     * 陈列的陈列面数量
     * */
    private Integer displayDisplaySurface;

    /**
     * 陈列的陈列费用
     * */
    private BigDecimal displayDisplayCost;

    /**
     * 陈列的每月进货量
     * */
    private Integer displayReplenishStockQuantity;

    /**
     * 陈列面(不少于)
     * */
    private Integer displaySurface;

    /**
     * 月进货数(箱)
     * */
    private Integer monthScanInNum;

    /**
     * 年进货数(箱)
     * */
    private Integer yearScanInNum;

    /**
     * 陈列奖励(分/月)
     * */
    private BigDecimal displayAmount;

    /**
     * 年度包量奖励(箱)
     * */
    private BigDecimal packageAmount;

    public Integer getDisplaySurface() {
        if(null == displaySurface){
            displaySurface = 0;
        }
        return displaySurface;
    }

    public Integer getMonthScanInNum() {
        if(null == monthScanInNum){
            monthScanInNum = 0;
        }
        return monthScanInNum;
    }

    public Integer getYearScanInNum() {
        if(null == yearScanInNum){
            yearScanInNum = 0;
        }
        return yearScanInNum;
    }

    public BigDecimal getDisplayAmount() {
        if(null == displayAmount){
            displayAmount = new BigDecimal(0);
        }
        return displayAmount;
    }

    public BigDecimal getPackageAmount() {
        if(null == packageAmount){
            packageAmount = new BigDecimal(0);
        }
        return packageAmount;
    }
}
