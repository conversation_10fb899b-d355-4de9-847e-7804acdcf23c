package com.intelliquor.cloud.shop.job.model.req;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.job.model.TerminalShopContractProductModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 终端采集信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Data
public class TerminalShopReq implements Serializable {

    private static final long serialVersionUID = -828261797531736550L;

    private Integer id;

    /**
     * 终端店铺名称
     */
    private String shopName;

    /**
     * 终端负责人姓名
     */
    private String leaderName;

    /**
     * 终端负责人电话
     */
    private String leaderPhone;

    /**
     * 标记 1-普通、2-多关注、3-重点关注
     */
    private Integer tag;

    /**
     * 终端门头照
     */
    private String headImg;

    /**
     * 备注
     */
    private String remark;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 终端类型 1-烟酒店、2-专卖店、3-餐饮店、4-商超
     */
    private Integer type;

    /**
     * 是否形象店 0- 否 1-是
     */
    private Integer isImage;

    /**
     * 形象店门头照
     */
    private String imageHeadPicture;

    /**
     * 终端店长姓名
     */
    private String keeperName;

    /**
     * 终端店长手机
     */
    private String keeperPhone;

    /**
     * 店铺面积
     */
    private String shopArea;

    /**
     * 有无营业执照
     */
    private Integer whetherLicense;

    /**
     * 营业执照照片
     */
    private String licenseImg;

    /**
     * 营业执照编号
     */
    private String licenseCode;

    /**
     * 激活状态 0未激活 1激活 2客户经理审核中 3客户经理审核失败 4 中台审核中 5中台审核失败
     */
    private Integer status;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 商户Id
     */
    private Integer companyId;

    /**
     * 联盟终端店的id
     */
    private Integer memberShopId;

    /**
     * 主编码
     */
    private String mainCode;

    /**
     * 副编码
     */
    private String deputyCode;

    /**
     * 主品合同或者酱酒合同
     */
    private TerminalShopContractReq primaryContract;

    /**
     * 新终端类型 0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5会员终端
     */
    private Integer shopType;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 食品经营许可证
     */
    private String foodBusinessLicense;

    /**
     * 是否是自营终端
     */
    private Integer whetherProprietaryTrading;

    /**
     * 收货仓库省
     */
    private String receivingWarehouseProvince;

    /**
     * 收货仓库市
     */
    private String receivingWarehouseCity;

    /**
     * 收货仓库区
     */
    private String receivingWarehouseDistrict;

    /**
     * 收货仓库地址
     */
    private String receivingWarehouseAddress;

    /**
     * 终端等级编码
     */
    private String levelCode;

    /**
     * 终端协议列表
     */
    private List<TerminalProtocolReq> protocolList;

    /**
     * 包量协议产品
     */
    private List<TerminalShopContractProductModel> packageQuantityAgreementProductList;

    /**
     * 陈列协议产品
     */
    private List<TerminalShopContractProductModel> displayAgreementProductList;

    /**
     * 陈列协议照片
     */
    private String displayImage;

    /**
     * 包量协议照片
     */
    private String packageQuantityImage;


    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人级别
     */
    private String contactLevel;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 收款方式 0:支付宝 1:微信 2:银行
     */
    private Integer receivingPaymentType;

    /**
     * 收款人名称
     */
    private String receivingPaymentName;

    /**
     * 收款人账户
     */
    private String receivingPaymentAccount;

    /**
     * 收款银行
     */
    private String receivingPaymentBank;

    /**
     * 收款人账户图片
     */
    private String receivingPaymentAccountPicture;

    /**
     * 审核人id
     */
    private Integer auditUserId;

    /**
     * 审核结果
     */
    private String auditResult;

    /**
     * 国台审核结果
     */
    private String gtAuditResult;

    /**
     * 终端协议 (0包量协议,1陈列协议 两个都有就是0,1)
     */
    private String shopAgreement;

    /**
     * 会员终端特殊字段 0是男 1是女
     */
    private Integer gender;

    /**
     * 会员终端特殊字段 生日
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 会员终端特殊字段 年龄
     */
    private String age;

    /**
     * 会员终端特殊字段 工作单位
     */
    private String workUnit;

    /**
     * 会员终端特殊字段 职务
     */
    private String position;

    /**
     * 会员终端特殊字段 个人爱好
     */
    private String personalPreference;

    /**
     * 会员终端特殊字段 经销商联系方式
     */
    private String dealerContactType;

    /**
     * 会员终端特殊字段 初次购买产品名称
     */
    private String firstBuyGoodsName;

    /**
     * 会员终端特殊字段 初次购买产品数量(箱)
     */
    private Integer firstBuyGoodsNumber;

    /**
     * 会员终端特殊字段 初次积分
     */
    private Integer firstScore;

    /**
     * 会员终端特殊字段 登记表图片
     */
    private String registrationFormPicture;

    /**
     * 包量的产品名称
     */
    private String packageQuantityName;

    /**
     * 包量的陈列面数量
     */
    private Integer packageQuantityDisplaySurface;

    /**
     * 包量的陈列费用
     */
    private BigDecimal packageQuantityDisplayCost;

    /**
     * 包量的全年进货量
     */
    private Integer packageQuantityReplenishStockQuantity;

    /**
     * 陈列的产品名称
     */
    private String displayName;

    /**
     * 陈列的陈列面数量
     */
    private Integer displayDisplaySurface;

    /**
     * 陈列的陈列费用
     */
    private BigDecimal displayDisplayCost;

    /**
     * 陈列的每月进货量
     */
    private Integer displayReplenishStockQuantity;

    /**
     * 陈列面(不少于)
     */
    private Integer displaySurface;

    /**
     * 月进货数(箱)
     */
    private Integer monthScanInNum;

    /**
     * 年进货数(箱)
     */
    private Integer yearScanInNum;

    /**
     * 陈列奖励(分/月)
     */
    private BigDecimal displayAmount;

    /**
     * 年度包量奖励(箱)
     */
    private BigDecimal packageAmount;

    /**
     * 网点建立理由 团购终端专属
     */
    private String networkPointEstablishReason;

    /***记录需要的编码对应名称的字段【BEGIN】****/
    //终端类型
    private String shopTypeName;
    //标记
    private String tagName;
    //收款方式
    private String receivingPaymentTypeName;
    //经销商
    private String dealerName;
    //经销商合同
    private String contractTypeName;
    //合同编码
    private String contractCodeName;
    //是否形象店
    private String isImageName;
    //有无营业执照
    private String whetherLicenseName;
    //是否是自营终端
    private String whetherProprietaryTradingName;
    //会员终端特殊字段 0是男 1是女
    private String genderName;
    /***记录需要的编码对应名称的字段【END】****/
}
