package com.intelliquor.cloud.shop.job.injector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.methods.AlwaysUpdateSomeColumnById;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;
import com.baomidou.mybatisplus.extension.injector.methods.LogicDeleteByIdWithFill;
import com.intelliquor.cloud.shop.common.basequery.CustomizedSqlInjector;
import com.intelliquor.cloud.shop.job.injector.method.SelectIgnoreLogicDelete;
import com.intelliquor.cloud.shop.job.injector.method.SelectIgnoreLogicDeleteByMap;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/4/20 09:42
 */
public class MySqlInjector extends CustomizedSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        // TODO Auto-generated method stub
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        // 添加InsertBatchSomeColumn方法
        methodList.add(new InsertBatchSomeColumn().setPredicate(t -> !t.isLogicDelete() || !"reversion".equals(t.getProperty())));
        methodList.add(new LogicDeleteByIdWithFill());
        methodList.add(new AlwaysUpdateSomeColumnById());
        methodList.add(new SelectIgnoreLogicDeleteByMap());
        methodList.add(new SelectIgnoreLogicDelete());
        return methodList;
    }
}
