package com.intelliquor.cloud.shop.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.DisplayResultModel;
import com.intelliquor.cloud.shop.job.model.DisplayResult;

/**
* <AUTHOR>
* @description 针对表【t_display_result(陈列统计)】的数据库操作Service
* @createDate 2023-03-16 14:49:50
*/
public interface IDisplayResultService extends IService<DisplayResult> {

    /**
     *  @author: HLQ
     *  @Date: 2023/3/16 15:01
     *  @Description:
     */
    void totalDisplayResultTaskNew() throws Exception;


    void asyncSendBanquetReward(DisplayResultModel toSend);
}
