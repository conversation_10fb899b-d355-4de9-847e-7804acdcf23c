package com.intelliquor.cloud.shop.job.alioss;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Data
@Component
public class AliOssPolicy implements Serializable {
    /**
     * 上传认证id
     */
    private String accessid;
    /**
     * 直传地址
     */
    private String host;
    /**
     * policy
     */
    private String policy;
    /**
     * 签名
     */
    private String signature;
    /**
     * 上传截止时间
     */
    private String expire;
    /**
     * 回调配置
     */
    private String callback;
    /**
     * 直传文件的开头（路径）
     */
    private String dir;

}
