package com.intelliquor.cloud.shop.job.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/3/19
 * @description 对接国台，生成签名
 */
@Slf4j
public class SignUtils {
    private final static String CHARSET_UTF8 = "utf8";
    private final static String ALGORITHM = "UTF-8";
    private final static String SEPARATOR = "&";

    /**
     * 调用此方法签名
     * @param parameter 参与签名的字段
     * @param productSecrete 平台提供的跟productKey对应的productSecrete
     * @return
     * @throws Exception
     */
    public static String generate(Map<String, String> parameter,
                                  String productSecrete) throws Exception {
        String signString = generateQueryString(parameter);
        byte[] signBytes = hmacSHA1Signature(productSecrete + SEPARATOR, signString);
        String signature = newStringByBase64(signBytes);
        return signature;

    }

    /**
     * 对各字段按字典排序，拼接
     * @param params
     * @return
     */
    public static String generateQueryString(Map<String, String> params) throws UnsupportedEncodingException {
        TreeMap<String, String> sortParameter = new TreeMap<String, String>();
        sortParameter.putAll(params);
        StringBuilder canonicalizedQueryString = new StringBuilder();
        for (Map.Entry<String, String> entry : sortParameter.entrySet()) {
            if("sign".equals(entry.getKey())){
                continue;
            }
            canonicalizedQueryString.append(percentEncode(entry.getKey())).append("=")
                    .append(percentEncode(entry.getValue())).append(SEPARATOR);
        }
        if (canonicalizedQueryString.length() > 1) {
            canonicalizedQueryString.setLength(canonicalizedQueryString.length() - 1);
        }
        return canonicalizedQueryString.toString();
    }

    /**
     * 计算HMAC值
     * @param secret
     * @param baseString
     * @return
     * @throws Exception
     */
    public static byte[] hmacSHA1Signature(String secret, String baseString)
            throws Exception {
        if (StringUtils.isEmpty(secret)) {
            throw new IOException("secret can not be empty");
        }
        if (StringUtils.isEmpty(baseString)) {
            return null;
        }
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(CHARSET_UTF8), ALGORITHM);
        mac.init(keySpec);
        return mac.doFinal(baseString.getBytes(CHARSET_UTF8));
    }

    /**
     * 计算签名值
     * @param bytes
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String newStringByBase64(byte[] bytes)
            throws UnsupportedEncodingException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        return new String(Base64.getEncoder().encode(bytes), CHARSET_UTF8);
    }

    public static String percentEncode(String value) throws UnsupportedEncodingException {
        return value != null ? URLEncoder.encode(value, CHARSET_UTF8).replace("+", "%20")
                .replace("*", "%2A").replace("%7E", "~") : null;
    }

    /**
     * 功能描述: 生成请求对象参数
     * @param accessKey
     * @param nonce
     * @param timestamp
     * @param version
     * @param signMethod`
     * @auther: tms
     * @date: 2021/01/19 14:02
     * @return java.util.Map<java.lang.String,java.lang.String>
    */
    public static Map<String, String> groupSignParams(String accessKey, String nonce, String timestamp,
                                               String version, String signMethod) {
        Map<String, String> postParams = new HashMap<>(8);
        postParams.put("accessKey", accessKey);
        postParams.put("nonce", nonce);
        // "2020-05-06T07:43:53.748Z"
        postParams.put("timestamp", timestamp);
        postParams.put("signMethod", signMethod);
        postParams.put("version", version);
        return postParams;
    }

    /**
     * 功能描述: 生成时间戳的方法
     * @auther: tms
     * @date: 2021/01/19 13:51
     * @return java.lang.String
    */
    public static String getUtcTime() {
        Calendar cal = Calendar.getInstance();
        // 2、取得时间偏移量：
        int zoneOffset = cal.get(Calendar.ZONE_OFFSET);
        // 3、取得夏令时差：
        int dstOffset = cal.get(Calendar.DST_OFFSET);
        // 4、从本地时间里扣除这些差量，即可以取得UTC时间：
        cal.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        return simpleDateFormat.format(cal.getTime());
    }

    /**
     * 功能描述: 组装请求头参数
     * @param accessKey
     * @param secret
     * @auther: tms
     * @date: 2021/01/19 15:55
     * @return void
    */
    public static Map<String, String> assembleReqHeader (String accessKey, String secret) throws Exception {
        // 随机数
        String nonce = String.valueOf((int)(Math.random() * 1000));
        String signMethod = "HMAC-SHA1";
        Map<String, String> headMap = groupSignParams(accessKey, nonce, getUtcTime(), "1.0.0", signMethod);
        String signStr = generate(headMap, secret);
        headMap.put("sign", signStr);
        return headMap;
    }

    /**
     * MD5方法
     *
     * @param text 明文
     * @param key  密钥
     * @return 密文
     * @throws Exception
     */
    public static String md5(String text, String key) {
        //加密后的字符串
        String encodeStr = DigestUtils.md5Hex(text + key);
        System.out.println("MD5加密后的字符串为:encodeStr=" + encodeStr);
        return encodeStr;
    }
}
