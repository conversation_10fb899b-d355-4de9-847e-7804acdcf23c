package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 账户代表变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Getter
@Setter
@TableName("t_terminal_account_manager_change_record")
public class TerminalAccountManagerChangeRecordModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 变更前手机号
     */
    private String beforeChangePhone;

    /**
     * 变更前姓名
     */
    private String beforeChangeName;

    /**
     * 变更前客户经理姓名
     */
    private String beforeChangeParentName;

    /**
     * 变更前客户经理手机号
     */
    private String beforeChangeParentPhone;

    /**
     * 变更后手机号
     */
    private String afterChangePhone;

    /**
     * 变更后姓名
     */
    private String afterChangeName;

    /**
     * 变更后客户经理姓名
     */
    private String afterChangeParentName;

    /**
     * 变更后客户经理手机号
     */
    private String afterChangeParentPhone;

    /**
     * 删除状态 0未删除 1已删除
     */
    private Integer isDelete;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
