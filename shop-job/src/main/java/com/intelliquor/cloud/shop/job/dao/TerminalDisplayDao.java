package com.intelliquor.cloud.shop.job.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.job.model.TerminalSkuCheck;
import com.intelliquor.cloud.shop.job.model.req.TerminalSkuCheckReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 终端采集信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Repository
public interface TerminalDisplayDao extends BaseMapper<TerminalSkuCheck> {


    /**
     *  @author: HLQ
     *  @Date: 2023/3/23 14:13
     *  @Description:获取时间范围参与陈列拍照的终端列表
     */
    List<Integer> selectTerminalShopIdList(@Param("startDate")String startDate,@Param("endDate")String endDate, @Param("monthYear")String monthYear );





    Integer selectAiSkuCountByCheckId(@Param("id") Integer id);



    void approvalDisplay2(@Param("req") TerminalSkuCheckReq req);

}
