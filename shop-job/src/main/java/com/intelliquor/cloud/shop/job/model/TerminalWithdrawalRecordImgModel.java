package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端积分提现凭证图片存放表实体类
 * @Date：2023-05-11 16:31
 * @author：Panys
 * @version：1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_terminal_withdrawal_record_img")
public class TerminalWithdrawalRecordImgModel implements Serializable {

    private static final long serialVersionUID = 117554392343313L;

    /**
     * 自增主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * t_terminal_withdrawal_record表的主键ID
     */
    private Integer withdrawalRecordId;

    /**
     * 打款凭证图片
     */
    private String voucherImg;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 是否删除 (0 :否 1 :是)
     */
    private Integer isDelete;
}
