<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.job.dao.TerminalVisitRecordDataDao">

    <sql id="baseColumn">
        id,
        visit_id,
        type,
        goods_code,
        goods_name,
        stock,
        scan_num,
        plan_date,
        create_time,
        is_delete,
        remark
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.job.model.TerminalVisitRecordDataModel">
            <id property="id" column="id"/>
            <result property="visitId" column="visit_id"/>
            <result property="type" column="type"/>
            <result property="goodsCode" column="goods_code"/>
            <result property="goodsName" column="goods_name"/>
            <result property="stock" column="stock"/>
            <result property="scanNum" column="scan_num"/>
            <result property="planDate" column="plan_date"/>
            <result property="createTime" column="create_time"/>
            <result property="isDelete" column="is_delete"/>
            <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            is_delete = 0
            <if test="visitId != null">
                and visit_id = #{visitId}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_terminal_visit_record_data
        <include refid="selectiveWhere"/>
        ORDER BY id desc
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.job.model.TerminalVisitRecordDataModel">
        INSERT INTO t_terminal_visit_record_data
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="visitId != null ">
            visit_id,
            </if>
            <if test="type != null ">
            type,
            </if>
            <if test="goodsCode != null and goodsCode != ''">
            goods_code,
            </if>
            <if test="goodsName != null and goodsName != ''">
            goods_name,
            </if>
            <if test="stock != null and stock != ''">
            stock,
            </if>
            <if test="scanNum != null and scanNum != ''">
            scan_num,
            </if>
            <if test="planDate != null and planDate != ''">
            plan_date,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="isDelete != null ">
            is_delete,
            </if>
            <if test="remark != null and remark != ''">
            remark
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="visitId != null ">
            #{visitId},
            </if>
            <if test="type != null ">
            #{type},
            </if>
            <if test="goodsCode != null and goodsCode != ''">
            #{goodsCode},
            </if>
            <if test="goodsName != null and goodsName != ''">
            #{goodsName},
            </if>
            <if test="stock != null and stock != ''">
            #{stock},
            </if>
            <if test="scanNum != null and scanNum != ''">
            #{scanNum},
            </if>
            <if test="planDate != null and planDate != ''">
            #{planDate},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="isDelete != null ">
            #{isDelete},
            </if>
            <if test="remark != null and remark != ''">
            #{remark}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.job.model.TerminalVisitRecordDataModel">
        UPDATE t_terminal_visit_record_data
        <set>
            <if test="visitId != null ">
            visit_id = #{visitId},
            </if>
            <if test="type != null ">
            type = #{type},
            </if>
            <if test="goodsCode != null and goodsCode != ''">
            goods_code = #{goodsCode},
            </if>
            <if test="goodsName != null and goodsName != ''">
            goods_name = #{goodsName},
            </if>
            <if test="stock != null and stock != ''">
            stock = #{stock},
            </if>
            <if test="scanNum != null and scanNum != ''">
            scan_num = #{scanNum},
            </if>
            <if test="planDate != null and planDate != ''">
            plan_date = #{planDate},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
            <if test="isDelete != null ">
            is_delete = #{isDelete},
            </if>
            <if test="remark != null and remark != ''">
            remark = #{remark},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_terminal_visit_record_data
        WHERE id = #{id}
    </delete>


    <delete id="deleteByVisitIdAndType">
        DELETE FROM t_terminal_visit_record_data
        WHERE visit_id = #{visitId} AND type = #{type}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_terminal_visit_record_data
        WHERE id = #{id}
    </select>

    <insert id="batchInsert">
        insert into t_terminal_visit_record_data
        (visit_id,type,
        goods_code,
        goods_name,
        stock,
        scan_num,
        plan_date
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{visitId},#{item.type},#{item.goodsCode},#{item.goodsName},#{item.stock},#{item.scanNum},#{item.planDate}
            )
        </foreach>
    </insert>
</mapper>
