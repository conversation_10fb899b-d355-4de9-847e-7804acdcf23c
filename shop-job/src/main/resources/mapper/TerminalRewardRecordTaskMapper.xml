<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.job.dao.TerminalRewardRecordTaskDao">
    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.job.model.resp.TerminalRewardRecordTaskResp">
        <id property="shopId" column="shop_id"/>
        <result property="amount" column="amount"/>
        <result property="source" column="source"/>
        <result property="createTime" column="create_time"/>
        <result property="isSendMessage" column="is_send_message"/>
        <result property="linkphone" column="linkphone"/>
        <result property="virtualAmount" column="virtual_amount"/>
        <result property="isMember" column="is_member"/>
    </resultMap>
    <select id="selectCurrentTerminalRewardRecord" resultMap="baseResultMap">
        SELECT
            a.shop_id,a.amount,a.source,a.create_time,a.is_send_message ,
            m.linkphone, a.after_change_virtual_amount as virtual_amount, m.is_member
        FROM
            t_terminal_reward_record a
        LEFT JOIN t_member_shop m ON a.shop_id = m.id
        inner join
            (
                SELECT
                shop_id,MIN( b.create_time ) AS create_time
                FROM
                t_terminal_reward_record b
                WHERE
                b.create_time > #{startTime} and b.create_time <![CDATA[<]]> #{endTime}
                AND
                (b.source = 1 OR b.source = 2)
                GROUP BY
                b.shop_id) tmp on tmp.shop_id = a.shop_id and tmp.create_time = a.create_time
        WHERE (a.is_send_message != 0 OR ISNULL( a.is_send_message ))
    </select>
    <select id="selectTodayTotalTerminalRewardRecord" resultMap="baseResultMap">
        SELECT
            b.shop_id, b.amount,m.linkphone,m.virtual_amount,m.is_member
        FROM
	        (SELECT a.shop_id AS shop_id, SUM(a.amount) AS amount
	        FROM t_terminal_reward_record a
	        WHERE
	            a.create_time > #{startTime} AND a.create_time <![CDATA[<]]> #{endTime}
	            AND
	            (a.source = 1 OR a.source = 2)
	        GROUP BY a.shop_id) b
	    LEFT JOIN t_member_shop m ON b.shop_id = m.id
    </select>
</mapper>
