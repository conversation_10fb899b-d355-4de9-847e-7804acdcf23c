package com.intelliquor.cloud.shop.wechat.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.intelliquor.cloud.shop.common.config.WxOpenServiceConfig;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.api.WxOpenMaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020.09.29
 */
@Slf4j
@Service
public class MaUserService {

    @Autowired
    protected WxOpenServiceConfig wxOpenService;

    public String getOpenId(String wechatCode, String appId) {
        if (null == appId || "".equals(appId) || "undefined".equals(appId)) {
            throw new BusinessException("缺少appId");
        }
       /* PayInfoModel payInfo = payInfoService.getByAppId(appId);
        if (null == payInfo) {
            return Response.fail("小程序配置错误,请联系管理员");
        }*/

        // String sessionKey = shopUserService.getSessionKey(wechatCode, appId, payInfo.getSecret());
        String openId=null;
        try {
            log.info("===="+wxOpenService.getWxOpenConfigStorage().getComponentVerifyTicket());
            WxOpenMaService wxOpenMaService= wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appId);
            WxMaJscode2SessionResult session = null;
            session = wxOpenMaService.getUserService().getSessionInfo(wechatCode);
            if (null == session) {
                return null;
            }
            openId=session.getOpenid();
        } catch (WxErrorException e) {
            e.printStackTrace();
        }


        return openId;
    }
}
