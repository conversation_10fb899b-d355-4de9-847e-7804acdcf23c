package com.intelliquor.cloud.shop.wechat.service;

import com.intelliquor.cloud.shop.wechat.dao.WechatPackageInfoDao;
import com.intelliquor.cloud.shop.wechat.model.WechatPackageApplyforRecordModel;
import com.intelliquor.cloud.shop.wechat.model.WechatPackageInfoModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* 描述：包量套餐 服务实现层
* <AUTHOR>
* @date 2019/07/2
*/
@Service
public class WechatPackageInfoService{

    @Autowired
    private WechatPackageInfoDao wechatPackageInfoDao;


    /**
     * 查询数据
     *
     * @return
     */
    public List<WechatPackageInfoModel> selectListByPage(Map<String, Object> searchMap) {
        return wechatPackageInfoDao.selectListByPage(searchMap);
    }

    /**
    * 新增数据
    *
    * @param model
    */
    public void insert(WechatPackageInfoModel model) {
        wechatPackageInfoDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    public void update(WechatPackageInfoModel model) {
        wechatPackageInfoDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    public void delete(Integer id) {
        wechatPackageInfoDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    public WechatPackageInfoModel getById(Integer id) {
        return wechatPackageInfoDao.getById(id);
    }
}