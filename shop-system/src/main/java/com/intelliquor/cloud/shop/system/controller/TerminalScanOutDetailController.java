package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.TerminalScanOutDetailModel;
import com.intelliquor.cloud.shop.system.model.resp.TerminalScanOutDetailResp;
import com.intelliquor.cloud.shop.system.service.TerminalScanOutDetailService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 描述：终端扫码出库明细表控制层
 *
 * <AUTHOR>
 * @date 2022-09-15
 */
@RestController
@RequestMapping("/terminalScanOutDetail")
public class TerminalScanOutDetailController {

    @Autowired
    private TerminalScanOutDetailService terminalScanOutDetailService;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<TerminalScanOutDetailModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                        @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                        TerminalScanOutDetailModel model) {

        PageHelper.startPage(page, limit);
        List<TerminalScanOutDetailModel> list = terminalScanOutDetailService.selectList(SearchUtil.getSearch(model));
        PageInfo<TerminalScanOutDetailModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<TerminalScanOutDetailModel>> getList(TerminalScanOutDetailModel model) {
        List<TerminalScanOutDetailModel> list = terminalScanOutDetailService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(TerminalScanOutDetailModel model) {
        terminalScanOutDetailService.insert(model);
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息", httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(TerminalScanOutDetailModel model) {
        terminalScanOutDetailService.update(model);
        return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息", httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
        terminalScanOutDetailService.delete(id);
        return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息", httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<TerminalScanOutDetailModel> getById(@RequestParam(value = "id") Integer id) {
        TerminalScanOutDetailModel model = terminalScanOutDetailService.getById(id);
        return Response.ok(model);
    }

    /**
     * 根据批次id查询批次下的扫码详情
     * @param page
     * @param limit
     * @return
     */
    @RequestMapping(value = "/getListByBalanceId")
    public PageResponse<List<TerminalScanOutDetailResp>> getListByBalanceId(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                            @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                            @RequestParam(value = "balanceId") Integer balanceId) {

        PageHelper.startPage(page, limit);
        List<TerminalScanOutDetailResp> list = terminalScanOutDetailService.getListByBalanceId(balanceId);
        PageInfo<TerminalScanOutDetailResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

}