package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.MiddleRewardScanCodeDetailRelationModel;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
import java.util.List;
/**
* 描述：终端店扫码记录箱码盒码关联表 Dao接口
* <AUTHOR>
* @date 2020-08-20
*/
public interface MiddleRewardScanCodeDetailRelationDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<MiddleRewardScanCodeDetailRelationModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(MiddleRewardScanCodeDetailRelationModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(MiddleRewardScanCodeDetailRelationModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    MiddleRewardScanCodeDetailRelationModel getById(Integer id);

    /**
     * 查询关联扫码记录最后一条记录的ID
     *
     * @return
     */
    Integer getMaxDetailId(@Param("companyId") Integer companyId);

}