package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.FileItem;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.enums.OrderTypeEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.ExcelTools;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import com.intelliquor.cloud.shop.system.model.PreSaleShopCardModel;
import com.intelliquor.cloud.shop.system.model.req.LadderGrouponOrderPageReq;
import com.intelliquor.cloud.shop.system.model.req.PreSaleShopCardExpReq;
import com.intelliquor.cloud.shop.system.model.req.PreSaleShopInfoReq;
import com.intelliquor.cloud.shop.system.model.resp.PreSaleShopInfoResp;
import com.intelliquor.cloud.shop.system.model.resp.PscRechargeOrderResp;
import com.intelliquor.cloud.shop.system.service.OrderDownloadCenterService;
import com.intelliquor.cloud.shop.system.service.PreSaleShopCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * 描述：终端购买预售卡订单表控制层
 *
 * <AUTHOR>
 * @date 2020-10-26
 */
@Api(tags = {"终端购买预售卡订单表操作接口"}, description = "终端购买预售卡订单表操作接口")
@RestController
@RequestMapping("/preSaleShopCard")
public class PreSaleShopCardController {

    @Autowired
    private PreSaleShopCardService preSaleShopCardService;

    @Autowired
    UserContext userContext;

    @Autowired
    private OrderDownloadCenterService downloadCenterService;

    @ApiOperation(value = "查询分页信息", notes = "查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @GetMapping(value = "/getListByPage")
    public PageResponse<List<PreSaleShopCardModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                  @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                  PreSaleShopCardModel model) {

        PageHelper.startPage(page, limit);
        List<PreSaleShopCardModel> list = preSaleShopCardService.selectList(SearchUtil.getSearch(model));
        PageInfo<PreSaleShopCardModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @GetMapping(value = "/getList")
    public Response<List<PreSaleShopCardModel>> getList(PreSaleShopCardModel model) {
        List<PreSaleShopCardModel> list = preSaleShopCardService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @GetMapping(value = "/getShopSaleList")
    public PageResponse<List<PreSaleShopInfoResp>> getShopSaleList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                   @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                   PreSaleShopInfoReq model) {

        PageHelper.startPage(page, limit);
        List<PreSaleShopInfoResp> list = preSaleShopCardService.getShopSaleList(SearchUtil.getSearch(model));
        PageInfo<PreSaleShopInfoResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @GetMapping(value = "/getShopCardList")
    public PageResponse<List<PreSaleShopCardModel>> getShopCardList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                    @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                    PreSaleShopInfoReq model) {

        PageHelper.startPage(page, limit);
        List<PreSaleShopCardModel> list = preSaleShopCardService.selectCardList(SearchUtil.getSearch(model));
        PageInfo<PreSaleShopCardModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    /**
     * 导出终端店铺预售卡额度数据
     */
    @ApiOperation(value = "导出终端店铺预售卡额度数据", notes = "导出", httpMethod = "GET")
    @RequestMapping(value = "/exportSaleShopData")
    public Response<String> exportShopData(@RequestBody PreSaleShopCardExpReq model) throws IOException {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);
        Map<String, Object> searchMap = SearchUtil.getSearch(model);
        if (searchMap == null) {
            throw new BusinessException("缺少参数");
        }

        terminalExportShopSaleTask(searchMap, model, OrderTypeEnum.YUSHOUKASHOPSALE.getValue());

        return Response.ok("导出成功！");

    }

    /**
     * 导出终端店铺预售卡数据
     */
    @ApiOperation(value = "导出终端店铺预售卡列表数据", notes = "导出", httpMethod = "GET")
    @RequestMapping(value = "/exportSaleShopCardData")
    public Response<String> exportSaleShopCardData(@RequestBody PreSaleShopCardExpReq model) throws IOException {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);
        Map<String, Object> searchMap = SearchUtil.getSearch(model);
        if (model.getCompanyId() == null) {
            throw new BusinessException("缺少参数");
        }

        terminalExportShopCardTask(searchMap, model, OrderTypeEnum.YUSHOUKASHOPCARD.getValue());

        return Response.ok("导出成功！");

    }


    /**
     * 终端导出
     *
     * @param searchMap
     * @throws IOException
     */
    @Async
    public void terminalExportShopSaleTask(Map<String, Object> searchMap, PreSaleShopCardExpReq exportReq, Integer type) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addDownloadCenter(exportReq, type);

        // 2、上传文件
        String fileUrl = this.exportShopSale(searchMap);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 终端导出
     *
     * @param searchMap
     * @throws IOException
     */
    @Async
    public void terminalExportShopCardTask(Map<String, Object> searchMap, PreSaleShopCardExpReq exportReq, Integer type) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addDownloadCenter(exportReq, type);

        // 2、上传文件
        String fileUrl = this.exportShopCard(searchMap);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param searchMap
     * @return
     * @throws IOException
     */
    public String exportShopSale(Map<String, Object> searchMap) throws IOException {
        List<PreSaleShopInfoResp> exportRespList = preSaleShopCardService.getShopSaleList(searchMap);

        FileItem fileItem = ExcelTools.exportByFile(exportRespList, 1);
        // String fileName="扫码终端店铺统计列表.xls";
        String fileName = (new StringBuffer()).append("预售卡终端额度统计列表").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }


    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param searchMap
     * @return
     * @throws IOException
     */
    public String exportShopCard(Map<String, Object> searchMap) throws IOException {
        List<PreSaleShopCardModel> exportRespList = preSaleShopCardService.selectCardList(searchMap);

        FileItem fileItem = ExcelTools.exportByFile(exportRespList, 1);
        // String fileName="扫码终端店铺统计列表.xls";
        String fileName = (new StringBuffer()).append("预售卡终端统计列表").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    /**
     * 添加下载中心数据
     *
     * @return
     */
    public OrderDownloadCenterModel addDownloadCenter(PreSaleShopCardExpReq request, Integer type) {
        OrderDownloadCenterModel downloadCenterModel = request.getDownloadCenterModel();

        downloadCenterModel.setType(type.shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(PreSaleShopCardModel model) {
        preSaleShopCardService.insert(model);
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息", httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(PreSaleShopCardModel model) {
        preSaleShopCardService.update(model);
        return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息", httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
        preSaleShopCardService.delete(id);
        return Response.ok("删除成功");
    }


    // 导出接口


    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息", httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<PreSaleShopCardModel> getById(@RequestParam(value = "id") Integer id) {
        PreSaleShopCardModel model = preSaleShopCardService.getById(id);
        return Response.ok(model);
    }


    @ApiOperation(value = "根据ID查询店铺预售卡信息", notes = "根据ID查询信息", httpMethod = "GET")
    @GetMapping(value = "/getByShopId")
    public Response<Map<String, Object>> getByShopId(@RequestParam(value = "shopId") Integer shopId) {
        Map<String, Object> map = preSaleShopCardService.getByShopId(shopId);
        return Response.ok(map);
    }

    /***
     * <TODO 预售卡充值订单管理列表>
     * @param     page *
     * @param     limit *
     * @param     pscRechargeOrderResp *
     * @return com.intelliquor.cloud.shop.common.entity.PageResponse<com.intelliquor.cloud.shop.system.model.resp.PscRechargeOrderResp>
     * <AUTHOR>
     * @date 2020/10/28
     **/
    @ApiOperation(value = "预售卡充值订单管理列表", notes = "预售卡充值订单管理列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @GetMapping(value = "/getSelectCardOrderList")
    public PageResponse<PscRechargeOrderResp> selectCardOrderList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                  @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                  PscRechargeOrderResp pscRechargeOrderResp) {

        PageHelper.startPage(page, limit);
        List<PscRechargeOrderResp> list = preSaleShopCardService.selectCardOrderList(SearchUtil.getSearch(pscRechargeOrderResp));
        PageInfo<PscRechargeOrderResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    /***
     * <TODO 预售卡充值订单导出>
     * @param     resp *
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.lang.String>
     * <AUTHOR>
     * @date 2020/10/29
     **/
    @ApiOperation(value = "预售卡充值订单导出", notes = "预售卡充值订单导出")
    @PostMapping(value = "/export")
    public Response<String> export(@RequestBody PscRechargeOrderResp resp) throws Exception {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);
        preSaleShopCardService.export(resp);
        return Response.ok("导出成功！");
    }
}