package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.dao.DigitalMarketingDao;
import com.intelliquor.cloud.shop.system.model.*;
import com.intelliquor.cloud.shop.system.util.Paging;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 景芝营销数字大屏系统接口
 *
 * <AUTHOR>
 * @date 2019/09/06
 */
@Service
public class DigitalMarketingService {

    @Autowired
    private DigitalMarketingDao digitalMarketingDao;


    /**
     * 获取累计注册门店总数
     *
     * @param searchMap
     * @return
     */
    public TerminalDigitalStatisticsModel getTerminalDigitalStatistics(Map<String, Object> searchMap) {
        TerminalDigitalStatisticsModel digitalStatisticsModel = new TerminalDigitalStatisticsModel();
        digitalStatisticsModel.setTerminalTotalCount(digitalMarketingDao.selectTerminalTotalCount(searchMap));
        digitalStatisticsModel.setTerminalActiveTotalCount(digitalMarketingDao.selectTerminalActivieTotalCount(searchMap));
        digitalStatisticsModel.setYesterdayTerminalRegisterCount(digitalMarketingDao.selectTerminalYesterdayRegisterCount(searchMap));
        digitalStatisticsModel.setYesterdayTerminalActiveCount(digitalMarketingDao.selectYesterdayTerminalActiveCount(searchMap));
        digitalStatisticsModel.setPurchaseTotalCount(digitalMarketingDao.selectPurchaseTotalCount(searchMap));
        digitalStatisticsModel.setYesterdayPurchaseCount(digitalMarketingDao.selectYesterdayPurchaseCount(searchMap));
        return digitalStatisticsModel;
    }

    /**
     * 查询数据
     *
     * @return
     */
    public void selectTerminalListByPage(Paging<TerminalJzSimpleModel> paging) {
        paging.setTotal(digitalMarketingDao.selectListByCount(paging.getSearchMap()));
        List<TerminalJzSimpleModel> list = digitalMarketingDao.selectListByPage(paging.getSearchMap());
        paging.setData(list);
    }


    /**
     * 查询活跃终端分页数据
     *
     * @return
     */
    public void selectTerminalActiveListByPage(Paging<TerminalJzSimpleModel> paging) {
        paging.setTotal(digitalMarketingDao.selectTerminalActiveListByCount(paging.getSearchMap()));
        List<TerminalJzSimpleModel> list = digitalMarketingDao.selectTerminalActiveListByPage(paging.getSearchMap());
        paging.setData(list);
    }

    /**
     * 查询消费者扫码明细
     *
     * @return
     */
    public void selectCustomerByPage(Paging<WechatJzCustomerScanCodeModel> paging) {
        paging.setTotal(digitalMarketingDao.selectCustomerListByCount(paging.getSearchMap()));
        List<WechatJzCustomerScanCodeModel> list = digitalMarketingDao.selectCustomerListByPage(paging.getSearchMap());
        paging.setData(list);
    }

    /**
     * 查询数据(不分页)
     *
     * @return
     */
    public List<TerminalJzSimpleModel> selectTerminalList(Map<String, Object> searchMap) {
        return digitalMarketingDao.selectListByPage(searchMap);
    }

    /**
     * 查询活跃终端(不分页)数据
     *
     * @return
     */
    public List<TerminalJzSimpleModel> selectTerminalActiveList(Map<String, Object> searchMap) {
        return digitalMarketingDao.selectTerminalActiveListByPage(searchMap);
    }

    /**
     * 终端数字地图数据
     *
     * @param searchMap
     * @return
     */
    public TerminalDigitalStatisticsModel getTerminalDigitalMapData(Map<String, Object> searchMap) {
        TerminalDigitalStatisticsModel digitalStatisticsModel = new TerminalDigitalStatisticsModel();
        digitalStatisticsModel.setJzTerminalList(digitalMarketingDao.selectTerminalList(searchMap));
        return digitalStatisticsModel;
    }

    /**
     * 终端数字地图终端 点 数据
     *
     * @param searchMap
     * @return
     */
    public TerminalDigitalStatisticsModel getTerminalPointsData(Map<String, Object> searchMap) {
        TerminalDigitalStatisticsModel digitalStatisticsModel = new TerminalDigitalStatisticsModel();
        digitalStatisticsModel.setJzPointList(digitalMarketingDao.selectTerminalPointList(searchMap));
        return digitalStatisticsModel;
    }


    /**
     * 通过终端id查询终端详情
     *
     * @param searchMap
     * @return
     */
    public TerminalJzSimpleModel getTerminalInfoById(Map<String, Object> searchMap) {
        return   digitalMarketingDao.selectTerminalById(searchMap);
    }

    /**
     * 终端数字地图每个市的终端数量
     *
     * @param searchMap
     * @return
     */
    public List<DigitalMarketingMapCountModel> selectTerminalCountForMap(Map<String, Object> searchMap) {
        return digitalMarketingDao.selectTerminalCountForMap(searchMap);
    }

    /**
     * 消费者扫码地图中每个市终端数量
     *
     * @param searchMap
     * @return
     */
    public List<DigitalMarketingMapCountModel> selectCustomerCountForMap(Map<String, Object> searchMap) {
        return digitalMarketingDao.selectCustomerCountForMap(searchMap);
    }


    /**
     * 获取扫码消费者统计数据
     *
     * @param searchMap
     * @return
     */
    public CustomerScanCodeStatisticsModel getCustomerScanCodeStatistics(Map<String, Object> searchMap) {
        CustomerScanCodeStatisticsModel customerScanCodeStatisticsModel = new CustomerScanCodeStatisticsModel();
        customerScanCodeStatisticsModel.setScanCodeCustomerTotalCount(digitalMarketingDao.selectScanCodeCustomerTotalCount(searchMap));
        customerScanCodeStatisticsModel.setOpenBottleTotalCount(digitalMarketingDao.selectOpenBottleTotalCount(searchMap));
        customerScanCodeStatisticsModel.setYesterdayOpenBottleCount(digitalMarketingDao.selectYesterdayOpenBottleCount(searchMap));
        customerScanCodeStatisticsModel.setYesterdayScanCodeCustomerCount(digitalMarketingDao.selectYesterdayScanCodeCustomerCount(searchMap));
        return customerScanCodeStatisticsModel;
    }


    /**
     * 消费者扫码地图数据
     *
     * @param searchMap
     * @return
     */
    public List<CustomerScanCodeSimpleModel> getCustomerScanCodeMapData(Map<String, Object> searchMap) {
        List<CustomerScanCodeSimpleModel> customerScanCodeSimpleModels = digitalMarketingDao.selectCustomerScanCodeList(searchMap);
        return customerScanCodeSimpleModels;
    }


    /**
     * 查询消异常终端
     *
     * @return
     */
    public void selectExceptionTerminalListByPage(Paging<TerminalJzSimpleModel> paging) {
        paging.setTotal(digitalMarketingDao.selectExceptionTerminalCount(paging.getSearchMap()));
        List<TerminalJzSimpleModel> list = digitalMarketingDao.selectExceptionTerminalListByPage(paging.getSearchMap());
        paging.setData(getExceptionCause(list));
    }


    /**
     * 查询消异常终端
     *
     * @return
     */
    public List<TerminalJzSimpleModel> selectExceptionTerminalList() {
        List<TerminalJzSimpleModel> list = digitalMarketingDao.selectExceptionTerminalList();
       return getExceptionCause(list);
    }

    /**
     * 获取异常原因
     * @param list
     * @return
     */
    public List<TerminalJzSimpleModel> getExceptionCause(List<TerminalJzSimpleModel> list) {
        for (TerminalJzSimpleModel model : list) {
            StringBuilder builder = new StringBuilder();
            //累计终端进货150箱及以上
            if (model.getBoxCount() > 150) {
                builder.append("累计终端进货150箱及以上");
                //累计终端进货150箱及以上,且消费者扫码率为0%的终端
                if (Double.valueOf(model.getXfzCount()) / Double.valueOf(model.getBottleCount()) == 0) {
                    builder.append("且消费者扫码率为0%的终端；");
                }
                //累计终端进货150箱及以上,且消费者扫码率30%及以上且人均开瓶数5瓶的终端
                if (Double.valueOf(model.getXfzCount()) / Double.valueOf(model.getBottleCount()) >= 0.3 &&
                        Double.valueOf(model.getXfzCount()) / Double.valueOf(model.getXfzPCount()) >= 5) {
                    builder.append("且消费者扫码率30%及以上且人均开瓶数5瓶的终端；");
                }
                //累计终端进货150箱及以上,且消费者扫码率30%及以上且日均开瓶数5瓶的终端
                if (Double.valueOf(model.getXfzCount()) / Double.valueOf(model.getBottleCount()) >= 0.3 &&
                        Double.valueOf(model.getXfzCount()) / Double.valueOf(model.getScanDays()) >= 5) {
                    builder.append("且消费者扫码率30%及以上且日均开瓶数5瓶的终端；");
                }
                //累计终端进货150箱及以上,且消费者单人累计扫码瓶数20瓶及以上的终端
                if (model.getIsMoreThan() == 1) {
                    builder.append("且消费者单人累计扫码瓶数20瓶及以上的终端；");
                }
            }
            model.setExceptionCause(builder.toString());
        }
        return list;
    }

    /**
     * 消费者数字地图终端 点 数据
     *
     * @param searchMap
     * @return
     */
    public TerminalDigitalStatisticsModel getCustomerPointsData(Map<String, Object> searchMap) {
        TerminalDigitalStatisticsModel digitalStatisticsModel = new TerminalDigitalStatisticsModel();
        digitalStatisticsModel.setJzPointList(digitalMarketingDao.selectCustomerPointList(searchMap));
        return digitalStatisticsModel;
    }
}