package com.intelliquor.cloud.shop.system.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import lombok.Data;

/**
 * 消费者中奖信息
 * Created by Administrator on 2020/8/21 0021.
 */
@Data
public class ConsumerRewardInfoReq {
    /**
     * 消费者扫码日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String scanTimeStart;
    /**
     * 消费者扫码日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String scanTimeEnd;
    /**
     * 微信号
     */
    private String openId;
    /**
     * 微信名称
     */
    private String nickname;
    /**
     * 抽奖活动
     */
    private String activityName;
    /**
     * 产品编码
     */
    private String goodsCode;

    /**
     * 产品名称
     */
    private String goodsName;
    /**
     * 条码信息
     */
    private String qrcode;
    /**
     * 消费者扫码位置
     */
    private String drawRegionName;
    /**
     * 终端有效（或待确认）扫码日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String shopScanTimeStart;
    /**
     * 终端有效（或待确认）扫码日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String shopScanTimeEnd;
    /**
     * 终端编码
     */
    private String shopId;
    /**
     * 终端编码
     */
    private String diyCode;

    /**
     * 终端名称
     */
    private String shopName;

    /**
     * 终端业务区域编码
     */
    private String shopOrgCode;

    /**
     * 终端业务区域
     */
    private String shopOrgName;
    /**
     * 经销商编码
     */
    private String dealerNo;

    /**
     * 经销商名称
     */
    private String dealerName;
    /**
     * 对经销商发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String outTimeStart;
    /**
     * 对经销商发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String outTimeEnd;
    /**
     * 公司id
     */
    private Long companyId;
    private OrderDownloadCenterModel downloadCenterModel;

    private Integer pageSize;
    private Integer offset;
}
