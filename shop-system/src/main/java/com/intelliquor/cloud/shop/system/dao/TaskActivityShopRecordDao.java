package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.TaskActivityShopRecordModel;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;
import java.util.List;
/**
* 描述：活动任务-终端完成次数记录表 Dao接口
* <AUTHOR>
* @date 2019-11-04
*/
public interface TaskActivityShopRecordDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<TaskActivityShopRecordModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(TaskActivityShopRecordModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(TaskActivityShopRecordModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer updateActivityShopRecord(TaskActivityShopRecordModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    TaskActivityShopRecordModel getById(Integer id);

    TaskActivityShopRecordModel getByShopIdAndTaskActivityId(@Param("shopId") Integer shopId,@Param("taskActivityId") Integer taskActivityId);

    BigDecimal queryCount(@Param("shopId") Integer shopId,@Param("taskActivityId") Integer taskActivityId);

    TaskActivityShopRecordModel lock(Integer id);

    Integer addCount(@Param("id") Integer id,@Param("count") BigDecimal count);


}
