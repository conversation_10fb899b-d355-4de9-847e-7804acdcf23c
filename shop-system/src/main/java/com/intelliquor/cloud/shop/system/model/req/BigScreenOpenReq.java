package com.intelliquor.cloud.shop.system.model.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 大屏请求
 * @auhtor xn
 * @date 2021.05.14
 *
 */
@Data
public class BigScreenOpenReq implements Serializable {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 商户Id
     */
    private  Integer companyId;

    /**
     * 排序的字段
     */
    private String sortCode;

    /**
     * 顺序
     */
    private String sortRole;

    /**
     * 商品编码
     */
    private String goodsCode;
}
