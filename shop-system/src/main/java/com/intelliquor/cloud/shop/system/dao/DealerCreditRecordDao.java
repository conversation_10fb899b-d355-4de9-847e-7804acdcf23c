package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.DealerCreditRecordModel;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
* 描述：授信额度调整记录表 Dao接口
* <AUTHOR>
* @date 2021-12-08
*/
@Repository
public interface DealerCreditRecordDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<DealerCreditRecordModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(DealerCreditRecordModel model);
}