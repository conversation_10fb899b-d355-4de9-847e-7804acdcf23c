package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.StorageScanCode;
import com.intelliquor.cloud.shop.system.model.StorageScanCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StorageScanCodeMapper {
    int countByExample(StorageScanCodeExample example);

    int deleteByExample(StorageScanCodeExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StorageScanCode record);

    int insertSelective(StorageScanCode record);

    List<StorageScanCode> selectByExample(StorageScanCodeExample example);

    List<String> selectCodeUsed(@Param("list") List<String> list);

    StorageScanCode selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StorageScanCode record, @Param("example") StorageScanCodeExample example);

    int updateByExample(@Param("record") StorageScanCode record, @Param("example") StorageScanCodeExample example);

    int updateByPrimaryKeySelective(StorageScanCode record);

    int updateByPrimaryKey(StorageScanCode record);
}