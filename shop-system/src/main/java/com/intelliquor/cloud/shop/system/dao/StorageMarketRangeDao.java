package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.StorageMarketRange;
import com.intelliquor.cloud.shop.system.model.StorageMarketRangeExample;
import com.intelliquor.cloud.shop.system.model.req.StorageMarketRangeReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StorageMarketRangeDao {
    int countByExample(StorageMarketRangeExample example);

    int deleteByExample(StorageMarketRangeExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StorageMarketRange record);

    int insertSelective(StorageMarketRange record);

    List<StorageMarketRange> selectByExample(StorageMarketRangeExample example);

    StorageMarketRange selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StorageMarketRange record, @Param("example") StorageMarketRangeExample example);

    int updateByExample(@Param("record") StorageMarketRange record, @Param("example") StorageMarketRangeExample example);

    int updateByPrimaryKeySelective(StorageMarketRange record);

    int updateByPrimaryKey(StorageMarketRange record);

    void batchInsert(List<StorageMarketRange> list);

    List<StorageMarketRange> searchList(StorageMarketRangeReq req);
}