package com.intelliquor.cloud.shop.system.controller.open;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.ShopCarouselBannerModel;
import com.intelliquor.cloud.shop.system.model.gt.GtUnionGoodsModel;
import com.intelliquor.cloud.shop.system.service.gt.GtUnionGoodsService;
import com.intelliquor.cloud.shop.system.service.impl.gt.ShopCarouselBannerService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2021/01/17 17:40
 * @Description: 国台联盟商品-控制层-内部开放接口便于其他系统调用
 */
@Slf4j
@RestController
@RequestMapping("/inside/open/gt/unionGoods")
public class GtUnionGoodsOpenController {

    @Autowired
    private GtUnionGoodsService gtUnionGoodsService;

    @Autowired
    private ShopCarouselBannerService bannerService;

    @ApiOperation(value = "获取联盟店下的可售商品列表", notes = "获取联盟店下的可售商品列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "shopId", value = "联盟店id", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "companyId", value = "公司id", required = true, paramType = "query", dataType = "int")
    })
    @GetMapping(value = "/queryShopEnableSaleGoodsList")
    public RestResponse<GtUnionGoodsModel> queryShopEnableSaleGoodsList(@RequestParam(value = "page", required = true) Integer page,
                                                                        @RequestParam(value = "limit", required = true) Integer limit,
                                                                        @RequestParam(value = "companyId", required = true) Integer companyId,
                                                                        @RequestParam(value = "shopId", required = true) Integer shopId) {
        try {
            if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(limit)) {
                return RestResponse.error("分页信息为空");
            }
            if (ObjectUtil.isEmpty(shopId)) {
                return RestResponse.error("联盟店id为空");
            }
            if (ObjectUtil.isEmpty(companyId)) {
                return RestResponse.error("公司id为空");
            }
            PageMethod.startPage(page, limit);
            List<GtUnionGoodsModel> gtUnionGoodsModels = gtUnionGoodsService.queryShopEnableSaleGoodsList(shopId, companyId);
            PageInfo<GtUnionGoodsModel> pageInfo = new PageInfo<>(gtUnionGoodsModels);
            return RestResponse.success(pageInfo);
        } catch (Exception e) {
            log.error("获取联盟店下的可售商品列表失败：{}", e);
            return RestResponse.error("获取联盟店下的可售商品列表失败");
        }
    }


    @ApiOperation(value = "获取联盟店下的通用版可售商品列表", notes = "获取联盟店下的可售商品列表", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "shopId", value = "联盟店id", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "companyId", value = "公司id", required = true, paramType = "query", dataType = "int")
    })
    @GetMapping(value = "/queryCurrencyEnableSaleGoodsList")
    public RestResponse<GtUnionGoodsModel> queryCurrencyEnableSaleGoodsList(@RequestParam(value = "page", required = true) Integer page,
                                                                        @RequestParam(value = "limit", required = true) Integer limit,
                                                                        @RequestParam(value = "companyId", required = true) Integer companyId,
                                                                        @RequestParam(value = "shopId", required = true) Integer shopId) {
        try {
            if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(limit)) {
                return RestResponse.error("分页信息为空");
            }
            if (ObjectUtil.isEmpty(shopId)) {
                return RestResponse.error("联盟店id为空");
            }
            if (ObjectUtil.isEmpty(companyId)) {
                return RestResponse.error("公司id为空");
            }
            PageMethod.startPage(page, limit);
            List<GtUnionGoodsModel> gtUnionGoodsModels = gtUnionGoodsService.queryCurrencySaleGoodsList(shopId, companyId);
            PageInfo<GtUnionGoodsModel> pageInfo = new PageInfo<>(gtUnionGoodsModels);
            return RestResponse.success(pageInfo);
        } catch (Exception e) {
            log.error("获取联盟店下的可售商品列表失败：{}", e);
            return RestResponse.error("获取联盟店下的可售商品列表失败");
        }
    }

    @ApiOperation(value = "获取联盟店下的国台商品数据", notes = "获取联盟店下的国台商品数据", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "goodsId", value = "商品id", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "shopId", value = "联盟id", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "companyId", value = "商户di", required = true, paramType = "query", dataType = "int")
    })
    @GetMapping(value = "/getGtShopGoodsData")
    public RestResponse<GtUnionGoodsModel> getGtShopGoodsData(@RequestParam(value = "goodsId", required = true) Integer goodsId,
                                                              @RequestParam(value = "shopId", required = true) Integer shopId,
                                                              @RequestParam(value = "companyId", required = true) Integer companyId) {
        try {
            if (ObjectUtil.isEmpty(goodsId)) {
                return RestResponse.error("商品id为空");
            }
            if (ObjectUtil.isEmpty(shopId)) {
                return RestResponse.error("联盟店id为空");
            }
            if (ObjectUtil.isEmpty(companyId)) {
                return RestResponse.error("商户id为空");
            }
            GtUnionGoodsModel gtUnionGoodsModel = gtUnionGoodsService.getGtShopGoodsData(goodsId, shopId, companyId);
            return RestResponse.success(gtUnionGoodsModel);
        } catch (BusinessException e) {
            log.error("获取联盟店下的国台商品数据失败：BusinessException:{}", e);
            return RestResponse.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取联盟店下的国台商品数据失败：{}", e);
            return RestResponse.error("获取联盟店下的国台商品数据失败");
        }
    }

    /**
     * 获取配置的轮播图
     * @param companyId
     * @return
     */
    @RequestMapping(value="/getShopBannerList")
    public RestResponse<ShopCarouselBannerModel> getShopBannerList(Integer companyId) {

        ShopCarouselBannerModel bannerModel = new ShopCarouselBannerModel();
        bannerModel.setCompanyId(companyId);
        bannerModel.setStatus(1);
        List<ShopCarouselBannerModel> list = bannerService.selectList(SearchUtil.getSearch(bannerModel));

        return RestResponse.success(list);
    }



}
