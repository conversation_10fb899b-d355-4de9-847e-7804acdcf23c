package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.DefaultRemarkModel;
import java.util.Map;
import java.util.List;
/**
* 描述：默认备注信息表 Dao接口
* <AUTHOR>
* @date 2019-10-28
*/
public interface DefaultRemarkDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<DefaultRemarkModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(DefaultRemarkModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(DefaultRemarkModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    DefaultRemarkModel getById(Integer id);

}