package com.intelliquor.cloud.shop.system.model;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 描述：从扫码奖励扫码明细表获取的商品经销商终端关联关系表实体类
 *
 * <AUTHOR>
 * @date 2020-03-27
 */
public class RewardScanCodeRelationRecordModel {


    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "最近一次更新id")
    private int lastId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    public RewardScanCodeRelationRecordModel() {
    }

    public RewardScanCodeRelationRecordModel(Integer id, int lastId) {
        this.id = id;
        this.lastId = lastId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public int getLastId() {
        return lastId;
    }

    public void setLastId(int lastId) {
        this.lastId = lastId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}