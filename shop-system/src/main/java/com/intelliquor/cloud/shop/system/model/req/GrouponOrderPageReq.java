package com.intelliquor.cloud.shop.system.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.system.model.GrouponOrderDetail;
import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/12 18:24
 */
@Data
public class GrouponOrderPageReq {
    private Integer pageNum = 1;
    private Integer pageSize = 10;
    private String startTime;
    private String endTime;
    private Integer sendType;//配送方式
    private String activityName; //活动名称
    private String shopName; //终端名称
    private String shopPhone; //终端电话
    private String goodsName; //商品名称
    private String sendTimeStart; //
    private String sendTimeEnd; //
    private String receivedTimeStart; //
    private String receivedTimeEnd; //
    private Integer isCloudApp;  //是否云店小程序 null不是  other是

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单状态 0未支付 1待成团 2代发货 3已发货 4已完成 5拼团失败
     */
    private Integer orderStatus;

    /**
     * 订单类型 1多人拼团 2单人订单
     */
    private Integer orderType;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 支付方式 1微信支付
     */
    private Integer payType;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    /**
     * 收货方式 1自提 2邮寄
     */
    private Integer receivedType;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 收货时间
     */
    private Date receivedTime;

    /**
     * 收货人姓名
     */
    private String receivedName;

    /**
     * 收货人电话
     */
    private String receivedMobile;

    /**
     * 收货人地址
     */
    private String receivedAddress;

    /**
     * 消费者unionid
     */
    private String unionid;

    /**
     * 消费者openid
     */
    private String openid;

    /**
     * 消费者头像
     */
    private String headImg;

    /**
     * 发货时间
     */
    private Date sendTime;

    /**
     * 终端店id
     */
    private Integer shopId;

    /**
     * 团编号
     */
    private String grouponCode;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 快递编号
     */
    private String expressCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商户id
     */
    private Integer companyId;

    /**
     * 活动id
     */
    private Integer activityId;

    /**
     * 云众小程序appid
     *
     * @mbg.generated
     */
    private String appid;

    /**
     * 商品列表
     */
    List<GrouponOrderDetail> orderDetailList = new ArrayList<>();

    /**
     * 订单状态 0未支付 1待成团 2代发货 3已发货 4已完成 5拼团失败(从数据库中查出的状态)
     */
    private Integer orderStatusFromDataBase;

    /**
     * 发货时间(从数据库中查出的时间)
     */
    private Date sendTimeFromDataBase;

    private static final long serialVersionUID = 1L;

    /**
     * 下载中心实体
     */
    private OrderDownloadCenterModel downloadCenterModel;
}
