package com.intelliquor.cloud.shop.system.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 描述：景酱活动实体类
* <AUTHOR>
* @date 2020-09-29
*/
@Data
public class SauceActivityModel {


    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "一件对应多少瓶")
    private Integer standard;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "最低价格")
    private BigDecimal minGoodsPrice;

    @ApiModelProperty(value = "最高价格")
    private BigDecimal maxGoodsPrice;

    @ApiModelProperty(value = "推广返利 0：未开启，1开启")
    private Integer promotionRebateStatus;

    @ApiModelProperty(value = "推广返利金额")
    private BigDecimal promotionRebate;

    @ApiModelProperty(value = "销售返利 0：未开启，1开启")
    private Integer salesRebateStatus;

    @ApiModelProperty(value = "推广返利金额")
    private BigDecimal salesRebate;

    @ApiModelProperty(value = "渠道id")
    private String channelId;

    @ApiModelProperty(value = "主图")
    private String mainImg;



    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

    @ApiModelProperty(value = "1有效0无效")
    private Integer status;

    @ApiModelProperty(value = "0未删除1已删除")
    private Integer isDelete;

    @ApiModelProperty(value = "商户ID")
    private Integer companyId;


    @ApiModelProperty(value="范围json")
    private String rangeJson;

    @ApiModelProperty(value = "创建人")
    private Integer  createUser;

    @ApiModelProperty(value = "申请时间")
    @JSONField(format= "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间，时间戳")
    @JSONField(format= "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @ApiModelProperty(value = "范围")
    List<SauceActivityRangeModel> ranges;

    /**
    * 排序字段默认为id
    */
    private String sortCode = "id";

    /**
    * 排序规则默认为降序排列(DESC/ASC)
    */
    private String sortRole = "DESC";

    private Integer sortId;

    @ApiModelProperty(value = "商品会员价格")
    private BigDecimal goodsMemberPrice;

    @ApiModelProperty(value = "商品默认价格")
    private BigDecimal goodsDefaultPrice;

    /**
     * 商店库存
     */
    private Integer stock;

    /**
     * 店铺信息
     */
    private Map<String, Object> shopInfo;

    @ApiModelProperty(value = "固定费用")
    private BigDecimal fixedFee;

}