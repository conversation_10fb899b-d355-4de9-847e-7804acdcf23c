package com.intelliquor.cloud.shop.system.model.req;

import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020.08.19
 * 产品有效扫码前（后）无效扫码查询的入参
 */
@Data
public class RewardScanCodeRecordInvalidDetailReq {


    /**
     * 类型
     */
    private Integer type;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 产品编码
     */
    private String goodsCode;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 产品名称
     */
    private String code;

    /**
     * 区域编码
     */
    private String orgCode;

    /**
     * 区域名称
     */
    private String orgName;

    /**
     * 终端编码
     */
    private String diyCode;

    /**
     * 终端编码
     */
    private String shopName;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "id";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";


    /**
     * 下载中心实体
     */
    private OrderDownloadCenterModel downloadCenterModel;


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public OrderDownloadCenterModel getDownloadCenterModel() {
        return downloadCenterModel;
    }

    public void setDownloadCenterModel(OrderDownloadCenterModel downloadCenterModel) {
        this.downloadCenterModel = downloadCenterModel;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public String getDiyCode() {
        return diyCode;
    }

    public void setDiyCode(String diyCode) {
        this.diyCode = diyCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSortCode() {
        return sortCode;
    }

    public String getSortRole() {
        return sortRole;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }
}
