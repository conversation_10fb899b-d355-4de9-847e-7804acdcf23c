package com.intelliquor.cloud.shop.system.model;

import java.util.ArrayList;
import java.util.List;

public class TerminalDigitalStatisticsModel {
    /**
     * 累计注册门店总数
     */
    private Integer terminalTotalCount;
    /**
     * 累计活跃门店数
     */
    private Integer terminalActiveTotalCount;
    /**
     * 昨日门店注册数量
     */
    private Integer yesterdayTerminalRegisterCount;
    /**
     * 日活门店数
     */
    private Integer yesterdayTerminalActiveCount;
    /**
     * 累计进货数（瓶）
     */
    private Integer purchaseTotalCount;
    /**
     * 昨日进货数（瓶）
     */
    private Integer yesterdayPurchaseCount;


    private List<TerminalJzSimpleModel> jzTerminalList = new ArrayList<>();

    private List<TerminalJzPointModel> jzPointList = new ArrayList<>();


    public Integer getTerminalTotalCount() {
        return terminalTotalCount;
    }

    public void setTerminalTotalCount(Integer terminalTotalCount) {
        this.terminalTotalCount = terminalTotalCount;
    }

    public Integer getTerminalActiveTotalCount() {
        return terminalActiveTotalCount;
    }

    public void setTerminalActiveTotalCount(Integer terminalActiveTotalCount) {
        this.terminalActiveTotalCount = terminalActiveTotalCount;
    }

    public Integer getYesterdayTerminalRegisterCount() {
        return yesterdayTerminalRegisterCount;
    }

    public void setYesterdayTerminalRegisterCount(Integer yesterdayTerminalRegisterCount) {
        this.yesterdayTerminalRegisterCount = yesterdayTerminalRegisterCount;
    }

    public Integer getYesterdayTerminalActiveCount() {
        return yesterdayTerminalActiveCount;
    }

    public void setYesterdayTerminalActiveCount(Integer yesterdayTerminalActiveCount) {
        this.yesterdayTerminalActiveCount = yesterdayTerminalActiveCount;
    }

    public Integer getPurchaseTotalCount() {
        return purchaseTotalCount;
    }

    public void setPurchaseTotalCount(Integer purchaseTotalCount) {
        this.purchaseTotalCount = purchaseTotalCount;
    }

    public Integer getYesterdayPurchaseCount() {
        return yesterdayPurchaseCount;
    }

    public void setYesterdayPurchaseCount(Integer yesterdayPurchaseCount) {
        this.yesterdayPurchaseCount = yesterdayPurchaseCount;
    }

    public List<TerminalJzSimpleModel> getJzTerminalList() {
        return jzTerminalList;
    }

    public void setJzTerminalList(List<TerminalJzSimpleModel> jzTerminalList) {
        this.jzTerminalList = jzTerminalList;
    }

    public List<TerminalJzPointModel> getJzPointList() {
        return jzPointList;
    }

    public void setJzPointList(List<TerminalJzPointModel> jzPointList) {
        this.jzPointList = jzPointList;
    }
}
