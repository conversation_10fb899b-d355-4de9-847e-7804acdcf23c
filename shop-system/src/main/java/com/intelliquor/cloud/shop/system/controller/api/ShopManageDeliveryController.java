package com.intelliquor.cloud.shop.system.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.TerminalAuthManagDeliveryModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.service.ITerminalAuthManagDeliveryService;
import com.intelliquor.cloud.shop.system.dao.ShopTerminalDeliveryDao;
import com.intelliquor.cloud.shop.system.model.TerminalManageModel;
import com.intelliquor.cloud.shop.system.service.ShopManageDeliveryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 会员业代发货授权
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/shopManageDelivery")
public class ShopManageDeliveryController {

    private final ShopManageDeliveryService shopManageDeliveryService;

    private final ITerminalAuthManagDeliveryService terminalAuthManagDeliveryService;

    private final UserContext userContext;


    /**
     * 获取出库给自己的记录
     *
     * @param shopId
     * @return
     */
    @GetMapping(value = "/terminalManage")
    public Response<List<TerminalManageModel>> terminalManage(@RequestParam(value = "shopId", required = true) String shopId) {
        TerminalManageModel terminalManageModel = new TerminalManageModel();
        terminalManageModel.setShopId(shopId);
        List<TerminalManageModel> terminalManageModelList = shopManageDeliveryService.shopTerminalManage(terminalManageModel);
        return Response.ok(terminalManageModelList);
    }


    @GetMapping(value = "/updateAuth")
    public Response<String> updateAuth(@RequestParam(value = "shopId", required = true) String shopId
            , @RequestParam(value = "isAuthManagDelivery", required = true) String isAuthManagDelivery
            , @RequestParam(value = "manageId", required = true) String manageId) {
//        TerminalManageModel terminalManageModel = new TerminalManageModel();
//        terminalManageModel.setShopId(shopId);
//        terminalManageModel.setIsAuthManagDelivery(Integer.parseInt(isAuthManagDelivery));
//        shopManageDeliveryService.updateAuth(terminalManageModel);
        List<TerminalAuthManagDeliveryModel> list = terminalAuthManagDeliveryService.list(
                new QueryWrapper<TerminalAuthManagDeliveryModel>().lambda()
                        .eq(TerminalAuthManagDeliveryModel::getTerminalShopId, shopId)
                        .eq(TerminalAuthManagDeliveryModel::getTerminalAccountManagerId, manageId)
                        .eq(TerminalAuthManagDeliveryModel::getDelFlag, false));

        if (CollectionUtils.isNotEmpty(list)) {
            TerminalAuthManagDeliveryModel terminalAuthManagDeliveryModel = list.get(0);
            terminalAuthManagDeliveryModel.setIsAuth(StringUtils.equals(isAuthManagDelivery, "1"));
            terminalAuthManagDeliveryService.update(terminalAuthManagDeliveryModel, new QueryWrapper<TerminalAuthManagDeliveryModel>().lambda()
                    .eq(TerminalAuthManagDeliveryModel::getTerminalShopId, shopId)
                    .eq(TerminalAuthManagDeliveryModel::getTerminalAccountManagerId, manageId)
                    .eq(TerminalAuthManagDeliveryModel::getDelFlag, false));
        } else {
            TerminalAuthManagDeliveryModel terminalAuthManagDeliveryModel = new TerminalAuthManagDeliveryModel();
            terminalAuthManagDeliveryModel.setTerminalShopId(Integer.valueOf(shopId));
            terminalAuthManagDeliveryModel.setTerminalAccountManagerId(Integer.valueOf(manageId));
            terminalAuthManagDeliveryModel.setIsAuth(StringUtils.equals(isAuthManagDelivery, "1"));
            terminalAuthManagDeliveryModel.setCreateTime(new Date());
            terminalAuthManagDeliveryModel.setCreateUser(userContext.getShopUserModel().getId());
            terminalAuthManagDeliveryModel.setCreateUserName(userContext.getShopUserModel().getName());
            terminalAuthManagDeliveryModel.setDelFlag(0);
            terminalAuthManagDeliveryService.save(terminalAuthManagDeliveryModel);
        }
        return Response.ok("修改成功");
    }

}
