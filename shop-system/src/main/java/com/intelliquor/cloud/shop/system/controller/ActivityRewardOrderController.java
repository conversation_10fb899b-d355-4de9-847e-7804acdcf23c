package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.intelliquor.cloud.shop.common.entity.FileItem;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.ActivityRewardRecordModel;
import com.intelliquor.cloud.shop.common.model.req.ActivityRewardOrderReceiptInformationReq;
import com.intelliquor.cloud.shop.common.utils.logistics.LogisticsCode;
import com.intelliquor.cloud.shop.common.enums.OrderTypeEnum;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.ActivityRewardOrderModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.req.ActivityRewardOrderReq;
import com.intelliquor.cloud.shop.common.model.req.FindLogisticsReq;
import com.intelliquor.cloud.shop.common.model.resp.ActivityRewardOrderResp;
import com.intelliquor.cloud.shop.common.model.resp.FindLogisticsResp;
import com.intelliquor.cloud.shop.common.service.ActivityRewardOrderService;
import com.intelliquor.cloud.shop.common.utils.ExcelTools;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.common.utils.logistics.LogisticsUtils;
import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import com.intelliquor.cloud.shop.system.service.OrderDownloadCenterService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequiredArgsConstructor
@RequestMapping("/activity/reward/order")
public class ActivityRewardOrderController {

    private final ActivityRewardOrderService activityRewardOrderService;

    private final OrderDownloadCenterService downloadCenterService;

    private final UserContext userContext;


    /**
     * 查询不同奖励类型的奖励订单
     *
     * @param page                   页码
     * @param limit                  条码
     * @param activityRewardOrderReq 查询参数
     * @return 实物奖励记录列表
     */
    @GetMapping("pageByRewardType")
    public RestResponse<List<ActivityRewardOrderResp>> pageByRewardType(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                ActivityRewardOrderReq activityRewardOrderReq) {
        try {
            List<ActivityRewardOrderResp> list = activityRewardOrderService.getPageForRewardType(page, limit, activityRewardOrderReq);
            return RestResponse.success(list);
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }


    @PostMapping("receiveAllGoods")
    public RestResponse<String> receiveAllGoods(@RequestBody ActivityRewardOrderReceiptInformationReq activityRewardOrderReceiptInformationReq) {
        try {
            activityRewardOrderService.updateOrderReceiptInformation(activityRewardOrderReceiptInformationReq);
            return RestResponse.success("领取成功");
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }

    @PostMapping("receiveGoods/{id}")
    public RestResponse<String> receiveGoods(@PathVariable("id") Integer id) {
        try {
            if (Objects.isNull(id)) {
                return RestResponse.error("未获取到奖励信息参数");
            }
            activityRewardOrderService.receiveGoods(id);
            return RestResponse.success("领取成功");
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }




    /**
     * 查询实物奖励记录列表
     *
     * @param page                   页码
     * @param limit                  条码
     * @param activityRewardOrderReq 查询参数
     * @return 实物奖励记录列表
     */
    @GetMapping("pageList")
    public RestResponse<List<ActivityRewardOrderResp>> pageList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                ActivityRewardOrderReq activityRewardOrderReq) {
        try {
            List<ActivityRewardOrderResp> list = activityRewardOrderService.getPageList(page, limit, activityRewardOrderReq);
            return RestResponse.success(list);
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 实物奖励发货
     *
     * @param activityRewardOrderModel 实物奖励信息
     */
    @PostMapping("sendGoods")
    public Response<String> sendGoods(@RequestBody ActivityRewardOrderModel activityRewardOrderModel) {
        try {
            activityRewardOrderService.sendGoods(activityRewardOrderModel);
            return Response.ok("发货成功");
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 确认收货
     *
     * @param orderId 订单id
     */
    @PostMapping("takeGoods")
    public RestResponse<String> takeGoods(@RequestBody Map<String, Object> map) {
        String orderId = (String) map.get("orderId");
        if (StringUtils.isBlank(orderId)) {
            return RestResponse.error("参数异常");
        }
        try {
            activityRewardOrderService.takeGoods(orderId);
            return RestResponse.success("收货成功");
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 查询订单物流信息
     *
     * @param 物流查询信息
     */
    @PostMapping("findLogistics")
    public RestResponse<ActivityRewardOrderModel> findLogistics(@RequestBody Map<String, Object> map) {
        String orderId = (String) map.get("orderId");
        if (StringUtils.isBlank(orderId)) {
            return RestResponse.error("参数异常");
        }
        try {
            ActivityRewardOrderModel activityRewardOrderModel = activityRewardOrderService.getById(orderId);
            String code = LogisticsCode.logisticsMap.get(activityRewardOrderModel.getExpressCompany());
            if (StringUtils.isBlank(code)) {
                return RestResponse.error("未查询到物流编码");
            }
            FindLogisticsReq findLogisticsReq = new FindLogisticsReq();
            findLogisticsReq.setLogisticsOrderCode(activityRewardOrderModel.getExpressCode());
            findLogisticsReq.setPhone(activityRewardOrderModel.getReceivedMobile());
            findLogisticsReq.setLogisticsCode(code);
            FindLogisticsResp logistics = LogisticsUtils.findLogistics(findLogisticsReq);

            if (logistics.getSuccess()) {
                activityRewardOrderModel.setFindLogisticsResp(logistics);
                return RestResponse.success("查询成功", activityRewardOrderModel);
            } else {
                return RestResponse.error("未查询到物流信息");
            }
        } catch (Exception e) {
            return RestResponse.error("物流接口请求异常" + e.getMessage());
        }
    }

    /**
     * 后台查询实物奖励记录列表
     *
     * @param page                   页码
     * @param limit                  条码
     * @param activityRewardOrderReq 查询参数
     * @return 实物奖励记录列表
     */
    @GetMapping("managerPageList")
    public PageResponse<List<ActivityRewardOrderResp>> managerPageList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                       @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                       ActivityRewardOrderReq activityRewardOrderReq) {
        List<ActivityRewardOrderResp> list = activityRewardOrderService.getManagerPageList(page, limit, activityRewardOrderReq);
        PageInfo<ActivityRewardOrderResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    /**
     * 导出实体发放记录
     */
    @PostMapping("export")
    public Response<String> export(@RequestBody ActivityRewardOrderReq activityRewardOrderReq) {
        try {

            // 1、添加下载中心数据
            OrderDownloadCenterModel downloadCenterModel = this.addOrderDownloadCenter(activityRewardOrderReq);

            // 2、上传文件
            String fileUrl = this.exportOrderFile(activityRewardOrderReq);

            // 3、修改下载中心状态
            downloadCenterModel.setFilePath(fileUrl);
            downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

            return Response.ok("导出成功");
        } catch (Exception e) {
            return Response.fail("导出失败：" + e.getMessage());
        }
    }

    /**
     * 添加下载中心数据
     */
    public OrderDownloadCenterModel addOrderDownloadCenter(ActivityRewardOrderReq activityRewardOrderReq) {
        OrderDownloadCenterModel downloadCenterModel = new OrderDownloadCenterModel();
        downloadCenterModel.setUserId(Long.valueOf(userContext.getUserInfo().getUserId()));
        downloadCenterModel.setUserName(userContext.getUserInfo().getUsername());
        List<OrderDownloadCenterModel.SelectConditionBody> selectConditionBodies = new ArrayList<>();

        OrderDownloadCenterModel.SelectConditionBody selectConditionBody3 = new OrderDownloadCenterModel.SelectConditionBody();
        selectConditionBody3.setName("终端名称");
        selectConditionBody3.setValue(activityRewardOrderReq.getShopName());

        OrderDownloadCenterModel.SelectConditionBody selectConditionBody4 = new OrderDownloadCenterModel.SelectConditionBody();
        selectConditionBody4.setName("终端编码");
        selectConditionBody4.setValue(activityRewardOrderReq.getShopCode());


        OrderDownloadCenterModel.SelectConditionBody selectConditionBody7 = new OrderDownloadCenterModel.SelectConditionBody();
        selectConditionBody7.setName("奖励产生日期");
        selectConditionBody7.setValue(StringUtils.isBlank(activityRewardOrderReq.getRewardStartTime()) ? "" : (activityRewardOrderReq.getRewardStartTime() + '-' + activityRewardOrderReq.getRewardEndTime()));

        OrderDownloadCenterModel.SelectConditionBody selectConditionBody11 = new OrderDownloadCenterModel.SelectConditionBody();
        selectConditionBody11.setName("发货状态");
        selectConditionBody11.setValue(Objects.isNull(activityRewardOrderReq.getOrderStatus()) ? "" : (activityRewardOrderReq.getOrderStatus() == 0 ? "未发货" : activityRewardOrderReq.getOrderStatus() == 1 ? "已发货" : "已收货"));
        selectConditionBodies.add(selectConditionBody3);
        selectConditionBodies.add(selectConditionBody4);
        selectConditionBodies.add(selectConditionBody7);
        selectConditionBodies.add(selectConditionBody11);

        downloadCenterModel.setSelectCondition(new Gson().toJson(selectConditionBodies));

        downloadCenterModel.setType(OrderTypeEnum.ACTIVITY_REWARD_ORDER_RECORD.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 创建文件并上传至阿里云OSS
     */
    public String exportOrderFile(ActivityRewardOrderReq activityRewardOrderReq) throws IOException {
        List<ActivityRewardOrderResp> list = activityRewardOrderService.export(activityRewardOrderReq);

        FileItem fileItem = ExcelTools.exportByFile(list, 1);
        String fileName = "AROR" + TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss") + ".xlsx";
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    /**
     * 导入物流信息
     *
     * @param file 文件
     */
    @PostMapping("importData")
    public RestResponse<String> importData(@RequestParam("file") MultipartFile file) {
        try {
            if (null != file) {
                activityRewardOrderService.importData(file);
                return RestResponse.success("导入成功");
            } else {
                return RestResponse.error("参数对象不能为空");
            }
        } catch (Exception e) {
            return RestResponse.error(e.getMessage() == null ? "格式异常" : e.getMessage());
        }
    }

    @PostMapping("findLogisticsMap")
    public Response<Map<String,String>> findLogisticsMap() {
        return Response.ok(LogisticsCode.logisticsMap);
    }
}
