package com.intelliquor.cloud.shop.system.controller.open;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.req.MarketContractReq;
import com.intelliquor.cloud.shop.common.model.req.MarketTerminalDataReq;
import com.intelliquor.cloud.shop.common.model.resp.MarketDealerContractResp;
import com.intelliquor.cloud.shop.common.model.resp.MarketDeliveryTerminalDataResp;
import com.intelliquor.cloud.shop.common.model.resp.MarketOpenAllopatryDataResp;
import com.intelliquor.cloud.shop.common.model.resp.MarketTerminalDataResp;
import com.intelliquor.cloud.shop.common.utils.GuotaiUtil;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.system.service.MarketOpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 布奖查询相关接口
 *
 * @CreateTime: 2024-04-19
 */
@Slf4j
@RestController
@RequestMapping("/open/market")
public class MarketOpenController {

    @Resource
    private MarketOpenService marketOpenService;

    /**
     * 获取终端数据
     *
     * @param page          页码
     * @param limit         每页条数
     * @param terminalIds   终端Id列表
     * @param shopName      终端名称
     * @param contractCodes 合同编码列表  示例：20240118030025,20240118030024
     * @param contractType  合同类型  示例：2
     * @param dealerCode    经销商编码 示例：111102998
     * @param dealerName    经销商名称 模糊搜索
     */
    @GetMapping("/terminalData")
    public PageResponse<MarketTerminalDataResp> terminalData(HttpServletRequest httpServletRequest,
                                                                   @RequestParam(value = "page", defaultValue = "1") int page,
                                                                   @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                   @RequestParam(value = "deputyCode", required = false) String deputyCode,
                                                                   @RequestParam(value = "terminalIds", required = false) String terminalIds,
                                                                   @RequestParam(value = "shopName", required = false) String shopName,
                                                                   @RequestParam(value = "contractCodes", required = false) String contractCodes,
                                                                   @RequestParam(value = "contractType", required = false) String contractType,
                                                                   @RequestParam(value = "dealerCode", required = false) String dealerCode,
                                                                   @RequestParam(value = "dealerName", required = false) String dealerName) {

        String token = httpServletRequest.getHeader("token");
        String timestampStr = httpServletRequest.getHeader("timestamp");
        //校验Token
        try {
            GuotaiUtil.verifyToken(token, timestampStr);
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        }


        MarketTerminalDataReq dataReq = new MarketTerminalDataReq();
        dataReq.setTerminalIds(terminalIds);
        dataReq.setContractCodes(contractCodes);
        dataReq.setDealerCode(dealerCode);
        dataReq.setDealerName(dealerName);
        dataReq.setContractType(contractType);
        dataReq.setShopName(shopName);
        dataReq.setDeputyCode(deputyCode);

        List<MarketTerminalDataResp>  list = marketOpenService.terminalData(page, limit, dataReq);
        PageInfo<MarketTerminalDataResp> dataRespPageInfo = new PageInfo<>(list);
        return PageResponse.ok(dataRespPageInfo);

    }


    /**
     * 分页查询合同数据
     *
     * @param req 合同查询条件
     * @return 合同数据
     */
    @PostMapping("/contractData")
    public PageResponse<MarketDealerContractResp> contractData(HttpServletRequest httpServletRequest,
                                                               @Validated @RequestBody MarketContractReq req) {

        String token = httpServletRequest.getHeader("token");
        String timestampStr = httpServletRequest.getHeader("timestamp");
        //校验Token
        try {
            GuotaiUtil.verifyToken(token, timestampStr);
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        }
        List<MarketDealerContractResp> list = marketOpenService.contractData(req);
        PageInfo<MarketDealerContractResp> page = new PageInfo<>(list);
        return PageResponse.ok(page);
    }


    /**
     * 查询入库终端
     *
     * @param codeHe    盒码
     * @param codeXiang 箱码
     */
    @GetMapping("/deliveryTerminalData")
    public RestResponse<MarketDeliveryTerminalDataResp> deliveryTerminalData(HttpServletRequest httpServletRequest,

                                                                             @RequestParam(value = "codeHe") String codeHe,
                                                                             @RequestParam(value = "codeXiang") String codeXiang) {

        String token = httpServletRequest.getHeader("token");
        String timestampStr = httpServletRequest.getHeader("timestamp");
        //校验Token
        try {
            GuotaiUtil.verifyToken(token, timestampStr);
        } catch (BusinessException e) {
            return RestResponse.error(e.getMessage());
        }

        if (ObjectUtil.isEmpty(codeHe)) {
            return RestResponse.error("盒码为空");
        }

        if (ObjectUtil.isEmpty(codeXiang)) {
            return RestResponse.error("箱码为空");
        }

        MarketDeliveryTerminalDataResp dataReq = new MarketDeliveryTerminalDataResp();
        dataReq.setCodeHe(codeHe);
        dataReq.setCodeXiang(codeXiang);

        MarketDeliveryTerminalDataResp resp = marketOpenService.deliveryTerminalData(dataReq);
        if (ObjectUtil.isEmpty(resp)) {
            return RestResponse.success("未查询到入库终端", resp);
        }else {
            resp.setCodeHe(codeHe);
            resp.setCodeXiang(codeXiang);
            return RestResponse.success("查询成功", resp);
        }
    }


    /**
     * 区域区隔判断是否异地
     *
     * @param province  省
     * @param city 市
     * @param codeHe 瓶码
     * @param contractCode 合同号
     */
    @GetMapping("/openBottleLegalityStatus")
    public RestResponse<MarketDeliveryTerminalDataResp> openBottleLegalityStatus(HttpServletRequest httpServletRequest,
                                                                                 @RequestParam(value = "province") String province,
                                                                                 @RequestParam(value = "city") String city,
                                                                                 @RequestParam(value = "contractCode") String contractCode,
                                                                                 @RequestParam(value = "codeHe") String codeHe)  {

        String token = httpServletRequest.getHeader("token");
        String timestampStr = httpServletRequest.getHeader("timestamp");

        log.info("布奖查询抽奖区域是否异地province:{},city:{},contractCode:{},codeHe:{}",province,city,contractCode,codeHe);
        //校验Token
        try {
            GuotaiUtil.verifyToken(token, timestampStr);
        } catch (BusinessException e) {
            return RestResponse.error(e.getMessage());
        }

        if (ObjectUtil.isEmpty(province)) {
            return RestResponse.error("省份不能为空");
        }

        if (ObjectUtil.isEmpty(city)) {
            return RestResponse.error("市不能为空");
        }

        if (ObjectUtil.isEmpty(contractCode)) {
            return RestResponse.error("合同号不能为空");
        }

        MarketOpenAllopatryDataResp dataReq = new MarketOpenAllopatryDataResp();
        dataReq.setProvince(province);
        dataReq.setCity(city);
        dataReq.setContractCode(contractCode);
        dataReq.setCodeHe(codeHe);

        MarketOpenAllopatryDataResp resp = marketOpenService.openBottleLegalityStatus(dataReq);

        return RestResponse.success("查询成功", resp);

    }
}


