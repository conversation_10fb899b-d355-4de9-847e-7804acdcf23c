package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.dao.DealerAreaDao;
import com.intelliquor.cloud.shop.system.model.DealerAreaModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 描述：经销商信息 服务实现层
 *
 * <AUTHOR>
 * @date 2019-11-21
 */
@Service
public class DealerAreaService {

    @Autowired
    private DealerAreaDao dealerAreaDao;

    /**
     * 查询数据
     *
     * @return
     */
    public List<DealerAreaModel> selectList(Map<String, Object> searchMap) {
        return dealerAreaDao.selectList(searchMap);
    }


    /**
     * 新增数据
     *
     * @param model
     */
    public void insert(DealerAreaModel model) {
        dealerAreaDao.insert(model);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    public void update(DealerAreaModel model) {
        dealerAreaDao.update(model);
    }

    /**
     * 删除数据
     *
     * @param id
     */
    public void delete(Integer id) {
        dealerAreaDao.delete(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public DealerAreaModel getById(Integer id) {
        return dealerAreaDao.getById(id);
    }
}