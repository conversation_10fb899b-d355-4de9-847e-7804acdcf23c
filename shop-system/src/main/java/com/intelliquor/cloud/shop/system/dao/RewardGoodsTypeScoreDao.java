package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.RewardGoodsTypeScoreModel;
import com.intelliquor.cloud.shop.system.model.resp.RewardGoodsTypeScoreResp;
import com.intelliquor.cloud.shop.system.model.resp.ShopIncomeResp;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
/**
* 描述：终端店奖励积分商品类型 Dao接口
* <AUTHOR>
* @date 2021-04-20
*/
public interface RewardGoodsTypeScoreDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<RewardGoodsTypeScoreModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(RewardGoodsTypeScoreModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(RewardGoodsTypeScoreModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    RewardGoodsTypeScoreModel getById(Integer id);

    /**
     * 根据终端ID和积分类型查询
     *
     * @param
     * @return
     */
    RewardGoodsTypeScoreModel getByShopIdAndScoreTypeId(@Param("shopId")Integer shopId,@Param("scoreTypeId")Integer scoreTypeId);

  /**
     * 根据终端用户ID和积分类型查询
     *
     * @param
     * @return
     */
    RewardGoodsTypeScoreModel getByShopUserIdAndScoreTypeId(@Param("shopUserId")Integer shopUserId,@Param("scoreTypeId")Integer scoreTypeId);


    /**
     * 添加积分
     * @param id
     * @param score
     */
    void  addScore(@Param("id") Integer id, @Param("score")Integer score);

    /**
     * 根据条件查询终端积分信息
     * @param param
     * @return
     */
    List<RewardGoodsTypeScoreResp> selectListByCondition(Map<String,Object> param);

    /**
     * 扣减积分
     * @param param
     * @return
     */
    Integer reduceScore(RewardGoodsTypeScoreModel param);

    ShopIncomeResp getShopIncome(Map<String, Object> param);
}