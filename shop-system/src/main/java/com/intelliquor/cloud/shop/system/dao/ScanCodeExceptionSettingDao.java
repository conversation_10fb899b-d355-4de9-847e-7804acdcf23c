package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.ScanCodeExceptionSettingModel;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
import java.util.List;
/**
* 描述：扫码异常限制 Dao接口
* <AUTHOR>
* @date 2019-12-11
*/
public interface ScanCodeExceptionSettingDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<ScanCodeExceptionSettingModel> selectList(Map<String, Object> searchMap);

    ScanCodeExceptionSettingModel selectSettingByCodeAndCompanyId(@Param("exceptionCode") String exceptionCode,@Param("companyId") Integer companyId);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(ScanCodeExceptionSettingModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(ScanCodeExceptionSettingModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    ScanCodeExceptionSettingModel getById(Integer id);

}