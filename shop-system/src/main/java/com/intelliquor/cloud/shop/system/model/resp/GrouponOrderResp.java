package com.intelliquor.cloud.shop.system.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.system.model.GrouponActivityRewardRecordModel;
import com.intelliquor.cloud.shop.system.model.GrouponOrderDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/13 11:04
 */
@Data
public class GrouponOrderResp {
    /**
     * id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单状态 0未支付 1待成团 2代发货(21仓库接单 22仓库打包中 23已出库) 3已收货 4已完成 5拼团失败 6已退款 7退款中
     */
    private Integer orderStatus;

    /**
     * 订单类型 1多人拼团 2单人订单
     */
    private Integer orderType;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 支付方式 1微信支付
     */
    private Integer payType;

    /**
     * 支付时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 收货方式 1自提 2邮寄
     */
    private Integer receivedType;

    /**
     * 下单时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 收货时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receivedTime;

    /**
     * 收货人姓名
     */
    private String receivedName;

    /**
     * 收货人电话
     */
    private String receivedMobile;

    /**
     * 收货人地址
     */
    private String receivedAddress;

    /**
     * 消费者unionid
     */
    private String unionid;

    /**
     * 消费者openid
     */
    private String openid;

    /**
     * 发货时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 终端店id
     */
    private Integer shopId;

    /**
     * 团编号
     */
    private String grouponCode;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 快递编号
     */
    private String expressCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商户id
     */
    private Integer companyId;

    //非domain
    private List<GrouponOrderDetail> grouponOrderDetailList;
    private String sellerName;  //发货人
    private String sellerMobile;  //发货电话
    private String sellerAddress;  //发货地址
    private Double sellerLon = 0d;  //发货方经度
    private Double sellerLat = 0d;  //发货方纬度

    private Double distance = 0d;  //距离
    private BigDecimal totalMoney;  //实付价格

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;  //完成拼团时间

    /**
     * 仓库接单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warehouseReceiveTime;

    /**
     * 仓库打包时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warehousePackTime;

    /**
     * 仓库发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warehouseOutTime;

    /**
     * 拼团失败时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date groupFailTime;

    /**
     * 退款成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundTime;

    /**
     * 退款中时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundingTime;

    /**
     * 待解锁商品
     */
    List<GrouponActivityRewardRecordModel> grouponActivityRewardRecordList = new ArrayList<>();

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 经销商联系人
     */
    private String dealerMan;

    /**
     * 经销商电话
     */
    private String dealerPhone;

    /**
     * 邮寄发货方式  1:厂家 2:进销商
     */
    private Integer sendType;

    private String couponInfo;  //卡券信息
}
