package com.intelliquor.cloud.shop.system.model.resp;

import com.intelliquor.cloud.shop.common.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020.08.18
 * 经销商串货日统计表
 */
@Excel(fileName = "经销商串货产品流向日统计表.xls")
@Sheet(sheetNames = {"经销商串货产品流向日统计表"}, numPerSheet = 50000, numOfSheet = {}, groupNumber = 1)
@LevelExcelTitle(titleNames = {"经销商串货产品流向日统计表"}, titleLevel = 1, colSpans = {14}, groupNumber = 1)
@Data
public class DealerFlowProductExportResp {

    /**
     * 终端有效扫码时间
     */
    @ExcelTitle(titleName = "终端有效扫码时间", index = 1, groupNumber = 1, width = 5000)
    private String scanTime;
    /**
     * 有效扫码终端编码
     */
    @ExcelTitle(titleName = "有效扫码终端编码", index = 2, groupNumber = 1, width = 5000)
    private String  diyCode;
    /**
     * 有效扫码终端名称
     */
    @ExcelTitle(titleName = "有效扫码终端名称", index = 3, groupNumber = 1, width = 5000)
    private String shopName;
    /**
     * 终端区域编码
     */
    @ExcelTitle(titleName = "终端区域编码", index = 4, groupNumber = 1, width = 5000)
    private  String shopAreaCode;

    /**
     * 终端区域编码
     */
    @ExcelTitle(titleName = "终端区域名称", index = 5, groupNumber = 1, width = 5000)
    private  String shopAreaName;


    @ExcelTitle(titleName = "产品编码", index = 6, groupNumber = 1, width = 5000)
    private String goodsCode;
    /**
     * goodsName
     */
    @ExcelTitle(titleName = "产品名称", index = 7, groupNumber = 1, width = 5000)
    private String goodsName;
    /**
     * 串货数量
     */
    @ExcelTitle(titleName = "串货数量", index = 8, groupNumber = 1, width = 5000)
    private Integer number;

    /**
     * 经销商编码
     */
    @ExcelTitle(titleName = "经销商编码", index = 9, groupNumber = 1, width = 5000)
    private String companyCode;
    /**
     * 经销商名称
     */
    @ExcelTitle(titleName = "经销商名称", index = 10, groupNumber = 1, width = 5000)
    private String companyName;

    /**
     * 经销商区域编码
     */
    @ExcelTitle(titleName = "经销商区域编码", index = 11, groupNumber = 1, width = 5000)
    private String dealerAreaCode;
    /**
     * 经销商业务区域
     */
    @ExcelTitle(titleName = "经销商业务区域", index =12, groupNumber = 1, width = 5000)
    private String dealerAreaName;



}
