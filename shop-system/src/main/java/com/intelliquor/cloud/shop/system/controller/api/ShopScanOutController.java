package com.intelliquor.cloud.shop.system.controller.api;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.CloudDealerOutBalanceDetailCodeModel;
import com.intelliquor.cloud.shop.common.model.ShopUserModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.req.OutBound;
import com.intelliquor.cloud.shop.common.model.resp.CloudDealerOutBalanceDetailResp;
import com.intelliquor.cloud.shop.common.model.resp.CloudDealerOutBalanceResp;
import com.intelliquor.cloud.shop.common.model.resp.QuashOrderDetailResp;
import com.intelliquor.cloud.shop.common.service.ICloudDealerOutBalanceDetailCodeService;
import com.intelliquor.cloud.shop.common.service.ICloudDealerOutBalanceService;
import com.intelliquor.cloud.shop.system.model.TerminalScanOutDetailModel;
import com.intelliquor.cloud.shop.system.model.req.TerminalScanReq;
import com.intelliquor.cloud.shop.system.model.resp.TerminalScanOutBalanceResp;
import com.intelliquor.cloud.shop.system.service.ShopScanOutService;
import com.intelliquor.cloud.shop.system.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 扫码出库
 */
@Slf4j
@RestController
@RequestMapping("/shopScanOut")
public class ShopScanOutController {


    @Autowired
    private UserContext userContext;

    @Autowired
    private ShopScanOutService shopScanOutService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ICloudDealerOutBalanceService cloudDealerOutBalanceService;

    @Autowired
    private ICloudDealerOutBalanceDetailCodeService cloudDealerOutBalanceDetailCodeService;

    /**
     * 合伙人出库和按订单发货出库
     *
     * @param detail
     * @return
     */
    @GetMapping(value = "/scanOut")
    public Response<TerminalScanOutDetailModel> scanOut(TerminalScanOutDetailModel detail) {
        if (detail == null || StringUtils.isBlank(detail.getQrcode())) {
            throw new BusinessException("码为空");
        }
        if (detail.getFromShopId() == null) {
            throw new BusinessException("出库终端为空");
        }
        if (detail.getLongitude() == null || detail.getLatitude() == null) {
            throw new BusinessException("请开启地理位置权限");
        }
        // 码未处理完之前不允许重复扫码
        String key = "SCAN_OUT:" + detail.getQrcode();
        boolean flag = redisUtil.setIfAbent(key, detail.getQrcode(), 10, TimeUnit.SECONDS);
        if (!flag) {
            throw new BusinessException("操作频繁");
        }

        ShopUserModel shopUserModel = userContext.getShopUserModel();
        log.info("扫码人信息：{}", JSONObject.toJSONString(shopUserModel));
        detail.setCompanyId(shopUserModel.getCompanyId());
        detail.setCreateUserId(shopUserModel.getId());
        detail.setCreateUserPhone(shopUserModel.getPhone());
        shopScanOutService.scanOut(detail);

        // 码处理完成，清除redis
        redisUtil.delete(key);

        return Response.ok(detail);
    }

    /**
     * 分销商合伙人出库生成发货单
     * 2023年6月15日的新需求，要求可以下游不下下单订货。上游直接发货，然后下游收货
     * 冲刺文档 https://zy-sk.feishu.cn/docx/GU2PdafuAoUffpxr64Aco4yJngg
     *
     * @param detail
     * @return
     */
    @GetMapping(value = "/outBound")
    public Response<OutBound> outBound(OutBound detail) {
        if (detail == null || StringUtils.isBlank(detail.getQrcode())) {
            throw new BusinessException("码为空");
        }
        if (detail.getFromShopId() == null) {
            throw new BusinessException("出库终端为空");
        }
        if (detail.getLongitude() == null || detail.getLatitude() == null) {
            throw new BusinessException("请开启地理位置权限");
        }
        // 码未处理完之前不允许重复扫码
        String key = "SCAN_OUT_BOUND:" + detail.getQrcode();
        boolean flag = redisUtil.setIfAbent(key, detail.getQrcode(), 10, TimeUnit.SECONDS);
        if (!flag) {
            throw new BusinessException("操作频繁");
        }

        ShopUserModel shopUserModel = userContext.getShopUserModel();
        log.info("扫码人信息：{}", JSONObject.toJSONString(shopUserModel));
        detail.setCompanyId(shopUserModel.getCompanyId());
        detail.setCreateUserId(shopUserModel.getId());
        detail.setCreateUserPhone(shopUserModel.getPhone());
        cloudDealerOutBalanceService.outBound(detail);

        // 码处理完成，清除redis
        redisUtil.delete(key);

        return Response.ok(detail);
    }

    /**
     * 获取自己出货的记录
     *
     * @param keyword
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @return
     */
    @GetMapping(value = "/outBoundLog")
    public PageResponse<List<CloudDealerOutBalanceResp>> outBoundLog(@RequestParam(value = "keyword", required = false) String keyword,
                                                                     @RequestParam(value = "startTime", required = false) String startTime,
                                                                     @RequestParam(value = "endTime", required = false) String endTime,
                                                                     @RequestParam(value = "receivingStatus", required = false) Integer receivingStatus,
                                                                     @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                     @RequestParam(value = "pageSize", defaultValue = "10") Integer limit) {
        Map<String, Object> search = new HashMap<>();
        search.put("keyword", keyword);
        if (!StrUtil.isBlank(startTime)) {
            search.put("startTime", startTime + " 00:00:00");
        }
        if (!StrUtil.isBlank(endTime)) {
            search.put("endTime", endTime + " 23:59:59");
        }
        search.put("receivingStatus", receivingStatus);
        search.put("fromDealerCode", userContext.getShopUserModel().getDealerCode());
        PageHelper.startPage(page, limit);
        List<CloudDealerOutBalanceResp> list = cloudDealerOutBalanceService.outBoundLog(search);
        PageInfo<CloudDealerOutBalanceResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    /**
     * 获取出库给自己的记录
     *
     * @param keyword
     * @param startTime
     * @param endTime
     * @param page
     * @param limit
     * @return
     */
    @GetMapping(value = "/toMeLog")
    public PageResponse<List<CloudDealerOutBalanceResp>> toMeLog(@RequestParam(value = "keyword", required = false) String keyword,
                                                                 @RequestParam(value = "startTime", required = false) String startTime,
                                                                 @RequestParam(value = "endTime", required = false) String endTime,
                                                                 @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                 @RequestParam(value = "pageSize", defaultValue = "10") Integer limit) {
        Map<String, Object> search = new HashMap<>();
        search.put("keyword", keyword);
        if (!StrUtil.isBlank(startTime)) {
            search.put("startTime", startTime + " 00:00:00");
        }
        if (!StrUtil.isBlank(endTime)) {
            search.put("endTime", endTime + " 23:59:59");
        }
        search.put("toDealerCode", userContext.getShopUserModel().getDealerCode());
        PageHelper.startPage(page, limit);
        List<CloudDealerOutBalanceResp> list = cloudDealerOutBalanceService.outBoundLog(search);
        PageInfo<CloudDealerOutBalanceResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    /**
     * 根据ID查询出货单详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/getOutBalanceDetailById")
    public Response<CloudDealerOutBalanceDetailResp> getCloudDealerOutBalanceDetailById(@RequestParam(value = "id") Long id) {
        CloudDealerOutBalanceDetailResp resp = cloudDealerOutBalanceService.getCloudDealerOutBalanceDetailById(id);
        return Response.ok(resp);
    }

    @GetMapping(value = "/outBoundCode")
    public Response<List<CloudDealerOutBalanceDetailCodeModel>> outBoundCode(@RequestParam(value = "balanceId", required = false) Integer balanceId) {
        List<CloudDealerOutBalanceDetailCodeModel> list = cloudDealerOutBalanceDetailCodeService.getCodeList(balanceId);
        return Response.ok(list);
    }


    /**
     * 出库记录
     *
     * @return
     */
    @GetMapping(value = "/getScanList")
    public PageResponse<List<TerminalScanOutBalanceResp>> getScanList(TerminalScanReq req) {
        if (req == null || req.getShopId() == null) {
            throw new BusinessException("参数不合法");
        }
        req.setCompanyId(userContext.getShopUserModel().getCompanyId());
        PageInfo<TerminalScanOutBalanceResp> pageInfo = shopScanOutService.getScanList(req);
        return PageResponse.ok(pageInfo);
    }


    /**
     * 查询订单发货记录
     *
     * @param orderCode 订单号
     * @return Response<List < TerminalScanOutDetailModel>>
     */
    @GetMapping(value = "/getOrderShipList")
    public Response<List<QuashOrderDetailResp>> getOrderShipList(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            throw new BusinessException("订单号不能为空");
        }
        List<QuashOrderDetailResp> list = shopScanOutService.getOrderShipList(orderCode);
        return Response.ok(list);
    }

    @GetMapping(value = "/cancelOrder")
    public Response<Void> cancelOrder(String orderCode) {
        cloudDealerOutBalanceService.cancelOrder(orderCode);
        return Response.ok();
    }
}
