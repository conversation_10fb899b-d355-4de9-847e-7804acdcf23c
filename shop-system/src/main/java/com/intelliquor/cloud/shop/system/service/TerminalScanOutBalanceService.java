package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.model.TerminalScanOutBalanceModel;
import com.intelliquor.cloud.shop.system.model.resp.TerminalScanOutBalanceWebResp;

import java.util.List;
import java.util.Map;

public interface TerminalScanOutBalanceService {

    /**
     * 查询数据
     *
     * @return
     */
    List<TerminalScanOutBalanceModel> selectList(Map<String, Object> searchMap);


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(TerminalScanOutBalanceModel model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(TerminalScanOutBalanceModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    TerminalScanOutBalanceModel getById(Integer id);

    /**
     * 查询数据
     *
     * @return
     */
    List<TerminalScanOutBalanceWebResp> selectList4Web(Map<String, Object> searchMap);
}
