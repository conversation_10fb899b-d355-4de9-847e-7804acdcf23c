package com.intelliquor.cloud.shop.system.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intelliquor.cloud.shop.system.dao.GoodsRegionPriceDao;
import com.intelliquor.cloud.shop.system.model.GoodsRegionPriceModel;
import java.util.Map;
import java.util.List;

/**
* 描述：区域供货价 服务实现层
* <AUTHOR>
* @date 2019-06-18
*/
@Service
public class GoodsRegionPriceService {

    @Autowired
    private GoodsRegionPriceDao tGoodsRegionPriceDao;

    /**
    * 查询数据
    *
    * @return
    */
    public List<GoodsRegionPriceModel> selectList(Map<String, Object> searchMap) {
        return tGoodsRegionPriceDao.selectList(searchMap);
    }


    /**
    * 新增数据
    *
    * @param model
    */
    public void insert(GoodsRegionPriceModel model) {
        tGoodsRegionPriceDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    public void update(GoodsRegionPriceModel model) {
        tGoodsRegionPriceDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    public void delete(Integer id) {
        GoodsRegionPriceModel model = new GoodsRegionPriceModel();
        model.setId(id);
        model.setIsDelete(1);
        tGoodsRegionPriceDao.update(model);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    public GoodsRegionPriceModel getById(Integer id) {
        return tGoodsRegionPriceDao.getById(id);
    }
}