package com.intelliquor.cloud.shop.system.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TaskActivityExhibitProtocolQuery {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected String fields;

    public TaskActivityExhibitProtocolQuery() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setFields(String fields) {
        this.fields=fields;
    }

    public String getFields() {
        return fields;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Integer value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Integer value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Integer value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Integer value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Integer> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Integer> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andDealerCodeIsNull() {
            addCriterion("dealer_code is null");
            return (Criteria) this;
        }

        public Criteria andDealerCodeIsNotNull() {
            addCriterion("dealer_code is not null");
            return (Criteria) this;
        }

        public Criteria andDealerCodeEqualTo(String value) {
            addCriterion("dealer_code =", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeNotEqualTo(String value) {
            addCriterion("dealer_code <>", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeGreaterThan(String value) {
            addCriterion("dealer_code >", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("dealer_code >=", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeLessThan(String value) {
            addCriterion("dealer_code <", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeLessThanOrEqualTo(String value) {
            addCriterion("dealer_code <=", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeLike(String value) {
            addCriterion("dealer_code like", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeNotLike(String value) {
            addCriterion("dealer_code not like", value, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeIn(List<String> values) {
            addCriterion("dealer_code in", values, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeNotIn(List<String> values) {
            addCriterion("dealer_code not in", values, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeBetween(String value1, String value2) {
            addCriterion("dealer_code between", value1, value2, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerCodeNotBetween(String value1, String value2) {
            addCriterion("dealer_code not between", value1, value2, "dealerCode");
            return (Criteria) this;
        }

        public Criteria andDealerNameIsNull() {
            addCriterion("dealer_name is null");
            return (Criteria) this;
        }

        public Criteria andDealerNameIsNotNull() {
            addCriterion("dealer_name is not null");
            return (Criteria) this;
        }

        public Criteria andDealerNameEqualTo(String value) {
            addCriterion("dealer_name =", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameNotEqualTo(String value) {
            addCriterion("dealer_name <>", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameGreaterThan(String value) {
            addCriterion("dealer_name >", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameGreaterThanOrEqualTo(String value) {
            addCriterion("dealer_name >=", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameLessThan(String value) {
            addCriterion("dealer_name <", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameLessThanOrEqualTo(String value) {
            addCriterion("dealer_name <=", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameLike(String value) {
            addCriterion("dealer_name like", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameNotLike(String value) {
            addCriterion("dealer_name not like", value, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameIn(List<String> values) {
            addCriterion("dealer_name in", values, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameNotIn(List<String> values) {
            addCriterion("dealer_name not in", values, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameBetween(String value1, String value2) {
            addCriterion("dealer_name between", value1, value2, "dealerName");
            return (Criteria) this;
        }

        public Criteria andDealerNameNotBetween(String value1, String value2) {
            addCriterion("dealer_name not between", value1, value2, "dealerName");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeIsNull() {
            addCriterion("terminal_code is null");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeIsNotNull() {
            addCriterion("terminal_code is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeEqualTo(String value) {
            addCriterion("terminal_code =", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeNotEqualTo(String value) {
            addCriterion("terminal_code <>", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeGreaterThan(String value) {
            addCriterion("terminal_code >", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("terminal_code >=", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeLessThan(String value) {
            addCriterion("terminal_code <", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeLessThanOrEqualTo(String value) {
            addCriterion("terminal_code <=", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeLike(String value) {
            addCriterion("terminal_code like", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeNotLike(String value) {
            addCriterion("terminal_code not like", value, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeIn(List<String> values) {
            addCriterion("terminal_code in", values, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeNotIn(List<String> values) {
            addCriterion("terminal_code not in", values, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeBetween(String value1, String value2) {
            addCriterion("terminal_code between", value1, value2, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalCodeNotBetween(String value1, String value2) {
            addCriterion("terminal_code not between", value1, value2, "terminalCode");
            return (Criteria) this;
        }

        public Criteria andTerminalNameIsNull() {
            addCriterion("terminal_name is null");
            return (Criteria) this;
        }

        public Criteria andTerminalNameIsNotNull() {
            addCriterion("terminal_name is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalNameEqualTo(String value) {
            addCriterion("terminal_name =", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameNotEqualTo(String value) {
            addCriterion("terminal_name <>", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameGreaterThan(String value) {
            addCriterion("terminal_name >", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameGreaterThanOrEqualTo(String value) {
            addCriterion("terminal_name >=", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameLessThan(String value) {
            addCriterion("terminal_name <", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameLessThanOrEqualTo(String value) {
            addCriterion("terminal_name <=", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameLike(String value) {
            addCriterion("terminal_name like", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameNotLike(String value) {
            addCriterion("terminal_name not like", value, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameIn(List<String> values) {
            addCriterion("terminal_name in", values, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameNotIn(List<String> values) {
            addCriterion("terminal_name not in", values, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameBetween(String value1, String value2) {
            addCriterion("terminal_name between", value1, value2, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalNameNotBetween(String value1, String value2) {
            addCriterion("terminal_name not between", value1, value2, "terminalName");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterIsNull() {
            addCriterion("terminal_master is null");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterIsNotNull() {
            addCriterion("terminal_master is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterEqualTo(String value) {
            addCriterion("terminal_master =", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterNotEqualTo(String value) {
            addCriterion("terminal_master <>", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterGreaterThan(String value) {
            addCriterion("terminal_master >", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterGreaterThanOrEqualTo(String value) {
            addCriterion("terminal_master >=", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterLessThan(String value) {
            addCriterion("terminal_master <", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterLessThanOrEqualTo(String value) {
            addCriterion("terminal_master <=", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterLike(String value) {
            addCriterion("terminal_master like", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterNotLike(String value) {
            addCriterion("terminal_master not like", value, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterIn(List<String> values) {
            addCriterion("terminal_master in", values, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterNotIn(List<String> values) {
            addCriterion("terminal_master not in", values, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterBetween(String value1, String value2) {
            addCriterion("terminal_master between", value1, value2, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalMasterNotBetween(String value1, String value2) {
            addCriterion("terminal_master not between", value1, value2, "terminalMaster");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneIsNull() {
            addCriterion("terminal_phone is null");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneIsNotNull() {
            addCriterion("terminal_phone is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneEqualTo(String value) {
            addCriterion("terminal_phone =", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneNotEqualTo(String value) {
            addCriterion("terminal_phone <>", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneGreaterThan(String value) {
            addCriterion("terminal_phone >", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("terminal_phone >=", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneLessThan(String value) {
            addCriterion("terminal_phone <", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneLessThanOrEqualTo(String value) {
            addCriterion("terminal_phone <=", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneLike(String value) {
            addCriterion("terminal_phone like", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneNotLike(String value) {
            addCriterion("terminal_phone not like", value, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneIn(List<String> values) {
            addCriterion("terminal_phone in", values, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneNotIn(List<String> values) {
            addCriterion("terminal_phone not in", values, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneBetween(String value1, String value2) {
            addCriterion("terminal_phone between", value1, value2, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalPhoneNotBetween(String value1, String value2) {
            addCriterion("terminal_phone not between", value1, value2, "terminalPhone");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressIsNull() {
            addCriterion("terminal_address is null");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressIsNotNull() {
            addCriterion("terminal_address is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressEqualTo(String value) {
            addCriterion("terminal_address =", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressNotEqualTo(String value) {
            addCriterion("terminal_address <>", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressGreaterThan(String value) {
            addCriterion("terminal_address >", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressGreaterThanOrEqualTo(String value) {
            addCriterion("terminal_address >=", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressLessThan(String value) {
            addCriterion("terminal_address <", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressLessThanOrEqualTo(String value) {
            addCriterion("terminal_address <=", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressLike(String value) {
            addCriterion("terminal_address like", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressNotLike(String value) {
            addCriterion("terminal_address not like", value, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressIn(List<String> values) {
            addCriterion("terminal_address in", values, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressNotIn(List<String> values) {
            addCriterion("terminal_address not in", values, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressBetween(String value1, String value2) {
            addCriterion("terminal_address between", value1, value2, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andTerminalAddressNotBetween(String value1, String value2) {
            addCriterion("terminal_address not between", value1, value2, "terminalAddress");
            return (Criteria) this;
        }

        public Criteria andDisplayIdIsNull() {
            addCriterion("display_id is null");
            return (Criteria) this;
        }

        public Criteria andDisplayIdIsNotNull() {
            addCriterion("display_id is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayIdEqualTo(Integer value) {
            addCriterion("display_id =", value, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdNotEqualTo(Integer value) {
            addCriterion("display_id <>", value, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdGreaterThan(Integer value) {
            addCriterion("display_id >", value, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("display_id >=", value, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdLessThan(Integer value) {
            addCriterion("display_id <", value, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdLessThanOrEqualTo(Integer value) {
            addCriterion("display_id <=", value, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdIn(List<Integer> values) {
            addCriterion("display_id in", values, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdNotIn(List<Integer> values) {
            addCriterion("display_id not in", values, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdBetween(Integer value1, Integer value2) {
            addCriterion("display_id between", value1, value2, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplayIdNotBetween(Integer value1, Integer value2) {
            addCriterion("display_id not between", value1, value2, "displayId");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsIsNull() {
            addCriterion("display_specs is null");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsIsNotNull() {
            addCriterion("display_specs is not null");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsEqualTo(String value) {
            addCriterion("display_specs =", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsNotEqualTo(String value) {
            addCriterion("display_specs <>", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsGreaterThan(String value) {
            addCriterion("display_specs >", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsGreaterThanOrEqualTo(String value) {
            addCriterion("display_specs >=", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsLessThan(String value) {
            addCriterion("display_specs <", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsLessThanOrEqualTo(String value) {
            addCriterion("display_specs <=", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsLike(String value) {
            addCriterion("display_specs like", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsNotLike(String value) {
            addCriterion("display_specs not like", value, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsIn(List<String> values) {
            addCriterion("display_specs in", values, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsNotIn(List<String> values) {
            addCriterion("display_specs not in", values, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsBetween(String value1, String value2) {
            addCriterion("display_specs between", value1, value2, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplaySpecsNotBetween(String value1, String value2) {
            addCriterion("display_specs not between", value1, value2, "displaySpecs");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardIsNull() {
            addCriterion("display_reward is null");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardIsNotNull() {
            addCriterion("display_reward is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardEqualTo(String value) {
            addCriterion("display_reward =", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardNotEqualTo(String value) {
            addCriterion("display_reward <>", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardGreaterThan(String value) {
            addCriterion("display_reward >", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardGreaterThanOrEqualTo(String value) {
            addCriterion("display_reward >=", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardLessThan(String value) {
            addCriterion("display_reward <", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardLessThanOrEqualTo(String value) {
            addCriterion("display_reward <=", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardLike(String value) {
            addCriterion("display_reward like", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardNotLike(String value) {
            addCriterion("display_reward not like", value, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardIn(List<String> values) {
            addCriterion("display_reward in", values, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardNotIn(List<String> values) {
            addCriterion("display_reward not in", values, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardBetween(String value1, String value2) {
            addCriterion("display_reward between", value1, value2, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayRewardNotBetween(String value1, String value2) {
            addCriterion("display_reward not between", value1, value2, "displayReward");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeIsNull() {
            addCriterion("display_begin_time is null");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeIsNotNull() {
            addCriterion("display_begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeEqualTo(Date value) {
            addCriterion("display_begin_time =", value, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeNotEqualTo(Date value) {
            addCriterion("display_begin_time <>", value, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeGreaterThan(Date value) {
            addCriterion("display_begin_time >", value, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("display_begin_time >=", value, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeLessThan(Date value) {
            addCriterion("display_begin_time <", value, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeLessThanOrEqualTo(Date value) {
            addCriterion("display_begin_time <=", value, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeIn(List<Date> values) {
            addCriterion("display_begin_time in", values, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeNotIn(List<Date> values) {
            addCriterion("display_begin_time not in", values, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeBetween(Date value1, Date value2) {
            addCriterion("display_begin_time between", value1, value2, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayBeginTimeNotBetween(Date value1, Date value2) {
            addCriterion("display_begin_time not between", value1, value2, "displayBeginTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeIsNull() {
            addCriterion("display_end_time is null");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeIsNotNull() {
            addCriterion("display_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeEqualTo(Date value) {
            addCriterion("display_end_time =", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeNotEqualTo(Date value) {
            addCriterion("display_end_time <>", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeGreaterThan(Date value) {
            addCriterion("display_end_time >", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("display_end_time >=", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeLessThan(Date value) {
            addCriterion("display_end_time <", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("display_end_time <=", value, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeIn(List<Date> values) {
            addCriterion("display_end_time in", values, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeNotIn(List<Date> values) {
            addCriterion("display_end_time not in", values, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeBetween(Date value1, Date value2) {
            addCriterion("display_end_time between", value1, value2, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("display_end_time not between", value1, value2, "displayEndTime");
            return (Criteria) this;
        }

        public Criteria andDisplayImageIsNull() {
            addCriterion("display_image is null");
            return (Criteria) this;
        }

        public Criteria andDisplayImageIsNotNull() {
            addCriterion("display_image is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayImageEqualTo(String value) {
            addCriterion("display_image =", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageNotEqualTo(String value) {
            addCriterion("display_image <>", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageGreaterThan(String value) {
            addCriterion("display_image >", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageGreaterThanOrEqualTo(String value) {
            addCriterion("display_image >=", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageLessThan(String value) {
            addCriterion("display_image <", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageLessThanOrEqualTo(String value) {
            addCriterion("display_image <=", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageLike(String value) {
            addCriterion("display_image like", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageNotLike(String value) {
            addCriterion("display_image not like", value, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageIn(List<String> values) {
            addCriterion("display_image in", values, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageNotIn(List<String> values) {
            addCriterion("display_image not in", values, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageBetween(String value1, String value2) {
            addCriterion("display_image between", value1, value2, "displayImage");
            return (Criteria) this;
        }

        public Criteria andDisplayImageNotBetween(String value1, String value2) {
            addCriterion("display_image not between", value1, value2, "displayImage");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNotNull() {
            addCriterion("remarks is not null");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualTo(String value) {
            addCriterion("remarks =", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualTo(String value) {
            addCriterion("remarks <>", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThan(String value) {
            addCriterion("remarks >", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualTo(String value) {
            addCriterion("remarks >=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThan(String value) {
            addCriterion("remarks <", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualTo(String value) {
            addCriterion("remarks <=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLike(String value) {
            addCriterion("remarks like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotLike(String value) {
            addCriterion("remarks not like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksIn(List<String> values) {
            addCriterion("remarks in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotIn(List<String> values) {
            addCriterion("remarks not in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksBetween(String value1, String value2) {
            addCriterion("remarks between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotBetween(String value1, String value2) {
            addCriterion("remarks not between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}