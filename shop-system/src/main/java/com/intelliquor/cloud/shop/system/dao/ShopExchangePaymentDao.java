package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.ShopExchangePaymentModel;
import com.intelliquor.cloud.shop.system.model.resp.ShopExchangePaymentResp;

import java.util.Map;
import java.util.List;
/**
* 描述：货款兑换记录 Dao接口
* <AUTHOR>
* @date 2021-04-21
*/
public interface ShopExchangePaymentDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<ShopExchangePaymentResp> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(ShopExchangePaymentModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(ShopExchangePaymentModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    ShopExchangePaymentModel getById(Integer id);

}