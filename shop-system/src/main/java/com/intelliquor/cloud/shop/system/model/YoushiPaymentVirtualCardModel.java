package com.intelliquor.cloud.shop.system.model;

import java.util.List;

public class YoushiPaymentVirtualCardModel {


    /**
     * cardInfo : [{"applyAvailVirtualLimit":"12800","applyVirtualLimit":"12800","effective":"1","status":"0","uid":"21784"}]
     * ret_msg : 交易成功
     * ret_code : 0000
     */

    private String ret_msg;
    private String ret_code;
    private List<CardInfoBean> cardInfo;

    public String getRet_msg() {
        return ret_msg;
    }

    public void setRet_msg(String ret_msg) {
        this.ret_msg = ret_msg;
    }

    public String getRet_code() {
        return ret_code;
    }

    public void setRet_code(String ret_code) {
        this.ret_code = ret_code;
    }

    public List<CardInfoBean> getCardInfo() {
        return cardInfo;
    }

    public void setCardInfo(List<CardInfoBean> cardInfo) {
        this.cardInfo = cardInfo;
    }

    public static class CardInfoBean {
        /**
         * applyAvailVirtualLimit : 12800
         * applyVirtualLimit : 12800
         * effective : 1
         * status : 0
         * uid : 21784
         */

        private String applyAvailVirtualLimit;
        private String applyVirtualLimit;
        private String effective;
        private String status;
        private String uid;

        public String getApplyAvailVirtualLimit() {
            return applyAvailVirtualLimit;
        }

        public void setApplyAvailVirtualLimit(String applyAvailVirtualLimit) {
            this.applyAvailVirtualLimit = applyAvailVirtualLimit;
        }

        public String getApplyVirtualLimit() {
            return applyVirtualLimit;
        }

        public void setApplyVirtualLimit(String applyVirtualLimit) {
            this.applyVirtualLimit = applyVirtualLimit;
        }

        public String getEffective() {
            return effective;
        }

        public void setEffective(String effective) {
            this.effective = effective;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }
    }
}
