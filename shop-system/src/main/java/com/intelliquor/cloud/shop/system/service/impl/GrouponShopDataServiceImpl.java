package com.intelliquor.cloud.shop.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.intelliquor.cloud.shop.common.dao.ShopDao;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.common.utils.LocationUtils;
import com.intelliquor.cloud.shop.system.dao.*;
import com.intelliquor.cloud.shop.system.model.*;
import com.intelliquor.cloud.shop.system.model.req.GrouponShopDataReq;
import com.intelliquor.cloud.shop.system.model.resp.GrouponOrderSimpleResp;
import com.intelliquor.cloud.shop.system.model.resp.GrouponShopDataResp;
import com.intelliquor.cloud.shop.system.service.GrouponShopDataService;
import com.intelliquor.cloud.shop.system.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GrouponShopDataServiceImpl implements GrouponShopDataService {

    @Autowired
    private GroupActivityServiceImpl groupActivityService;

    @Autowired
    private GroupActivityDao groupActivityDao;

    @Autowired
    private GrouponDataDao grouponDataDao;

    @Autowired
    private GrouponOrderDao grouponOrderDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private LadderGrouponOrderDao  ladderGrouponOrderDao;

    @Autowired
    private LadderGrouponDataDao ladderGrouponDataDao;

    @Autowired
    private LadderGrouponActivityDao ladderGrouponActivityDao;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<GrouponShopDataResp> getGrouponShopData(GrouponShopDataReq req) {
        if (StringUtils.isBlank(req.getGoodsCode())) {
            return new ArrayList<>();
        }
        //如果没有消费者位置信息，不处理
        if (req.getLat() == null || req.getLon() == null) {
            return new ArrayList<>();
        }

        //根据商品码获取当前拼团活动，在展示时间内且有效
        GroupActivityModel groupActivityModel = new GroupActivityModel();
        groupActivityModel.setGoodsCode(req.getGoodsCode());
        groupActivityModel.setCompanyId(req.getCompanyId());
        groupActivityModel.setCreateTime(new Date());
        List<GroupActivityModel> activityList= groupActivityDao.findListByGoodsCode(groupActivityModel);
        //如果没有符合活动，不处理
        if (activityList == null || activityList.size() == 0) {
            return new ArrayList<>();
        }

        //根据活动id查询当前已开团终端店id去重，
        List<GrouponShopDataResp> grouponShopDataRespList = new ArrayList<>();
        for (GroupActivityModel activityModel : activityList) {
            //查询当前活动已开团的终端id，去重
            GrouponDataQuery grouponDataQuery = new GrouponDataQuery();
            grouponDataQuery.createCriteria().andActicityIdEqualTo(activityModel.getId());
            grouponDataQuery.setDistinct(true);
            List<Integer> shopIdList = grouponDataDao.selectShopIdByExample(grouponDataQuery);
            //如果该活动没有开团的门店，跳过
            if (shopIdList == null || shopIdList.size() == 0) {
                continue;
            }
            //根据终端id和活动id查询当前最新拼团
            for (Integer shopId : shopIdList) {
                GrouponShopDataResp grouponShopDataResp = new GrouponShopDataResp();
                grouponShopDataResp.setShopId(shopId);
                grouponShopDataResp.setCompanyId(activityModel.getCompanyId());
                grouponShopDataResp.setActivityId(activityModel.getId());
                //查询当前活动当前终端店最新拼团数据
                GrouponDataQuery dataQuery = new GrouponDataQuery();
                dataQuery.createCriteria().andShopIdEqualTo(shopId)
                        .andActicityIdEqualTo(activityModel.getId())
                        .andCompanyIdEqualTo(activityModel.getCompanyId());
                dataQuery.setOrderByClause("id desc");
                List<GrouponData> grouponDataList = grouponDataDao.selectNewOneByExample(dataQuery);
                GrouponData grouponData = grouponDataList.get(0);

                long effectiveDuration = activityModel.getEffectiveDuration();  //活动开团有效时间  秒
                String effectiveDurationJson = JSON.toJSONString(groupActivityService.getTimes(effectiveDuration)); // 活动开团有效时间 日/时/分
                grouponShopDataResp.setEffectiveDuration(effectiveDuration);
                grouponShopDataResp.setEffectiveDurationJson(effectiveDurationJson);
                grouponShopDataResp.setShopImg(grouponData.getShopHeadImg());

                //查询该团下所有成员
                GrouponOrderQuery orderQuery = new GrouponOrderQuery();
                orderQuery.createCriteria().andShopIdEqualTo(shopId)
                        .andGrouponCodeEqualTo(grouponData.getGrouponCode())
                        .andCompanyIdEqualTo(activityModel.getCompanyId());
                orderQuery.setOrderByClause("pay_time asc");
                List<GrouponOrderSimpleResp> grouponOrderList = grouponOrderDao.selectSimpleOneByExample(orderQuery);
                Integer needNum = grouponData.getMemberNum();  //拼团成功缺少人数
                String grouponEndTime = "";
                if (grouponOrderList != null && grouponOrderList.size() > 0) {
                    //计算拼团结束时间
                    Date payTime = grouponOrderList.get(0).getPayTime();  //第一个进团的支付时间 毫秒
                    grouponEndTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(activityModel.getEffectiveDuration() * 1000 + payTime.getTime());
                    needNum = needNum - grouponOrderList.size();
                    /*if (needNum < 0) {
                        throw new BusinessException("拼团人数异常");
                    }*/
                }
                grouponShopDataResp.setMemberNum(grouponData.getMemberNum());
                grouponShopDataResp.setNeedNum(needNum);
                grouponShopDataResp.setGrouponEndTime(grouponEndTime);
                long count = grouponShopDataRespList.stream().filter(a -> a.getShopId().equals(shopId) ).count();
                if (count == 0) {
                    grouponShopDataRespList.add(grouponShopDataResp);
                }
            }
        }
        log.info("附近终端店:"+JSON.toJSONString(grouponShopDataRespList));

        //根据距离排序并取前5个
        if (grouponShopDataRespList != null && grouponShopDataRespList.size() > 0) {
            for (GrouponShopDataResp resp : grouponShopDataRespList) {
                ShopModel shopModel = shopDao.getById(resp.getShopId());
                double distance = LocationUtils.getDistance(req.getLat(), req.getLon(), shopModel.getLatitude(), shopModel.getLongitude());
                double f1 = new BigDecimal(distance).divide(new BigDecimal("1000")).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                resp.setDistance(f1);
                resp.setShopName(shopModel.getName());
            }
        }
        grouponShopDataRespList.sort(Comparator.comparing(GrouponShopDataResp::getDistance));
        if (grouponShopDataRespList.size() > 5) {
            grouponShopDataRespList = grouponShopDataRespList.subList(0,5);
        }
        log.info("附近终端店Final:"+JSON.toJSONString(grouponShopDataRespList));

        return grouponShopDataRespList;
    }


    /**
     * 查询当前门店下有哪些拼团活动
     * @param req
     * @return
     */
    @Override
    public List<GrouponShopDataResp> getGrouponDateByShopId(GrouponShopDataReq req) {
        Date now = new Date();
        ShopModel shopModel = shopDao.getById(req.getShopId());
        if (StringUtils.isBlank(shopModel.getProvince())) {
            return new ArrayList<>();
        }
        Map<String, Object> param = new HashMap<>();
        param.put("companyId", req.getCompanyId());
        param.put("showBeginTime", now);
        param.put("showEndTime", now);
        param.put("beginTime",now);
        param.put("endTime",now);
        param.put("shopId", req.getShopId());
        param.put("province", shopModel.getProvince());
        param.put("city", shopModel.getCity());
        param.put("district", shopModel.getDistrict());
        param.put("channel", shopModel.getChannelId());
        List<GroupActivityModel> activityModels = groupActivityDao.findListByShopId(param);
        if(!CollectionUtils.isEmpty(activityModels)&&req.getActivityId()!=null){
            activityModels=activityModels.stream().filter(a->!a.getId().equals(req.getActivityId())).collect(Collectors.toList());
        }else{
            activityModels = Lists.newArrayList();
        }
        List<GrouponShopDataResp> grouponShopDataRespList = new ArrayList<>();

        for(GroupActivityModel activityModel:activityModels){

            // 组装返回的数据
            GrouponShopDataResp grouponShopDataResp = new GrouponShopDataResp();
            grouponShopDataResp.setShopId(req.getShopId());
            grouponShopDataResp.setCompanyId(activityModel.getCompanyId());
            grouponShopDataResp.setActivityId(activityModel.getId());
            //查询当前活动当前终端店最新拼团数据
            GrouponDataQuery dataQuery = new GrouponDataQuery();
            dataQuery.createCriteria().andShopIdEqualTo(req.getShopId())
                    .andActicityIdEqualTo(activityModel.getId())
                    .andCompanyIdEqualTo(activityModel.getCompanyId());
            dataQuery.setOrderByClause("id desc");
            List<GrouponData> grouponDataList = grouponDataDao.selectNewOneByExample(dataQuery);
            if(CollectionUtils.isEmpty(grouponDataList)){
                continue ;
            }
            GrouponData grouponData = grouponDataList.get(0);

            long effectiveDuration = activityModel.getEffectiveDuration();  //活动开团有效时间  秒
            String effectiveDurationJson = JSON.toJSONString(groupActivityService.getTimes(effectiveDuration)); // 活动开团有效时间 日/时/分
            grouponShopDataResp.setEffectiveDuration(effectiveDuration);
            grouponShopDataResp.setEffectiveDurationJson(effectiveDurationJson);

            //查询该团下所有成员
            GrouponOrderQuery orderQuery = new GrouponOrderQuery();
            orderQuery.createCriteria().andShopIdEqualTo(req.getShopId())
                    .andGrouponCodeEqualTo(grouponData.getGrouponCode())
                    .andCompanyIdEqualTo(activityModel.getCompanyId());
            orderQuery.setOrderByClause("pay_time asc");
            List<GrouponOrderSimpleResp> grouponOrderList = grouponOrderDao.selectSimpleOneByExample(orderQuery);
            Integer needNum = grouponData.getMemberNum();  //拼团成功缺少人数
            if (grouponOrderList != null && grouponOrderList.size() > 0) {
                //计算拼团结束时间
                needNum = needNum - grouponOrderList.size();
            }
            if(activityModel.getSeckill()==1 ) {
                grouponShopDataResp.setType(2);
                //秒杀库存为0时，转为普通拼团库存
                Integer stock = (Integer) redisUtil.get("GroupActivity_seckill:" + activityModel.getId());
                if(stock == null || stock <= 0){
                    grouponShopDataResp.setType(1);
                }
            }else {
                grouponShopDataResp.setType(1);
            }
            grouponShopDataResp.setMemberNum(activityModel.getGroupBuyNum());
            grouponShopDataResp.setNeedNum(needNum);
            grouponShopDataResp.setGrouponEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(activityModel.getEndTime()));
            grouponShopDataResp.setGoodsCode(activityModel.getGoodsCode());
            grouponShopDataResp.setGoodsImage(activityModel.getMainImg());
            grouponShopDataResp.setGoodsName(activityModel.getGoodsName());
            // 添加拼团活动
            grouponShopDataRespList.add(grouponShopDataResp);
        }
        // 以下查询阶梯拼团的活动数据
        List<LadderGrouponActivityModel> ladderGrouponActivityModelList = ladderGrouponActivityDao.findListByShopId(param);
        if(!CollectionUtils.isEmpty(ladderGrouponActivityModelList)&&req.getActivityId()!=null){
            ladderGrouponActivityModelList=ladderGrouponActivityModelList.stream().filter(a->!a.getId().equals(req.getActivityId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(ladderGrouponActivityModelList)) {

            return grouponShopDataRespList;
        }
        // 循环遍历当前这个店下阶梯拼团的数据
        for(LadderGrouponActivityModel ladderGrouponActivityModel:ladderGrouponActivityModelList) {
            GrouponShopDataResp grouponShopDataResp = new GrouponShopDataResp();
            grouponShopDataResp.setShopId(req.getShopId());
            grouponShopDataResp.setCompanyId(ladderGrouponActivityModel.getCompanyId());
            grouponShopDataResp.setActivityId(ladderGrouponActivityModel.getId());
            //查询当前活动当前终端店最新拼团数据
            Map<String,Object> queryMap=new HashMap<>();
            queryMap.put("shopId",req.getShopId());
            queryMap.put("activityId",ladderGrouponActivityModel.getId());
            queryMap.put("companyId",ladderGrouponActivityModel.getCompanyId());
            queryMap.put("sortCode","id");
            queryMap.put("sortRole","desc");
            List<LadderGrouponDataModel> grouponDataList = ladderGrouponDataDao.selectList(queryMap);
            if(CollectionUtils.isEmpty(grouponDataList)){
                continue ;
            }
            LadderGrouponDataModel grouponData = grouponDataList.get(0);

            long effectiveDuration = ladderGrouponActivityModel.getEffectiveDuration();  //活动开团有效时间  秒
            String effectiveDurationJson = JSON.toJSONString(groupActivityService.getTimes(effectiveDuration)); // 活动开团有效时间 日/时/分
            grouponShopDataResp.setEffectiveDuration(effectiveDuration);
            grouponShopDataResp.setEffectiveDurationJson(effectiveDurationJson);

            //查询该团下所有成员
            Map<String,Object> orderMap =new HashMap<>();
            orderMap.put("shopId",req.getShopId());
            orderMap.put("grouponCode",grouponData.getGrouponCode());
            orderMap.put("companyId",ladderGrouponActivityModel.getCompanyId());
            orderMap.put("sortCode","pay_time");
            orderMap.put("sortRole","asc");

            Integer needNum = ladderGrouponActivityModel.getGroupBuyNum()==null?0:ladderGrouponActivityModel.getGroupBuyNum();  //拼团成功缺少人数
            grouponShopDataResp.setType(3);
            //已拼多少人
            grouponShopDataResp.setMemberNum(needNum);
            grouponShopDataResp.setNeedNum(0);
            grouponShopDataResp.setGrouponEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(ladderGrouponActivityModel.getEndTime()));
            grouponShopDataResp.setGoodsCode(ladderGrouponActivityModel.getGoodsCode());
            grouponShopDataResp.setGoodsImage(ladderGrouponActivityModel.getMainImg());
            grouponShopDataResp.setGoodsName(ladderGrouponActivityModel.getGoodsName());
            // 添加拼团活动
            grouponShopDataRespList.add(grouponShopDataResp);

        }

        return grouponShopDataRespList;
    }

}
