package com.intelliquor.cloud.shop.system.model.resp;

import lombok.Data;

/**
 * 云宴推介人界面结果参数类
 * <AUTHOR>
 * @date 2022-5-16
 */
@Data
public class CloudBanquetPromoterAppletResp {

    /**
     * 终端名称
     * */
    private String shopName;

    /**
     * 终端联系人
     * */
    private String linkPerson;

    /**
     * 终端联系手机号
     * */
    private String linkPhone;

    /**
     * 终端地址
     * */
    private String address;

    /**
     * 完成的宴席场次
     * */
    private Integer overBanquetNumber;

    /**
     * 场次排名
     * */
    private Integer banquetRanking;

    /**
     * 场次排名距离上一名的差距
     */
    private String banquetRankingString;

    /**
     * 活动简介
     * */
    private String activityText;
}
