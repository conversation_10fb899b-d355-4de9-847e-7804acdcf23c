package com.intelliquor.cloud.shop.system.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 描述：云仓商品管理实体类
 *
 * <AUTHOR>
 * @date 2020-03-16
 */
@Data
public class StorageGoodsModel {


    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "规格")
    private Integer standard;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "主图")
    private String mainImg;

    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

    @ApiModelProperty(value = "状态 1有效0无效")
    private Integer status;

    @ApiModelProperty(value = "0未删除1已删除")
    private Integer isDelete;

    @ApiModelProperty(value = "企业ID")
    private Integer companyId;

    @ApiModelProperty(value = "渠道ID")
    private String channelIds;

    /**
     * 门店范围
     */
    private String rangeJson;

    /**
     * 范围
     */
    private List<StorageRangeModel> rangeList;

    /**
     * 渠道
     */
    private List<Integer> channelList;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "id";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";

}