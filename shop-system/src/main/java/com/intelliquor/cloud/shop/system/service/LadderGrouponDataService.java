package com.intelliquor.cloud.shop.system.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.SnowFlake;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.system.dao.LadderGrouponActivityDao;
import com.intelliquor.cloud.shop.system.dao.LadderGrouponDataDao;
import com.intelliquor.cloud.shop.system.dao.LadderGrouponOrderDao;
import com.intelliquor.cloud.shop.system.model.LadderGrouponActivityModel;
import com.intelliquor.cloud.shop.system.model.LadderGrouponDataModel;
import com.intelliquor.cloud.shop.system.model.LadderGrouponOrderModel;
import com.intelliquor.cloud.shop.system.model.resp.LadderGrouponDataResp;
import com.intelliquor.cloud.shop.system.service.impl.LadderGroupOrderOpenServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
* 描述：开团信息数据 服务实现层
* <AUTHOR>
* @date 2020-08-27
*/
@Slf4j
@Service
public class LadderGrouponDataService{

    @Autowired
    private LadderGrouponDataDao ladderGrouponDataDao;

    @Autowired
    private LadderGrouponOrderDao ladderGrouponOrderDao;

    @Autowired
    private LadderGrouponActivityDao ladderGrouponActivityDao;

    @Autowired
    private LadderGrouponActivityService ladderGrouponActivityService;

    @Value("${complate-groupon-time}")
    private Integer complateTime;

    @Autowired
    LadderGroupOrderOpenServiceImpl ladderGroupOrderOpenService;

    /**
    * 查询数据
    *
    * @return
    */
    public List<LadderGrouponDataModel> selectList(Map<String, Object> searchMap) {
        return ladderGrouponDataDao.selectList(searchMap);
    }


    /**
     * 开团业务操作
     */
    public void  openGroup(LadderGrouponDataModel grouponData){
        grouponData.setGrouponCode(SnowFlake.getInstance().nextId());
        grouponData.setCreateTime(new Date());
        grouponData.setUpdateTime(new Date());
        ladderGrouponDataDao.insert(grouponData);
    }

    /**
    * 新增数据
    *
    * @param model
    */
    public void insert(LadderGrouponDataModel model) {
        ladderGrouponDataDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    public void update(LadderGrouponDataModel model) {
        ladderGrouponDataDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    public void delete(Integer id) {
        ladderGrouponDataDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    public LadderGrouponDataModel getById(Integer id) {
        return ladderGrouponDataDao.getById(id);
    }

    /**
     *
     * @param activityId
     * @param shopId
     * @param companyId
     * @param orderCode
     * @return
     */
    public LadderGrouponDataResp findOne(Integer activityId, Integer shopId, Integer companyId, String orderCode) {
        //查询拼团最新记录
        List<LadderGrouponDataModel> ladderGrouponDataModelList = new ArrayList<>();
        if (StringUtils.isNotBlank(orderCode)) {
            //根据订单里的团编号精确查找
            Map<String, Object> searchMap = Maps.newHashMap();
            searchMap.put("orderCode",orderCode);
            List<LadderGrouponOrderModel> grouponOrders = ladderGrouponOrderDao.selectList(searchMap);
            if (CollectionUtils.isEmpty(grouponOrders)) {
                throw new BusinessException("当前订单号异常");
            }
            if (StringUtils.isBlank(grouponOrders.get(0).getGrouponCode())) {
                throw new BusinessException("当前订单参团异常");
            }
            searchMap.clear();
            searchMap.put("grouponCode",grouponOrders.get(0).getGrouponCode());
            ladderGrouponDataModelList = ladderGrouponDataDao.selectListByGroupCode(searchMap);
        } else {
            //根据活动和终端店查询最新拼团
            Map<String, Object> searchMap = Maps.newHashMap();
            searchMap.put("shopId",shopId);
            searchMap.put("activityId",activityId);
            searchMap.put("companyId",companyId);
            searchMap.put("sortCode","id");
            searchMap.put("sortRole","desc");
            ladderGrouponDataModelList = ladderGrouponDataDao.selectList(searchMap);
        }
        if (CollectionUtils.isEmpty(ladderGrouponDataModelList)) {
            return null;
        }
        LadderGrouponDataResp resp = new LadderGrouponDataResp();
        BeanUtils.copyProperties(ladderGrouponDataModelList.get(0), resp);

        //查看当前活动
        LadderGrouponActivityModel activityModel = ladderGrouponActivityDao.getById(activityId);
        if (activityModel == null) {
            throw new BusinessException("当前活动不存在");
        }
        long effectiveDuration = activityModel.getEffectiveDuration();  //活动开团有效时间  秒
        String effectiveDurationJson = JSON.toJSONString(ladderGrouponActivityService.getTimes(effectiveDuration)); // 活动开团有效时间 日/时/分
        resp.setEffectiveDuration(effectiveDuration);
        resp.setEffectiveDurationJson(effectiveDurationJson);

        //查询该团下所有成员
        Map<String, Object> searchMap = Maps.newHashMap();
        searchMap.put("shopId",shopId);
        searchMap.put("grouponCode",resp.getGrouponCode());
        searchMap.put("companyId",companyId);
        searchMap.put("sortCode","pay_time");
        searchMap.put("sortRole","asc");
        List<LadderGrouponOrderModel> grouponOrderList = ladderGrouponOrderDao.selectList(searchMap);
        if (CollectionUtils.isNotEmpty(grouponOrderList)) {
            resp.setGrouponMemberList(grouponOrderList);
            Date payTime = grouponOrderList.get(0).getPayTime();  //第一个进团的支付时间 毫秒
            String grouponEndTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(effectiveDuration * 1000 + payTime.getTime());
            resp.setGrouponEndTime(grouponEndTime);
        }
        return resp;
    }

    /**
     * 处理成团下的订单
     */
    public void handGrouponData() {

        // 查询已完成的团
        List<LadderGrouponDataModel>  ladderGrouponDataModels=  ladderGrouponDataDao.selectCompleteData(complateTime,new Date());
        if(!CollectionUtils.isEmpty(ladderGrouponDataModels)) {

            for(LadderGrouponDataModel ladderGrouponDataModel:ladderGrouponDataModels) {
                log.info("activityId:"+ladderGrouponDataModel.getActivityId()+",grouponCode==="+ladderGrouponDataModel.getGrouponCode());
                ladderGroupOrderOpenService.handleGrouponOrder(ladderGrouponDataModel);

            }

        }

    }

    /**
     * 只处理成团信息
     * @param grouponDataModel
     */
    @Transactional(rollbackFor = Exception.class)
    public  void handleGrouponData(LadderGrouponDataModel grouponDataModel,LadderGrouponActivityModel findActivity){

        // 更新成团信息
        LadderGrouponDataModel updateGrouponData=new LadderGrouponDataModel();
        updateGrouponData.setIsComplete(1);
        updateGrouponData.setCompleteTime(new Date());
        updateGrouponData.setId(grouponDataModel.getId());
        ladderGrouponDataDao.update(updateGrouponData);

        // 如果活动时间还没有结束开新团
        //if(!TimeUtilis.dateCompareToDate(findActivity.getEndTime(), new Date())){
            // 开新团
            this.createNewGroupon(grouponDataModel.getCompanyId(), grouponDataModel.getShopId(), grouponDataModel.getActivityId());
      //  }
    }

    /**
     * 新增一条开团数据
     *
     * @param companyId  商户id
     * @param shopId     门店id
     * @param activityId 活动id
     */
    public String createNewGroupon(Integer companyId, Integer shopId, Integer activityId) {
        // 获取一条最新的数据 不关乎是否完成只为取终端头像、拼团限制人数
        Map<String,Object> searchMap=new HashMap<>();
        searchMap.put("activityId",activityId);
        searchMap.put("companyId",companyId);
        searchMap.put("shopId",shopId);
        searchMap.put("sortCode","id");
        searchMap.put("sortRole","Desc");
        List<LadderGrouponDataModel> grouponDataList=ladderGrouponDataDao.selectList(searchMap);

        // 构建新建拼团实体
        LadderGrouponDataModel grouponData = new LadderGrouponDataModel();
        String grouponCode = SnowFlake.getInstance().nextId(); // 生成团编号
        grouponData.setGrouponCode(grouponCode);
        grouponData.setActivityId(activityId);
        grouponData.setMemberNum(grouponDataList.get(0).getMemberNum());
        grouponData.setShopId(shopId);
        grouponData.setShopHeadImg(grouponDataList.get(0).getShopHeadImg());
        grouponData.setCompanyId(companyId);
        grouponData.setCreateTime(new Date());
        grouponData.setUpdateTime(new Date());
        ladderGrouponDataDao.insert(grouponData);
        return grouponCode;
    }

}