package com.intelliquor.cloud.shop.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.system.dao.CloudBanquetProductDao;
import com.intelliquor.cloud.shop.system.model.CloudBanquetProductModel;
import com.intelliquor.cloud.shop.system.service.CloudBanquetProductService;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 云宴商品服务层实现类
 * <AUTHOR>
 * @date 2022-4-27
 */
@Service
public class CloudBanquetProductServiceImpl implements CloudBanquetProductService {

    @Autowired
    private CloudBanquetProductDao cloudBanquetProductDao;

    /**
     * 添加云宴商品
     * @param cloudBanquetProductModel 云宴商品实体类参数
     * <AUTHOR>
     * @date 2022-4-27
     * @return 返回成功或者失败的标识
     */
    @Override
    public Response<String> insertCloudBanquetProduct(CloudBanquetProductModel cloudBanquetProductModel){
        //创建返回类
        Response<String> response = new Response<>();

        //判断必填参数是否为空
        if(StringUtils.isEmpty(cloudBanquetProductModel.getProductName()) || StringUtils.isEmpty(cloudBanquetProductModel.getProductCode()) ||
           StringUtils.isEmpty(cloudBanquetProductModel.getBottleCoverAward().toString()) || StringUtils.isEmpty(cloudBanquetProductModel.getProductStatus().toString()) ||
           null == cloudBanquetProductModel.getProductPrice()
        ){
            response.setCode(400);
            response.setMsg("必填参数错误或者为空");
            return response;
        }

        //判断productCode是否在数据库中存在 同一商品不可再次添加
        CloudBanquetProductModel judgeParam = cloudBanquetProductDao.selectCloudBanquetProductByCompanyIdAndProductCode(cloudBanquetProductModel.getCompanyId(),
                                                                                                                        cloudBanquetProductModel.getProductCode());

        //判断是否存在
        if(null != judgeParam && judgeParam.getIsDelete() == 0){
            response.setCode(400);
            response.setMsg("该产品已经添加过 请勿重复添加");
            return response;
            //判断是否被隐藏状态删除了
        }

        if(null != judgeParam && judgeParam.getIsDelete() == 1){
            //如果是被隐藏了 按照productCode把别的全部改变
            if(cloudBanquetProductDao.updateCloudBanquetProductByProductCode(cloudBanquetProductModel) < 1) {
                response.setCode(500);
                response.setMsg("添加失败");
                return response;
            }

            response.setCode(200);
            response.setMsg("成功");
            return response;
        }

        //不为空就去添加 小于1就是添加失败
        if(cloudBanquetProductDao.insertCloudBanquetProduct(cloudBanquetProductModel) < 1){
            response.setCode(500);
            response.setMsg("添加失败");
            return response;
        }

        //配置状态码和信息
        response.setCode(200);
        response.setMsg("执行成功");
        response.setResult("执行成功");
        //返回
        return response;
    }

    /**
     * 删除云宴商品
     * @param id 云宴商品表id
     * <AUTHOR>
     * @date 2022-4-27
     * @return 返回成功或者失败的标识
     */
    @Override
    public Response<String> deleteCloudBanquetProduct(Integer id){
        //创建返回类
        Response<String> response = new Response<>();

        //判断必填参数是否为空
        if(StringUtils.isEmpty(id.toString())){
            response.setCode(400);
            response.setMsg("必填参数错误或者为空");
            return response;
        }

        //参数不为空去执行删除 小于1就是失败
        if(cloudBanquetProductDao.deleteCloudBanquetProduct(id) < 1){
            response.setCode(500);
            response.setMsg("添加失败");
            return response;
        }

        //配置状态码和信息
        response.setCode(200);
        response.setMsg("执行成功");
        response.setResult("执行成功");
        //返回
        return response;
    }

    /**
     * @param cloudBanquetProductModel 云宴商品表id
     * <AUTHOR>
     * @date 2022-4-27
     * @return 返回成功或者失败的标识
     */
    @Override
    public Response<String> updateCloudBanquetProduct(@RequestBody CloudBanquetProductModel cloudBanquetProductModel){
        //创建返回类
        Response<String> response = new Response<>();

        //判断必填参数是否为空
        if(StringUtils.isEmpty(cloudBanquetProductModel.getId().toString())){
            response.setCode(400);
            response.setMsg("必填参数错误或者为空");
            return response;
        }

        //判断productCode是否在数据库中存在 同一商品不可再次添加
        CloudBanquetProductModel judgeParam = cloudBanquetProductDao.selectCloudBanquetProductByCompanyIdAndProductCode(cloudBanquetProductModel.getCompanyId(),
                                                                                                                        cloudBanquetProductModel.getProductCode());

        //判断是否存在 如果id相同说明本数据改本数据 不算重复添加
        if(null != judgeParam && !judgeParam.getId().equals(cloudBanquetProductModel.getId())){
            response.setCode(400);
            response.setMsg("该产品已经添加过 请勿重复添加");
            return response;
        }

        //参数不为空去执行删除 小于1就是失败
        if(cloudBanquetProductDao.updateCloudBanquetProduct(cloudBanquetProductModel) < 1){
            response.setCode(500);
            response.setMsg("添加失败");
            return response;
        }

        //配置状态码和信息
        response.setCode(200);
        response.setMsg("执行成功");
        response.setResult("执行成功");
        //返回
        return response;
    }

    /**
     * @param productName 云宴商品名称
     * @param productCode 云宴商品编码
     * @param productStatus 云宴商品状态
     * @param companyId 公司id
     * <AUTHOR>
     * @date 2022-4-27
     * @return 返回当前公司id下的云宴商品
     */
    @Override
    public PageResponse<PageInfo<CloudBanquetProductModel>> selectCloudBanquetProduct(String productName,
                                                                                      String productCode,
                                                                                      Integer productStatus,
                                                                                      Integer companyId,
                                                                                      Integer page,
                                                                                      Integer limit) {
        //创建返回类
        PageResponse<PageInfo<CloudBanquetProductModel>> response = new PageResponse<>();

        //判断必填参数是否为空
        if(StringUtils.isEmpty(companyId.toString())){
            response.setCode(400);
            response.setMsg("必填参数错误或者为空");
            return response;
        }

        //不为空创建参数集合
        HashMap<String,Object> paramMap = new HashMap<String,Object>(){{
            put("productName",productName);
            put("productCode",productCode);
            put("productStatus",productStatus);
            put("companyId",companyId);
        }};

        PageHelper.startPage(page,limit);

        //查询数据
        List<CloudBanquetProductModel> cloudBanquetProductModelList = cloudBanquetProductDao.selectCloudBanquetProduct(paramMap);

        //配置状态码和信息
        PageInfo<CloudBanquetProductModel> pageInfo = new PageInfo<>(ListUtils.emptyIfNull(cloudBanquetProductModelList));

        //返回
        return PageResponse.ok(pageInfo);
    }

    /**
     * @param productCode 云宴商品编码
     * <AUTHOR>
     * @date 2022-5-26
     * @return 返回当前公司id下的云宴商品
     */
    public CloudBanquetProductModel selectCloudBanquetProductByProductCode(String productCode){
        return cloudBanquetProductDao.selectCloudBanquetProductByProductCode(productCode);
    }

    /**
     * @param companyId 公司id
     * <AUTHOR>
     * @date 2022-5-20
     * @return 返回当前公司id下的云宴商品
     */
    public Response<List<CloudBanquetProductModel>> selectCloudBanquetProductByCompanyId(Integer companyId){
        //创建返回类
        Response<List<CloudBanquetProductModel>> response = new Response<>();

        //判断必填参数是否为空
        if(null == companyId){
            response.setCode(400);
            response.setMsg("必填参数错误或者为空");
            return response;
        }

        //查询数据
        List<CloudBanquetProductModel> cloudBanquetProductModelList = cloudBanquetProductDao.selectCloudBanquetProductByCompanyId(companyId);

        //返回
        response.setCode(200);
        response.setMsg("成功");
        response.setResult(cloudBanquetProductModelList);
        return response;
    }
}
