package com.intelliquor.cloud.shop.system.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.validation.groups.Insert;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 宴席活动订单 请求实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@Data
@EqualsAndHashCode()
public class CloudBanquetOrderSearchRes implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 终端店id
     */
    private Integer shopId;

    /**
     * 云宴订单状态
     */
    private Integer orderStatus;

    /**
     * 推介人唯一标识id
     * */
    private String promoterId;

    /**
     * 宴席创建人唯一id
     * */
    private String banquetUserId;

    /**
     * 云宴订单id
     */
    private Integer orderId;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 瓶数
     */
    private Integer productNumber;

    /**
     * 终端手机号
     */
    private String shopLinkPhone;

    /**
     * 消费者手机号
     */
    private String customerPhone;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "update_time";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";

}
