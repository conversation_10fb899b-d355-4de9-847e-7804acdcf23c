package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.SauceShopActivityPriceModel;
import java.util.Map;
import java.util.List;
/**
* 描述：商店商品默认价 Dao接口
* <AUTHOR>
* @date 2020-10-09
*/
public interface SauceShopActivityPriceDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<SauceShopActivityPriceModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(SauceShopActivityPriceModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(SauceShopActivityPriceModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    SauceShopActivityPriceModel getById(Integer id);


    /**
     * 根据ID查询
     *
     * @param
     * @return
     */
    SauceShopActivityPriceModel getByActivityIdAndShopId(SauceShopActivityPriceModel model);

}