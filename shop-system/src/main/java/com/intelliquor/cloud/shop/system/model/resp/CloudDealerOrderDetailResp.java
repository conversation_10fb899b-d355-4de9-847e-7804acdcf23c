package com.intelliquor.cloud.shop.system.model.resp;

import com.intelliquor.cloud.shop.system.model.CloudDealerOrderModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述：云商订单管理
 *
 * <AUTHOR>
 * @date 2021-01-20
 */
@Data
public class CloudDealerOrderDetailResp extends CloudDealerOrderModel {

    @ApiModelProperty(value = "饮酒联盟名称")
    private String shopName;

    @ApiModelProperty(value = "饮酒联盟手机号")
    private String shopPhone;

    @ApiModelProperty(value = "饮酒联盟地址")
    private String shopAddress;

    @ApiModelProperty(value = "业务区域")
    private String dealerAreaName;

}