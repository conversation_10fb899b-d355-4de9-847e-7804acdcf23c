package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.BuycardRechargeQuotaModel;
import java.util.Map;
import java.util.List;
/**
* 描述：进货卡充值额度 Dao接口
* <AUTHOR>
* @date 2019-10-09
*/
public interface BuycardRechargeQuotaDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<BuycardRechargeQuotaModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(BuycardRechargeQuotaModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(BuycardRechargeQuotaModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    BuycardRechargeQuotaModel getById(Integer id);

    /**
     * 取当前额度描述的个数（不包含自己）
     *
     * @param model
     * @return
     */
     int getByQuotaDescribeNum(BuycardRechargeQuotaModel model);

}