package com.intelliquor.cloud.shop.system.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * Created by Administrator on 2020/10/10 0010.
 */
@Excel(fileName = "景酱订单统计表.xls")
@Sheet(sheetNames = {"景酱订单统计表"}, numPerSheet = 50000, numOfSheet = {}, groupNumber = 1)
@LevelExcelTitle(titleNames = {"景酱订单统计表"}, titleLevel = 1, colSpans = {8}, groupNumber = 1)
@Data
public class ShopSauceOrderPageResp {

    @ApiModelProperty(value = "订单ID")
    private Integer id;

    @ApiModelProperty(value = "订单号")
    @ExcelTitle(titleName = "订单号", index = 1, groupNumber = 1, width = 5000)
    private String orderCode;

    @ApiModelProperty(value = "订单状态，目前均为已完成")
    private String orderStatus;

    @ApiModelProperty(value = "商品编码")
    @ExcelTitle(titleName = "商品编码", index = 4, groupNumber = 1, width = 5000)
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    @ExcelTitle(titleName = "商品名称", index = 2, groupNumber = 1, width = 5000)
    private String goodsName;

    @ApiModelProperty(value = "图片")
    private String goodsImage;

    @ApiModelProperty(value = "单价")
    private String goodsPrice;

    @ApiModelProperty(value = "数量")
    private String qty;

    @ApiModelProperty(value = "规格")
    private String standard;

    @ApiModelProperty(value = "订单金额")
    @ExcelTitle(titleName = "订单金额", index = 3, groupNumber = 1, width = 5000)
    private String orderAmount;

    @ApiModelProperty(value = "终端ID")
    private Integer shopId;

    @ApiModelProperty(value = "终端名称")
    @ExcelTitle(titleName = "终端名称", index = 5, groupNumber = 1, width = 5000)
    private String shopName;

    @ApiModelProperty(value = "终端电话")
    @ExcelTitle(titleName = "终端电话", index = 6, groupNumber = 1, width = 5000)
    private String linkphone;

    @ApiModelProperty(value = "终端地址")
    private String address;

    @ApiModelProperty(value = "消费者昵称")
    @ExcelTitle(titleName = "消费者昵称", index = 7, groupNumber = 1, width = 5000)
    private String nickName;

    @ApiModelProperty(value = "消费者联系方式")
    @ExcelTitle(titleName = "消费者联系方式", index = 8, groupNumber = 1, width = 5000)
    private String receivedMobile;

    @ApiModelProperty(value = "完成时间")
    @ExcelTitle(titleName = "完成时间", index = 9, format = "yyyy-MM-dd HH:mm:ss", groupNumber = 1, width = 5000)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String completeTime;

    @ApiModelProperty(value = "下单时间")
    @ExcelTitle(titleName = "下单时间", index = 10, format = "yyyy-MM-dd HH:mm:ss", groupNumber = 1, width = 5000)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @ApiModelProperty(value = "支付时间")
    @ExcelTitle(titleName = "支付时间", index = 11, format = "yyyy-MM-dd HH:mm:ss", groupNumber = 1, width = 5000)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String payTime;

    @ApiModelProperty(value = "订单收益：销售返利+推广分润")
    private BigDecimal earnings;
}
