package com.intelliquor.cloud.shop.system.model.gt;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020.01.21
 */
@Data
public class ShopOutStoreResp implements Serializable {

    private Integer id;
    @ApiModelProperty(value = "流水号")
    private String transaction;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "收货人")
    private String  receiveName;

    @ApiModelProperty(value = "收货人手机号")
    private String  receivePhone;

    @ApiModelProperty(value = "瓶数量")
    private Integer bottleNum;


    private List<ShopOutGoodsDetailResp> respList = new ArrayList<>();


}
