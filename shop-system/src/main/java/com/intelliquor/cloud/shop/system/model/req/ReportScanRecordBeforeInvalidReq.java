package com.intelliquor.cloud.shop.system.model.req;

import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020.08.19
 * 产品有效扫码前（后）无效扫码查询的入参
 */
@Data
public class ReportScanRecordBeforeInvalidReq {


    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 区域编码
     */
    private String orgCode;

    /**
     * 区域名称
     */
    private String orgName;

    /**
     * 终端编码
     */
    private String diyCode;

    /**
     * 终端编码
     */
    private String shopName;

    /**
     * 公司ID
     */
    private Integer companyId;


    /**
     * 下载中心实体
     */
    private OrderDownloadCenterModel downloadCenterModel;


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public OrderDownloadCenterModel getDownloadCenterModel() {
        return downloadCenterModel;
    }

    public void setDownloadCenterModel(OrderDownloadCenterModel downloadCenterModel) {
        this.downloadCenterModel = downloadCenterModel;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public String getDiyCode() {
        return diyCode;
    }

    public void setDiyCode(String diyCode) {
        this.diyCode = diyCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
