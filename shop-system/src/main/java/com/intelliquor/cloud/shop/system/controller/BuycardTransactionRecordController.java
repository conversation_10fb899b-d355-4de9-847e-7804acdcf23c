package com.intelliquor.cloud.shop.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.excel.ExcelUtil;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.AreaUtils;
import com.intelliquor.cloud.shop.common.utils.HttpUtils;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.AreaModel;
import com.intelliquor.cloud.shop.system.model.BuycardTransactionRecordModel;
import com.intelliquor.cloud.shop.system.model.DigitalMarketinPaging;
import com.intelliquor.cloud.shop.system.model.YoushiPaymentRecordModel;
import com.intelliquor.cloud.shop.system.model.req.BuycardShopPageReq;
import com.intelliquor.cloud.shop.system.model.req.BuycardTransactionRecordPageReq;
import com.intelliquor.cloud.shop.system.model.resp.BuycardShopPageResp;
import com.intelliquor.cloud.shop.system.model.resp.BuycardTransactionRecordPageResp;
import com.intelliquor.cloud.shop.system.service.BuycardTransactionRecordService;
import com.intelliquor.cloud.shop.system.util.Paging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 描述：进货卡交易记录控制层
 *
 * <AUTHOR>
 * @date 2019-10-09
 */
@Api(tags = {"提货卡交易记录操作接口"}, description = "提货卡交易记录操作接口")
@RestController
@RequestMapping("/buycardTransactionRecord")
public class BuycardTransactionRecordController {

    @Autowired
    private BuycardTransactionRecordService buycardTransactionRecordService;

    /**
     * 终端额度
     * @param page
     * @param limit
     * @param model
     * @param request
     * @return
     */
    @ApiOperation(value = "终端店提货卡，查询分页信息", notes = "终端店提货卡，查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/geShoptListByPage")
    public PageResponse<List<BuycardShopPageResp>> getShopListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                     @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                     BuycardShopPageReq model, HttpServletRequest request) {
        String authKey = request.getHeader("authKey");
        String sessionId = request.getHeader("sessionId");
        String areaList = AreaUtils.getArea(authKey,sessionId);
        List<BuycardShopPageResp> list;
        if(areaList == "admin"){
            PageHelper.startPage(page, limit);
            list = buycardTransactionRecordService.selectShopList(SearchUtil.getSearch(model));
        }else{
            PageHelper.startPage(page, limit);
            List<AreaModel> areaModelList = JSONArray.parseArray(areaList,AreaModel.class);
            model.setAreaModelList(areaModelList);
            list = buycardTransactionRecordService.selectShopListPermission(SearchUtil.getSearch(model));
        }

        PageInfo<BuycardShopPageResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "终端店提货卡导出", notes = "终端店提货卡导出", httpMethod = "GET")
    @RequestMapping(value = "/exportShop")
    public Response<String> exportShop(@RequestBody BuycardShopPageReq model) throws Exception {
        // 调异步方法前，需将RequestAttributes对象设置为子线程共享，否则为null
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);

        buycardTransactionRecordService.terminalPickupCardExportTask(model);
        return Response.ok("导出成功！");
    }

    /**
     * 终端额度流水
     * @param page
     * @param limit
     * @param model
     * @param request
     * @return
     */
    @ApiOperation(value = "提货卡交易记录，查询分页信息", notes = "提货卡交易记录，查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<BuycardTransactionRecordPageResp>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                              @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                              BuycardTransactionRecordPageReq model, HttpServletRequest request) {
        String authKey = request.getHeader("authKey");
        String sessionId = request.getHeader("sessionId");
        String areaList = "";
        if(StringUtils.isNotEmpty(authKey)&&StringUtils.isNotEmpty(sessionId)) {
            try {
                areaList =AreaUtils.getArea(authKey,sessionId);
            }catch (Exception e) {

            }
        }

        List<BuycardTransactionRecordPageResp> list;
        if(StringUtils.isEmpty(areaList)|| areaList.equals("admin")){
            PageHelper.startPage(page, limit);
            list = buycardTransactionRecordService.selectList(SearchUtil.getSearch(model));
        }else{
            List<AreaModel> areaModelList = JSONArray.parseArray(areaList,AreaModel.class);
            model.setAreaModelList(areaModelList);
            PageHelper.startPage(page, limit);
            list = buycardTransactionRecordService.selectListPermission(SearchUtil.getSearch(model));
        }
        PageInfo<BuycardTransactionRecordPageResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "提货卡交易记录导出", notes = "提货卡交易记录导出")
    @RequestMapping(value = "/export")
    public Response<String> export(@RequestBody BuycardTransactionRecordPageReq model) throws Exception {
        // 调异步方法前，需将RequestAttributes对象设置为子线程共享，否则为null
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);

        buycardTransactionRecordService.terminalPickupCardFlowExportTask(model);
        return Response.ok("导出成功！");
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<BuycardTransactionRecordPageResp>> getList(BuycardTransactionRecordModel model) {
        List<BuycardTransactionRecordPageResp> list = buycardTransactionRecordService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @ApiOperation(value = "景芝大屏-云仓保证金明细查询", notes = "景芝大屏-云仓保证金明细查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/selectYuncangDepositListByPage", method = RequestMethod.GET)
    public DigitalMarketinPaging<BuycardTransactionRecordPageResp> selectYuncangDepositList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                                            @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                                            @RequestParam(value = "areaName") String areaName,
                                                                                            @RequestParam(value = "companyId", required = true) int companyId) {
        Paging<BuycardTransactionRecordPageResp> paging = new Paging<>(page, limit);

        try {
            paging.putSearch("sortRole", "DESC");
            paging.putSearch("sortCode", "id");
            paging.putSearch("companyId", companyId);
            paging.putSearch("areaName", areaName);
            buycardTransactionRecordService.selectYuncangDepositList(paging);
            DigitalMarketinPaging<BuycardTransactionRecordPageResp> response = new DigitalMarketinPaging();
            response.setData(paging.getData());
            response.setTotal(paging.getTotal());
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(@RequestBody BuycardTransactionRecordModel model) {
        buycardTransactionRecordService.insert(model);
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息", httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<BuycardTransactionRecordModel> getById(@RequestParam(value = "id") Integer id, Integer companyId) {
        BuycardTransactionRecordModel model = buycardTransactionRecordService.getById(id, companyId);
        return Response.ok(model);
    }


    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Response<String> importExcel(@RequestParam("companyId") Integer companyId, @RequestParam("file") MultipartFile file) {
        try {
            if (null != file) {
                buycardTransactionRecordService.importExcel(companyId, file);
                return Response.ok("导入成功");
            } else {
                throw new BusinessException("参数对象不能为空");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(e.getMessage() == null ? "格式异常" : e.getMessage());
        }

    }



    @RequestMapping(value = "/importRevolvingFund", method = RequestMethod.POST)
    public Response<String> importRevolvingFund(@RequestParam("companyId") Integer companyId, @RequestParam("file") MultipartFile file) {
        try {
            if (null != file) {
                buycardTransactionRecordService.importRevolvingFund(companyId, file);
                return Response.ok("导入成功");
            } else {
                throw new BusinessException("参数对象不能为空");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(e.getMessage() == null ? "格式异常" : e.getMessage());
        }

    }



}
