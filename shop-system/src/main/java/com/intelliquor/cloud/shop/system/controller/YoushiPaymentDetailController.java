package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.intelliquor.cloud.shop.system.model.YoushiPaymentDetailModel;
import com.intelliquor.cloud.shop.system.service.YoushiPaymentDetailService;

import java.util.List;


/**
* 描述：优市订单明细控制层
* <AUTHOR>
* @date 2019-08-06
*/
@Api(tags = {"优市订单明细操作接口"}, description = "优市订单明细操作接口")
@RestController
@RequestMapping("/wechat/customerPaymentDetail")
public class YoushiPaymentDetailController {

    @Autowired
    private YoushiPaymentDetailService youshiPaymentDetailService;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<YoushiPaymentDetailModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                             @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                             YoushiPaymentDetailModel model) {

            PageHelper.startPage(page, limit);
            List<YoushiPaymentDetailModel> list = youshiPaymentDetailService.selectList(SearchUtil.getSearch(model));
            PageInfo<YoushiPaymentDetailModel> pageInfo = new PageInfo<>(list);
            return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表",httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<YoushiPaymentDetailModel>> getList(YoushiPaymentDetailModel model) {
            List<YoushiPaymentDetailModel> list = youshiPaymentDetailService.selectList(SearchUtil.getSearch(model));
            return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息",httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(YoushiPaymentDetailModel model) {
            youshiPaymentDetailService.insert(model);
            return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息",httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(YoushiPaymentDetailModel model) {
            youshiPaymentDetailService.update(model);
            return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息",httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
            youshiPaymentDetailService.delete(id);
            return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<YoushiPaymentDetailModel> getById(@RequestParam(value = "id") Integer id) {
            YoushiPaymentDetailModel model = youshiPaymentDetailService.getById(id);
            return Response.ok(model);
    }
}