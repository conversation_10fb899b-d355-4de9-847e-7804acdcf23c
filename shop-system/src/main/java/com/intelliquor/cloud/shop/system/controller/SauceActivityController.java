package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.GroupActivityModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.intelliquor.cloud.shop.system.model.SauceActivityModel;
import com.intelliquor.cloud.shop.system.service.SauceActivityService;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;


/**
* 描述：景酱活动控制层
* <AUTHOR>
* @date 2020-09-29
*/
@Api(tags = {"景酱活动操作接口"}, description = "景酱活动操作接口")
@RestController
@RequestMapping("/sauceActivity")
public class SauceActivityController {

    @Autowired
    private SauceActivityService sauceActivityService;

    @Autowired
    private UserContext userContext;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "name", value = "活动名称", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "status", value = "活动状态", required = false, paramType = "query", dataType = "int"),
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<SauceActivityModel>> getListByPage(@RequestParam(value = "page", required = true) int page,
                                                             @RequestParam(value = "limit", required = true) int limit,
                                                             SauceActivityModel model) {
        // 设置companyId为当前登录用户的companyId
        model.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        PageHelper.startPage(page, limit);
        List<SauceActivityModel> list = sauceActivityService.selectList(SearchUtil.getSearch(model));
        PageInfo<SauceActivityModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表",httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<SauceActivityModel>> getList(SauceActivityModel model) {
            List<SauceActivityModel> list = sauceActivityService.selectList(SearchUtil.getSearch(model));
            return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息",httpMethod = "POST")
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    public Response<String> save(@RequestBody SauceActivityModel model) {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);
        Long companyId = userContext.getUserInfo().getCompanyId();
        model.setCompanyId(Integer.parseInt(companyId.toString()));
        model.setCreateUser(userContext.getUserInfo().getUserId());

        try {
            sauceActivityService.insert(model);
            return Response.ok("保存成功");
        } catch (BusinessException e) {
            e.printStackTrace();
            return Response.fail(400, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Response.fail("保存失败！");
        }
    }

    @ApiOperation(value = "更新信息", notes = "更新信息",httpMethod = "POST")
    @RequestMapping(value = "/update",method = RequestMethod.POST)
    public Response<String> update(@RequestBody  SauceActivityModel model) {


        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        RequestContextHolder.setRequestAttributes(sra, true);
        Long companyId = userContext.getUserInfo().getCompanyId();
        model.setCompanyId(Integer.parseInt(companyId.toString()));
        model.setCreateUser(userContext.getUserInfo().getUserId());
        try {
            sauceActivityService.update(model);
            return Response.ok("修改成功");
        } catch (BusinessException e) {
            e.printStackTrace();
            return Response.fail(400, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Response.fail("修改失败！");
        }

    }

    @ApiOperation(value = "删除信息", notes = "删除信息",httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
            sauceActivityService.delete(id);
            return Response.ok("删除成功");
    }



    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<SauceActivityModel> getById(@RequestParam(value = "id") Integer id) {
            SauceActivityModel model = sauceActivityService.getById(id);
            return Response.ok(model);
    }

    /**
     * 修改活动状态
     *
     * @param model
     * @return
     */
    @RequestMapping("/updateStatus")
    public Response<String> updGroupStatus(@RequestBody SauceActivityModel model) {
        try {
            sauceActivityService.updateActivity(model);
            return Response.ok("修改成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Response.fail(e.getMessage());
        }
    }





}