package com.intelliquor.cloud.shop.system.service;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.dao.ShopDao;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.system.dao.SystemScanCodeSetDao;
import com.intelliquor.cloud.shop.system.factory.AnalysisCodeFactory;
import com.intelliquor.cloud.shop.system.factory.AnalysisCodeService;
import com.intelliquor.cloud.shop.system.model.CloudDealerOrderModel;
import com.intelliquor.cloud.shop.system.model.ShopScanRemoveInventoryBalanceModel;
import com.intelliquor.cloud.shop.system.model.ShopScanRemoveInventoryDetailModel;
import com.intelliquor.cloud.shop.system.model.SystemScanCodeSetModel;
import com.intelliquor.cloud.shop.system.model.req.DrinkClubOrderSendReq;
import com.intelliquor.cloud.shop.system.model.req.ShopScanRemoveInventoryReq;
import com.intelliquor.cloud.shop.system.util.constants.StatusConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: tianms
 * @Date: 2021/04/26 19:59
 * @Description: 扫码出库 - service
 */
@Slf4j
@Service("scanRemoveInventoryService")
public class ScanRemoveInventoryService {

    // 云众api地址
    @Value("${drink_club_manage_api.url}")
    private String drinkClubManageApiUrl;

    @Autowired
    private SystemScanCodeSetDao systemScanCodeSetDao;

    @Autowired
    private AnalysisCodeFactory analysisCodeFactory;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private ShopScanRemoveInventoryBalanceService shopScanRemoveInventoryBalanceService;

    @Autowired
    private ShopScanRemoveInventoryDetailService shopScanRemoveInventoryDetailService;

    @Autowired
    private CloudDealerOrderService cloudDealerOrderService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RestTemplate restTemplate;


    /**
     * 解析码
     *
     * @param shopScanRemoveInventoryReq
     * @return java.util.Map
     * @auther: tms
     * @date: 2021/04/27 09:02
     */
    @Transactional(rollbackFor = Exception.class)
    public ShopScanRemoveInventoryDetailModel analysisCode(ShopScanRemoveInventoryReq shopScanRemoveInventoryReq) throws Exception {

        // 解析码
        Map map = codeScanGetInfo(shopScanRemoveInventoryReq.getCompanyId(), shopScanRemoveInventoryReq.getCode(), shopScanRemoveInventoryReq.getShopId());

        if (ObjectUtil.isEmpty(map)) {
            throw new BusinessException("没有码数据");
        }

        // 存储扫码出库记录
        ShopScanRemoveInventoryDetailModel detailModel = this.saveScanRemoveInventory(map, shopScanRemoveInventoryReq);

        return detailModel;

    }

    /**
     * 扫码获取信息
     *
     * @param companyId
     * @param qrCode
     * @param shopId
     * @return java.util.Map
     * @auther: tms
     * @date: 2021/04/27 09:02
     */
    private Map codeScanGetInfo(Integer companyId, String qrCode, Integer shopId) throws Exception {
        // 根据商户配置信息获取解析码方式,然后根据解析方式获取解析实体类，调用解析方法获取码的具体信息
        SystemScanCodeSetModel scanCodeSetModel = systemScanCodeSetDao.getByCompanyId(companyId);
        if (scanCodeSetModel == null) {

            throw new BusinessException("解析码方式没有配置，请联系管理员配置");
        }
        log.info("codeScanGetInfo | 解析码方式====" + JSON.toJSONString(scanCodeSetModel));
        AnalysisCodeService analysisCodeService = AnalysisCodeFactory.getCompanyScan(scanCodeSetModel.getAnalysisCodeType());
        if (analysisCodeService == null) {
            throw new BusinessException("解析码方式不存在，请联系管理员查看");
        }
        // 解析码
        Map<String, String> map = analysisCodeService.generalAnalysis(qrCode, companyId, String.valueOf(shopId));

        log.info("codeScanGetInfo | 解析后的码的数据==" + JSON.toJSONString(map));

        // 判断码是否被出库使用过
        ShopScanRemoveInventoryDetailModel detailModel = shopScanRemoveInventoryDetailService.findByCode(qrCode, companyId);
        if (ObjectUtil.isNotEmpty(detailModel)) {
            throw new BusinessException("码已被出库使用");
        }

        return map;
    }

    /**
     * 保存扫码出库信息
     *
     * @param map
     * @param req
     * @return com.intelliquor.cloud.shop.system.model.ShopScanRemoveInventoryDetailModel
     * @auther: tms
     * @date: 2021/04/27 10:15
     */
    public ShopScanRemoveInventoryDetailModel saveScanRemoveInventory(Map<String, String> map, ShopScanRemoveInventoryReq req) {

        // 防止重复扫码key
        String key = "SHOP_SCAN_REMOVE_" + req.getShopId();

        // 扫码得到的信息
        // 码类型 1是箱码 2是盒码
        Integer codeType = Integer.valueOf(map.get("codeType"));
        // 有效数量 为1是盒码 大于1是箱码
        String number = map.get("number");
        // 有效码
        String codeIn = map.get("codeIn");
        //产品编码
        String goodsCode = map.get("WBProductNo");
        //产品名称
        String goodsName = map.get("ProductName");
        //出库时间
        Date outTime = TimeUtilis.dateFormat(map.get("OutTime"));
        //客户编码
        String wbCustomerNo = map.get("WBCustomerNo");
        //供应商名称
        String companyName = map.get("CustomerName");
        //批次信息
        String orderNo = map.get("orderNo");

        // 批次信息
        ShopScanRemoveInventoryBalanceModel balanceModel = null;

        // 第一次扫码，创建批次信息
        if (ObjectUtil.isEmpty(req.getBalanceId())) {
            if (!redisTemplate.opsForValue().setIfAbsent(key, req, 5, TimeUnit.SECONDS)) {
                throw new BusinessException("操作频繁");
            }

            // 获取出库订单信息
            JSONObject orderJson = cloudDealerOrderService.getAdviserOrderDetailed(req.getOrderNo(), req.getCompanyId());
            if (orderJson == null) {
                throw new BusinessException("未查询到订单");
            }

            // 获取终端店信息
            ShopModel shopModel = shopDao.getById(req.getShopId());

            balanceModel = new ShopScanRemoveInventoryBalanceModel();
            balanceModel.setCompanyId(req.getCompanyId());
            balanceModel.setCreateTime(new Date());
            balanceModel.setShopId(req.getShopId());
            balanceModel.setFromObjectId(String.valueOf(req.getShopId()));
            balanceModel.setOrderNo(req.getOrderNo());
            balanceModel.setTotalNum(orderJson.getInteger("qty"));
            balanceModel.setLongitude(shopModel.getLongitude());
            balanceModel.setLatitude(shopModel.getLatitude());
            shopScanRemoveInventoryBalanceService.insert(balanceModel);

            req.setBalanceId(balanceModel.getId());
        } else {
            if (!redisTemplate.opsForValue().setIfAbsent(key, req, 3, TimeUnit.SECONDS)) {
                throw new BusinessException("操作频繁");
            }

            balanceModel = shopScanRemoveInventoryBalanceService.findById(req.getBalanceId());
        }

        if (ObjectUtil.isEmpty(balanceModel)) {
            throw new BusinessException("扫码失败");
        }

        // 判断扫码总数是否超出订单出库总数
        Integer currNum = ObjectUtil.isEmpty(balanceModel.getRealNum()) ? 0 : balanceModel.getRealNum();
        Integer totalNum = balanceModel.getTotalNum();
        // 超过总数
        if (currNum + Integer.parseInt(number) > totalNum) {
            throw new BusinessException("扫码数量超过订单数量");
        }

        // 创建扫码记录详细信息
        ShopScanRemoveInventoryDetailModel detailModel = new ShopScanRemoveInventoryDetailModel();
        detailModel.setScanFinish(false);
        detailModel.setBalanceId(req.getBalanceId());
        detailModel.setCode(req.getCode());
        detailModel.setCodeIn(codeIn);
        detailModel.setCodeType(codeType);
        detailModel.setCompanyId(req.getCompanyId());
        detailModel.setCreateTime(new Date());
        detailModel.setCustomerName(companyName);
        detailModel.setGoodsCode(goodsCode);
        detailModel.setGoodsName(goodsName);
        detailModel.setCustomerNo(wbCustomerNo);
        detailModel.setOrderNo(orderNo);
        detailModel.setOutTime(outTime);
        detailModel.setNum(Integer.parseInt(number));
        shopScanRemoveInventoryDetailService.insert(detailModel);

        // 更新实际扫码数量
        shopScanRemoveInventoryBalanceService.updateRealNum(number, req.getBalanceId());

        // 扫码数量与订单商品数量一致
        if (currNum + Integer.parseInt(number) == totalNum) {
            detailModel.setScanFinish(true);

            CloudDealerOrderModel orderModel = new CloudDealerOrderModel();
            orderModel.setOrderCode(req.getOrderNo());
            orderModel.setCompanyId(req.getCompanyId());
            orderModel.setOrderStatus(StatusConstant.ORDER_STATUS.SENT);
            orderModel.setSendTime(new Date());
            // 更改订单状态为已发货
            cloudDealerOrderService.updateStatusByOrderCode(orderModel);

            // 更新云众订单状态为已发货
            this.yzOrderSend(orderModel);
        }

        redisTemplate.delete(key);

        return detailModel;
    }

    /**
     * 云众订单发货
     * @param orderModel
     */
    public void yzOrderSend(CloudDealerOrderModel orderModel) {
        ResponseEntity<String> responseEntity = null;
        HttpHeaders headers = this.getRequestHeaders(orderModel.getCompanyId());
        DrinkClubOrderSendReq req = new DrinkClubOrderSendReq();
        req.setOrderCode(orderModel.getOrderCode());
        req.setSendTime(orderModel.getSendTime());
        req.setCompanyId(orderModel.getCompanyId());
        HttpEntity<DrinkClubOrderSendReq> entity = new HttpEntity<>(req, headers);
        String sendUrl = drinkClubManageApiUrl + "/data-api/drinkClubData/send";
        responseEntity = restTemplate.postForEntity(sendUrl, entity, String.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            String body = responseEntity.getBody();
            log.info("云众订单发货同步成功: {}", body);
        } else {
            throw new BusinessException("云众订单发货同步失败");
        }
    }

    /**
     * @Title: getRequestHeaders
     * @Description: 获取请求头信息
     * @param @param companyId
     * @param @return
     * @return HttpHeaders
     * @throws
     */
    private HttpHeaders getRequestHeaders(Integer companyId) {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set("userId", String.valueOf(companyId));
        String base = companyId + "-" + TimeUtilis.getTimeStamp();
        requestHeaders.set("authLocal", Base64.encode(base.getBytes()));
        return requestHeaders;
    }

}
