package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.AppSettingModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 描述：小程序信息 Dao接口
 *
 * <AUTHOR>
 * @date 2019-11-20
 */
public interface AppSettingDao {


    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<AppSettingModel> selectList(Map<String, Object> searchMap);

    /**
     * 新增
     *
     * @param model
     * @return
     */
    Integer insert(AppSettingModel model);

    /**
     * 更新
     *
     * @param model
     * @return
     */
    Integer update(AppSettingModel model);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer delete(Integer id);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    AppSettingModel getById(Integer id);

    /**
     * 根据小程序名称查询
     *
     * @param name
     * @param companyId
     * @return
     */
    AppSettingModel getByName(@Param(value = "name") String name, @Param(value = "companyId") Integer companyId);

}