package com.intelliquor.cloud.shop.system.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 描述：进货订单实体类
 *
 * <AUTHOR>
 * @date 2019-10-09
 */
@Excel(fileName = "进货订单.xls")
@Sheet(sheetNames = {"进货订单"}, numPerSheet = 50000, numOfSheet = {}, groupNumber = 1)
@LevelExcelTitle(titleNames = {"进货订单"}, titleLevel = 1, colSpans = {14}, groupNumber = 1)
public class PurchaseOrderModel {


    @ApiModelProperty(value = "")
    private Integer id;

    @ExcelTitle(titleName = "订单号", index = 1, groupNumber = 1, width = 5000)
    @ApiModelProperty(value = "流水号")
    private String transaction;

    @ApiModelProperty(value = "终端id")
    private Integer shopId;

    @ApiModelProperty(value = "扫码次数")
    private Integer count;

    @ExcelTitle(titleName = "实付金额", index = 4, groupNumber = 1, width = 5000)
    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ExcelTitle(titleName = "支付时间", index = 6, groupNumber = 1, width = 5000)
    private String payTimeStr;

    @ApiModelProperty(value = "0未支付1已支付")
    private Integer status;

    @ExcelTitle(titleName = "状态", index = 7, groupNumber = 1, width = 5000)
    @ApiModelProperty(value = "0未支付1已支付")
    private String statusStr;

    @ApiModelProperty(value = "支付类型 1进货卡")
    private Integer payType;

    @ExcelTitle(titleName = "支付方式", index = 8, groupNumber = 1, width = 5000)
    private String payTypeStr;

    @ExcelTitle(titleName = "经销商", index = 9, groupNumber = 1, width = 5000)
    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "经销商编号")
    private String dealerNo;

    @ApiModelProperty(value = "balance Id")
    private Integer balanceId;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelTitle(titleName = "创建时间", index = 5, groupNumber = 1, width = 5000)
    private String createTimeStr;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String updateTimeStr;

    @ApiModelProperty(value = "")
    private String goodName;

    @ApiModelProperty(value = "")
    private String goodCode;

    private String goodShortName;

    private Integer companyId;

    private Integer orderActivityTypeId;
    private String orderActivityName;

    @ExcelTitle(titleName = "终端名称", index = 2, groupNumber = 1, width = 5000)
    private String shopName;

    @ExcelTitle(titleName = "联系电话", index = 3, groupNumber = 1, width = 5000)
    private String linkphone;

    private Integer bottleNum;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "id";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";

    /**
     * 下载中心实体
     */
    private OrderDownloadCenterModel downloadCenterModel;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTransaction() {
        return transaction;
    }

    public void setTransaction(String transaction) {
        this.transaction = transaction;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
        if (null != payTime) {
            this.payTimeStr = TimeUtilis.getStringDate("yyyy-MM-dd HH:mm:ss", payTime);
        }
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
        if (null != status) {
            if (0 == status) {
                this.statusStr = "未支付";
            }
            if (1 == status) {
                this.statusStr = "已支付";
            }
        }
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
        if (ObjectUtil.isNotEmpty(payType)) {
            if (1 == payType) {
                this.payTypeStr = "提货卡";
            } else if (2 == payType) {
                this.payTypeStr = "产品返利";
            } else if (3 == payType) {
                this.payTypeStr = "周转金";
            } else if (4 == payType) {
                this.payTypeStr = "预售卡";
            }

        }
    }

    public String getDealerName() {
        return dealerName;
    }

    public void setDealerName(String dealerName) {
        this.dealerName = dealerName;
    }

    public String getDealerNo() {
        return dealerNo;
    }

    public void setDealerNo(String dealerNo) {
        this.dealerNo = dealerNo;
    }

    public Integer getBalanceId() {
        return balanceId;
    }

    public void setBalanceId(Integer balanceId) {
        this.balanceId = balanceId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        if (null != createTime) {
            this.createTimeStr = TimeUtilis.getStringDate("yyyy-MM-dd HH:mm:ss", createTime);
        }
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        if (null != updateTime) {
            this.updateTimeStr = TimeUtilis.getStringDate("yyyy-MM-dd HH:mm:ss", updateTime);
        }
    }

    public String getGoodName() {
        return goodName;
    }

    public void setGoodName(String goodName) {
        this.goodName = goodName;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getSortCode() {
        return sortCode;
    }

    public void setSortCode(String sortCode) {
        this.sortCode = sortCode;
    }

    public String getSortRole() {
        return sortRole;
    }

    public void setSortRole(String sortRole) {
        this.sortRole = sortRole;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getPayTimeStr() {
        return payTimeStr;
    }

    public void setPayTimeStr(String payTimeStr) {
        this.payTimeStr = payTimeStr;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getUpdateTimeStr() {
        return updateTimeStr;
    }

    public void setUpdateTimeStr(String updateTimeStr) {
        this.updateTimeStr = updateTimeStr;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getLinkphone() {
        return linkphone;
    }

    public void setLinkphone(String linkphone) {
        this.linkphone = linkphone;
    }

    public String getPayTypeStr() {
        return payTypeStr;
    }

    public void setPayTypeStr(String payTypeStr) {
        this.payTypeStr = payTypeStr;
    }

    public Integer getBottleNum() {
        return bottleNum;
    }

    public void setBottleNum(Integer bottleNum) {
        this.bottleNum = bottleNum;
    }

    public String getGoodShortName() {
        return goodShortName;
    }

    public void setGoodShortName(String goodShortName) {
        this.goodShortName = goodShortName;
    }

    public OrderDownloadCenterModel getDownloadCenterModel() {
        return downloadCenterModel;
    }

    public void setDownloadCenterModel(OrderDownloadCenterModel downloadCenterModel) {
        this.downloadCenterModel = downloadCenterModel;
    }

    public Integer getOrderActivityTypeId() {
        return orderActivityTypeId;
    }

    public void setOrderActivityTypeId(Integer orderActivityTypeId) {
        this.orderActivityTypeId = orderActivityTypeId;
    }

    public String getOrderActivityName() {
        return orderActivityName;
    }

    public void setOrderActivityName(String orderActivityName) {
        this.orderActivityName = orderActivityName;
    }
}
