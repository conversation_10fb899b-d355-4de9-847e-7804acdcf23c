package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.SauceActivityModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.intelliquor.cloud.shop.system.model.SauceShopActivityPriceModel;
import com.intelliquor.cloud.shop.system.service.SauceShopActivityPriceService;

import java.util.List;


/**
* 描述：商店商品默认价控制层
* <AUTHOR>
* @date 2020-10-09
*/
@Api(tags = {"商店商品默认价操作接口"}, description = "商店商品默认价操作接口")
@RestController
@RequestMapping("/sauceShopActivityPrice")
public class SauceShopActivityPriceController {

    @Autowired
    private SauceShopActivityPriceService sauceShopActivityPriceService;



    @ApiOperation(value = "查询分页信息", notes = "查询分页信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<SauceShopActivityPriceModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                             @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                             SauceShopActivityPriceModel model) {

            PageHelper.startPage(page, limit);
            List<SauceShopActivityPriceModel> list = sauceShopActivityPriceService.selectList(SearchUtil.getSearch(model));
            PageInfo<SauceShopActivityPriceModel> pageInfo = new PageInfo<>(list);
            return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表",httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<SauceShopActivityPriceModel>> getList(SauceShopActivityPriceModel model) {
            List<SauceShopActivityPriceModel> list = sauceShopActivityPriceService.selectList(SearchUtil.getSearch(model));
            return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息",httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(SauceShopActivityPriceModel model) {
            sauceShopActivityPriceService.insert(model);
            return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息",httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(SauceShopActivityPriceModel model) {
            sauceShopActivityPriceService.update(model);
            return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息",httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
            sauceShopActivityPriceService.delete(id);
            return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<SauceShopActivityPriceModel> getById(@RequestParam(value = "id") Integer id) {
            SauceShopActivityPriceModel model = sauceShopActivityPriceService.getById(id);
            return Response.ok(model);
    }

    @RequestMapping(value = "/getActivityPriceList")
    public Response<List<SauceActivityModel>> getActivityPriceList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                       @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                   SauceShopActivityPriceModel model) {
        List<SauceActivityModel> list = sauceShopActivityPriceService.getActivityList(model);

        return Response.ok(list);
    }

    /**
     * 更新默认价格
     * @param
     * @return
     */
    @RequestMapping(value = "/updatePrice",method = RequestMethod.POST)
    public Response updatePrice(@RequestBody SauceShopActivityPriceModel model) {

        if(model==null|| StringUtils.isEmpty(model.getPriceJson())) {

            return Response.fail("缺少参数");
        }

        return sauceShopActivityPriceService.updatePrice(model);
    }





}