package com.intelliquor.cloud.shop.system.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaTemplateData;
//import cn.binarywang.wx.miniapp.bean.WxMaTemplateMessage;
//import cn.binarywang.wx.miniapp.config.WxMaInMemoryConfig;
import com.intelliquor.cloud.shop.common.utils.AirUtils;
import com.intelliquor.cloud.shop.system.model.WecharMessagePushModel;
import com.intelliquor.cloud.shop.system.model.WecharUserFormIdsModel;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信小程序模板推送实现
 * 微信已废弃该功能
 * <AUTHOR>
 * @date 2019/11/14 17:20
 * @desc
 */
@Deprecated
@Service
public class WecharPushTemplateService {

    /**
     * 小程序 APPID
     */
    @Value("${APP_ID_NEW}")
    private String appIdNew;

    /**
     * 小程序 AppSecret
     */
    @Value("${APP_SECRET_NEW}")
    private String appSecretNew;

    /**
     * 收益到账通知模板ID
     */
    @Value("${APP_INCOMETOACCOUNT_MESSAGE_TEMPLATEID}")
    private String incomeToAccountTemplateId;

    /**
     * 审核结果通知模板ID
     */
    @Value("${APP_CHECKRESULT_MESSAGE_TEMPLATEID}")
    private String checkResultTemplateId;

    @Autowired
    private WecharUserFormIdService userFormIdService;

    /**
     * 审核结果通知
     * @param pushModel
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String pushCheckResult(WecharMessagePushModel pushModel) {
        return "该方法已废弃";
//        //1、配置小程序信息
//        WxMaInMemoryConfig wxConfig = new WxMaInMemoryConfig();
//        wxConfig.setAppid(appIdNew); //小程序appid
//        wxConfig.setSecret(appSecretNew); //小程序AppSecret
//
//        WxMaService wxMaService = new WxMaServiceImpl();
//        wxMaService.setWxMaConfig(wxConfig);
//
//        //2、设置模版信息（keyword1：类型，keyword2：内容）
//        List<WxMaTemplateData> templateDataList = new ArrayList<>(5);
//        WxMaTemplateData data1 = new WxMaTemplateData("keyword1", pushModel.getCheckResult());
//        WxMaTemplateData data2 = new WxMaTemplateData("keyword2", pushModel.getRefusalReason());
//        WxMaTemplateData data3 = new WxMaTemplateData("keyword3", pushModel.getCheckName());
//        WxMaTemplateData data4 = new WxMaTemplateData("keyword4", pushModel.getMobile());
//        WxMaTemplateData data5 = new WxMaTemplateData("keyword5", pushModel.getCheckDate());
//        templateDataList.add(data1);
//        templateDataList.add(data2);
//        templateDataList.add(data3);
//        templateDataList.add(data4);
//        templateDataList.add(data5);
//
//        // 去记录表里查询最早的有效formId
//        WecharUserFormIdsModel userFormIdsModel = new WecharUserFormIdsModel();
//        userFormIdsModel.setOpenId(pushModel.getOpenId());
//        WecharUserFormIdsModel formIdsModel = userFormIdService.wecharUserFormIdList(userFormIdsModel);
//
//        if (AirUtils.hv(formIdsModel)) {
//            //3、设置推送消息
//            WxMaTemplateMessage templateMessage = WxMaTemplateMessage.builder()
//                    .toUser(pushModel.getOpenId())//要推送的用户openid
//                    .formId(formIdsModel.getFormId())//收集到的formid
//                    .templateId(checkResultTemplateId)//推送的模版id（在小程序后台设置）
//                    .data(templateDataList)//模版信息
//                    .page(pushModel.getPageUrl())//要跳转到小程序哪个页面
//                    .build();
//
//            //4、发起推送
//            try {
//                wxMaService.getMsgService().sendTemplateMsg(templateMessage);
//
//                // 推送成功后删除当前formId
//                userFormIdService.delWecharUserFormId(formIdsModel.getId());
//            } catch (WxErrorException e) {
//                System.out.println("推送失败：" + e.getMessage());
//                return e.getMessage();
//            }
//            return "推送成功";
//        } else {
//            return "推送失败，用户缺少formId";
//        }

    }

    /**
     * 收益到账通知
     * @param pushModel
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String pushIncomeToAccount(WecharMessagePushModel pushModel) {
//        //1、配置小程序信息
//        WxMaInMemoryConfig wxConfig = new WxMaInMemoryConfig();
//        wxConfig.setAppid(appIdNew); //小程序appid
//        wxConfig.setSecret(appSecretNew); //小程序AppSecret
//
//        WxMaService wxMaService = new WxMaServiceImpl();
//        wxMaService.setWxMaConfig(wxConfig);
//
//        //2、设置模版信息（keyword1：类型，keyword2：内容）
//        List<WxMaTemplateData> templateDataList = new ArrayList<>(6);
//        WxMaTemplateData data1 = new WxMaTemplateData("keyword1", pushModel.getSourcesOfIncome());
//        WxMaTemplateData data2 = new WxMaTemplateData("keyword2", pushModel.getIncomeType());
//        WxMaTemplateData data3 = new WxMaTemplateData("keyword3", pushModel.getIncomeAmount());
//        WxMaTemplateData data4 = new WxMaTemplateData("keyword4", pushModel.getRewardPoints());
//        WxMaTemplateData data5 = new WxMaTemplateData("keyword5", pushModel.getGoodsName());
//        WxMaTemplateData data6 = new WxMaTemplateData("keyword6", pushModel.getRevenueTime());
//        templateDataList.add(data1);
//        templateDataList.add(data2);
//        templateDataList.add(data3);
//        templateDataList.add(data4);
//        templateDataList.add(data5);
//        templateDataList.add(data6);
//
//        // 去记录表里查询最早的有效formId
//        WecharUserFormIdsModel userFormIdsModel = new WecharUserFormIdsModel();
//        userFormIdsModel.setOpenId(pushModel.getOpenId());
//        WecharUserFormIdsModel formIdsModel = userFormIdService.wecharUserFormIdList(userFormIdsModel);
//
//        if (AirUtils.hv(formIdsModel)) {
//            //3、设置推送消息
//            WxMaTemplateMessage templateMessage = WxMaTemplateMessage.builder()
//                    .toUser(pushModel.getOpenId())//要推送的用户openid
//                    .formId(formIdsModel.getFormId())//收集到的formid
//                    .templateId(incomeToAccountTemplateId)//推送的模版id（在小程序后台设置）
//                    .data(templateDataList)//模版信息
//                    .page(pushModel.getPageUrl())//要跳转到小程序哪个页面
//                    .build();
//
//            //4、发起推送
//            try {
//                wxMaService.getMsgService().sendTemplateMsg(templateMessage);
//
//                // 推送成功后删除当前formId
//                userFormIdService.delWecharUserFormId(formIdsModel.getId());
//            } catch (WxErrorException e) {
//                System.out.println("推送失败：" + e.getMessage());
//                return e.getMessage();
//            }
//            return "推送成功";
//        } else {
//            return "推送失败，用户缺少formId";
//        }
        return "该方法已废弃";
    }

}
