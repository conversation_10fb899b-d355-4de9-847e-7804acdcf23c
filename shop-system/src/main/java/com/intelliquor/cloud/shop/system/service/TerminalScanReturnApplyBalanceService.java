package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.model.TerminalScanReturnApplyBalanceModel;

import java.util.List;
import java.util.Map;

public interface TerminalScanReturnApplyBalanceService {
    /**
     * 查询数据
     *
     * @return
     */
    List<TerminalScanReturnApplyBalanceModel> selectList(Map<String, Object> searchMap);


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(TerminalScanReturnApplyBalanceModel model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(TerminalScanReturnApplyBalanceModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    TerminalScanReturnApplyBalanceModel getById(Integer id);
}
