package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.dao.ShopUserDao;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.ShopUserModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.AirUtils;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.LiveOrderCode;
import com.intelliquor.cloud.shop.system.model.req.LiveOrderDetailReq;
import com.intelliquor.cloud.shop.system.model.req.ScanLiveOrderReq;
import com.intelliquor.cloud.shop.system.model.resp.ShopLiveOrderPageResp;
import com.intelliquor.cloud.shop.system.service.GrouponOrderCodeService;
import com.intelliquor.cloud.shop.system.service.LiveOrderCodeService;
import com.intelliquor.cloud.shop.system.service.impl.GroupOrderOpenServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 *  直播订单扫码出库控制层
 * <AUTHOR>
 * @date 2020/4/20
 */
@Slf4j
@RestController
@RequestMapping("/liveOrderCode")
public class LiveOrderCodeController {

    @Autowired
    private LiveOrderCodeService liveOrderCodeService;

    @Autowired
    private ShopUserDao shopUserDao;

    @Autowired
    private GrouponOrderCodeService grouponOrderCodeService;

    @Autowired
    GroupOrderOpenServiceImpl groupOrderOpenServiceImpl;

    @Autowired
    private UserContext userContext;
    /**
     *
     * 扫码出库直播订单
     * @param scanLiveOrderReq 扫码请求参数
     * <AUTHOR>
     * @date 2020/4/24
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.util.List<java.util.Map<java.lang.String,java.lang.Object>>>
    */
    @PostMapping("/saveScanCode")
    public Response<List<Map<String, Object>>> saveScanCode(@RequestBody ScanLiveOrderReq scanLiveOrderReq){
        Response<List<Map<String, Object>>> response =  new Response<>();
        try {
            if (!AirUtils.hv(scanLiveOrderReq.getOrderCode())){
                response.setError("订单单号缺失");
                return response;
            }
            if (StringUtils.isBlank(scanLiveOrderReq.getScanCode())){
                response.setError("扫码信息缺失");
                return response;
            }

            //当前用户id
            Integer userId = userContext.getShopUserModel().getId();
            //当前用户登录的终端店id
            Integer shopId = userContext.getShopUserModel().getShopId();

            //查询用户信息
            ShopUserModel user = shopUserDao.loadById(userId,shopId);

            //查询订单信息
            LiveOrderDetailReq liveOrderDetailReq = new LiveOrderDetailReq();
            liveOrderDetailReq.setCompanyId(scanLiveOrderReq.getCompanyId());
            liveOrderDetailReq.setOrderCode(scanLiveOrderReq.getOrderCode());
            ShopLiveOrderPageResp order = liveOrderCodeService.getLiveOrderInfo(liveOrderDetailReq);
            if (!AirUtils.hv(order.getOrderCode())) {
                response.setError("订单不存在");
                return response;
            }
            String result = liveOrderCodeService.saveScanCode(order, scanLiveOrderReq.getScanCode(), user);
            if ("扫码成功".equals(result)) {
                response.setCode(200);
                response.setResult(liveOrderCodeService.selectScanCountByOrderCode(order));
            } else {
                response.setError(result);
                return response;
            }
        } catch (Exception e) {
            log.error("云众直播订单扫码信息保存异常！原因：{}", (Object) e.getStackTrace());
            e.printStackTrace();
            response.setError("保存失败");
        }
        return response;
    }

    /**
     *
     * 完成扫码信息
     * @param liveOrderDetailReq 参数
     * <AUTHOR>
     * @date 2020/4/22
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.lang.String>
    */
    @PostMapping("/completeScan")
    public Response<String> completeScan(@RequestBody LiveOrderDetailReq liveOrderDetailReq) {
        Response<String> response = new Response<>();
        try {
            ShopLiveOrderPageResp order = liveOrderCodeService.getLiveOrderInfo(liveOrderDetailReq);
            String result = liveOrderCodeService.completeScan(order);
            if (!"扫码完成".equals(result)) {
                response.setError(result);
            } else {
                response.setCode(200);
                response.setResult(result);
            }
        } catch (Exception e) {
            log.error("重置扫码信息异常！原因：{}", (Object) e.getStackTrace());
            e.printStackTrace();
            response.setError("保存失败");
        }
        return response;
    }

    /**
     *
     * 重置直播订单扫码出库
     * @param liveOrderDetailReq 参数信息
     * <AUTHOR>
     * @date 2020/4/26
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.lang.String>
    */
    @PostMapping("/resetScanInfo")
    public Response<String> resetScanInfo(@RequestBody LiveOrderDetailReq liveOrderDetailReq) {
        Response<String> response = new Response<>();
        try {
            if (StringUtils.isBlank(liveOrderDetailReq.getOrderCode())){
                response.setError("缺少订单编码参数");
            }
            grouponOrderCodeService.deleteByOrderCode(liveOrderDetailReq.getOrderCode());
            response.setCode(200);
            response.setResult("重置成功");
        } catch (Exception e) {
            log.error("重置扫码信息异常！原因：{}", e.getStackTrace());
            e.printStackTrace();
            response.setError("重置失败");
        }
        return response;
    }

    /**
     *
     * 订单扫码信息查询
     * @param orderCode 订单编号
     * <AUTHOR>
     * @date 2020/4/26
     * @return com.intelliquor.cloud.shop.common.entity.Response<java.util.List<java.util.Map<java.lang.String,java.lang.Object>>>
    */
    @GetMapping("/getOrderScanInfo")
    public Response<List<Map<String, Object>>> getOrderScanInfo(@RequestParam String orderCode,
                                                                @RequestParam Integer companyId) {
        Response<List<Map<String, Object>>> response = new Response<>();
        try {
            if (StringUtils.isBlank(orderCode)) {
                response.setError("参数缺失");
                return response;
            }
            //查询订单信息
            LiveOrderDetailReq liveOrderDetailReq = new LiveOrderDetailReq();
            liveOrderDetailReq.setCompanyId(companyId);
            liveOrderDetailReq.setOrderCode(orderCode);
            ShopLiveOrderPageResp order = liveOrderCodeService.getLiveOrderInfo(liveOrderDetailReq);
            if (!AirUtils.hv(order)) {
                response.setError("订单不存在");
                return response;
            }
            response.setCode(200);
            response.setResult(liveOrderCodeService.selectScanCountByOrderCode(order));
        } catch (Exception e) {
            log.error("订单扫码信息查询异常！原因：{}", e.getStackTrace());
            e.printStackTrace();
            response.setError("保存失败");
        }
        return response;
    }

}
