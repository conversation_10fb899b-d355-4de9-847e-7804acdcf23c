package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.model.req.TerminalAccountChangeDetailReq;
import com.intelliquor.cloud.shop.system.model.req.TerminalAccountChangeRewardRecordReq;
import com.intelliquor.cloud.shop.system.model.resp.TerminalAccountChangeDetailResp;
import com.intelliquor.cloud.shop.common.model.resp.TerminalAccountChangeRewardRecordResp;

import java.io.IOException;
import java.util.List;

public interface TerminalAccountChangeDetailService {

    List<TerminalAccountChangeDetailResp> selectMemberShopAccountChangeDetailList(Integer page, Integer limit, TerminalAccountChangeDetailReq terminalAccountChangeDetailReq);

    List<TerminalAccountChangeRewardRecordResp> selectTerminalRewardRecordListByShopId(Integer page, Integer limit, TerminalAccountChangeRewardRecordReq terminalAccountChangeDetailReq);

    void exportMemberShopAccountChangeDetailList(TerminalAccountChangeDetailReq terminalAccountChangeDetailReq) throws IOException;

    void exportTerminalRewardRecordListByShopId(TerminalAccountChangeRewardRecordReq terminalAccountChangeRewardRecordReq) throws IOException;

}
