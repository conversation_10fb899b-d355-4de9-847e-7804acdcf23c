package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.req.TerminalDealerInfoModelReq;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public interface TerminalDealerShopDao {


    TerminalDealerInfoModelReq selectDealerIdByDealerCode(String dealerCode);

    /**
     * 获取绑定关系数量
     * @param param
     * @return
     */
    Integer getRelationNum(Map<String,Object> param);

    TerminalDealerInfoModelReq selectDealerInfoById(Integer id);
}
