package com.intelliquor.cloud.shop.system.model.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020.12.07
 * 业务区域排名
 */
@Data
public class BusinessAchievementData implements Serializable {

    /**
     * 区域编码
     */
    private String orgCode;

    /**
     * 区域名称
     */
    private String orgName;

    /**
     * 数量 (终端注册数，或者酒的瓶数)
     */
    private Integer num;

    /**
     * 开瓶率
     */
    private Double openRate;



}
