package com.intelliquor.cloud.shop.system.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 描述：终端购买预售卡订单表实体类
* <AUTHOR>
* @date 2020-10-26
*/
@Data
public class PreSaleBuyOrderModel implements Serializable {


    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "订单号")
    private String orderCode;

    @ApiModelProperty(value = "终端id")
    private Integer shopId;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty(value = "0未支付1已支付2已取消")
    private Integer status;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "经销商编号")
    private String dealerCode;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "是否删除：0 否  1是")
    private Integer isDelete;

    @ApiModelProperty(value = "商户Id")
    private Integer companyId;

    /**
    * 排序字段默认为id
    */
    private String sortCode = "id";

    /**
    * 排序规则默认为降序排列(DESC/ASC)
    */
    private String sortRole = "DESC";

    /**
     * 商店名称
     */
    private String shopName;

    /**
     * 地址
     */
    private String address;

    /**
     * 下载中心实体
     */
    private OrderDownloadCenterModel downloadCenterModel;

    private String transaction;
    private String linkphone;

    /**
     * 查询条件 手机号或者终端名称
     */
    private String searchCondition;


}