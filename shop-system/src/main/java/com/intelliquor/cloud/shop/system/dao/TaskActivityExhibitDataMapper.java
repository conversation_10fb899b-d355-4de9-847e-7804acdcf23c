package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.TaskActivityExhibitData;
import com.intelliquor.cloud.shop.system.model.TaskActivityExhibitDataExample;
import java.util.List;

import com.intelliquor.cloud.shop.system.model.resp.TaskActivityExhibitAppData;
import org.apache.ibatis.annotations.Param;

public interface TaskActivityExhibitDataMapper {
    int countByExample(TaskActivityExhibitDataExample example);

    int deleteByExample(TaskActivityExhibitDataExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TaskActivityExhibitData record);

    int insertSelective(TaskActivityExhibitData record);

    List<TaskActivityExhibitAppData> selectByDate(@Param("terminalCode") String terminalCode, @Param("protocolId") Integer protocolId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    List<TaskActivityExhibitData> selectByExample(TaskActivityExhibitDataExample example);

    TaskActivityExhibitData selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskActivityExhibitData record, @Param("example") TaskActivityExhibitDataExample example);

    int updateByExample(@Param("record") TaskActivityExhibitData record, @Param("example") TaskActivityExhibitDataExample example);

    int updateByPrimaryKeySelective(TaskActivityExhibitData record);

    int updateByPrimaryKey(TaskActivityExhibitData record);

    List<TaskActivityExhibitData> findActivityExhibitData(TaskActivityExhibitData exhibitData);

}
