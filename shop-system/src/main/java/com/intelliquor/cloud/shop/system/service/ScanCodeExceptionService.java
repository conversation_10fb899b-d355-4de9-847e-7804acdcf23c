package com.intelliquor.cloud.shop.system.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.FileItem;
import com.intelliquor.cloud.shop.common.enums.OrderTypeEnum;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.common.utils.AirUtils;
import com.intelliquor.cloud.shop.common.utils.ExcelTools;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.system.dao.DealerInfoDao;
import com.intelliquor.cloud.shop.system.dao.ScanCodeExceptionDao;
import com.intelliquor.cloud.shop.system.model.*;
import com.intelliquor.cloud.shop.system.model.req.DealerFlowProductExportReq;
import com.intelliquor.cloud.shop.system.model.resp.DealerFlowProductExportResp;
import com.intelliquor.cloud.shop.system.model.resp.DealerFlowProductResp;
import com.intelliquor.cloud.shop.system.model.resp.RewardScanCodeExportResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 描述：扫码异常数据 服务实现层
 *
 * <AUTHOR>
 * @date 2019-12-10
 */
@Slf4j
@Service
public class ScanCodeExceptionService {

    @Autowired
    private ScanCodeExceptionDao scanCodeExceptionDao;

    @Autowired
    private DealerInfoDao dealerInfoDao;

    @Autowired
    private OrderDownloadCenterService downloadCenterService;


    /**
     * 查询数据
     *
     * @return
     */
    public List<ScanCodeException> selectList(Map<String, Object> searchMap) {
        return scanCodeExceptionDao.selectList(searchMap);
    }


    /**
     * 异常原因查询
     *
     * @return
     */
    public List<ScanCodeExceptionCause> selectExceptionCause() {
        return scanCodeExceptionDao.selectExceptionCauseList();
    }

    /**
     * 新增数据
     *
     * @param model
     */
    public void insert(ScanCodeException model) {
        scanCodeExceptionDao.insert(model);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    public void update(ScanCodeException model) {
        scanCodeExceptionDao.update(model);
    }

    /**
     * 删除数据
     *
     * @param id
     */
    public void delete(Integer id) {
        scanCodeExceptionDao.delete(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public ScanCodeException getById(Integer id) {
        return scanCodeExceptionDao.getById(id);
    }

    /**
     * 判断是否异常
     */

    public boolean isAreaException(String addressSmall, String addressBig) {
        if (addressSmall.contains(addressBig)) {
            return false;
        }
        return true;
    }


    public ScanCodeExceptionResult judgeDealerSalesAreaException(String code, String goodsCode, String goodsName, String dealerName, Integer shopId, Integer scanCodeUserId, ShopModel shopModel, String province, String city, String district, String street, String address,Integer companyId) {
        List<DealerInfoModel> dealers = dealerInfoDao.getByName(dealerName);
        if (dealers != null && dealers.size() > 0) {
            DealerInfoModel dealerInfoModel = dealers.get(0);
            List<DealerAreaModel> list = dealerInfoModel.getList();
            String scanCodeArea = province + city + district + street;
            boolean isInThisArea = false;
            for (DealerAreaModel areaModel : list) {
                String area = areaModel.getProvince() + areaModel.getCity() + areaModel.getDistrict() + areaModel.getStreet();
                log.info("scanCpdeArea==="+scanCodeArea+"area===="+area+",flag==="+isAreaException(scanCodeArea, area));
                if (isAreaException(scanCodeArea, area)) {

                } else {
                    isInThisArea = true;
                    break;
                }
            }
            if (!isInThisArea) {
                ScanCodeException scanCodeException = new ScanCodeException();
                scanCodeException.setCode(code);
                // 1 扫码进货  2 消费者返利  3 扫码核销
                scanCodeException.setType(1);
                scanCodeException.setAddress(address);
                scanCodeException.setGoodsCode(goodsCode);
                scanCodeException.setGoodsName(goodsName);
                scanCodeException.setDealerName(dealerName);
                scanCodeException.setTerminalId(shopId);
                scanCodeException.setScanUserId(scanCodeUserId);
                scanCodeException.setAddress(address);
                scanCodeException.setCompanyId(companyId);
                scanCodeException.setExceptionCode("0102");
                scanCodeException.setExceptionCause("0102异常：此产品并非您所在区域产品");
                insert(scanCodeException);
                return new ScanCodeExceptionResult(true, "0102异常：此产品并非您所在区域产品");
            }
        }
        return new ScanCodeExceptionResult(false, "");
    }

    public ScanCodeExceptionResult judgeTerminalScanCodeAreaException(String code, String goodsCode, String goodsName, String dealerName, Integer shopId, Integer scanCodeUserId, ShopModel shopModel, String province, String city, String district, String street, String address,Integer companyId) {
        String scanCodeArea = province + city + district + street;
        String shopArea = shopModel.getProvince() + shopModel.getCity() + shopModel.getDistrict();

        if (isAreaException(scanCodeArea, shopArea)) {
            ScanCodeException scanCodeException = new ScanCodeException();
            scanCodeException.setCode(code);
            // 1 扫码进货  2 消费者返利  3 扫码核销
            scanCodeException.setType(1);
            scanCodeException.setAddress(address);
            scanCodeException.setGoodsCode(goodsCode);
            scanCodeException.setGoodsName(goodsName);
            scanCodeException.setDealerName(dealerName);
            scanCodeException.setTerminalId(shopId);
            scanCodeException.setScanUserId(scanCodeUserId);
            scanCodeException.setAddress(address);
            scanCodeException.setCompanyId(companyId);
            scanCodeException.setExceptionCode("0105");
            scanCodeException.setExceptionCause("0105异常：该终端未在指定区域扫码");
            insert(scanCodeException);
            return new ScanCodeExceptionResult(true, "0105异常：该终端未在指定区域扫码");
        }
        return new ScanCodeExceptionResult(false, "");
    }


    /**
     * 导出
     *
     * @param model
     * @return
     * @throws IOException
     */
    @Async
    public void export(ScanCodeException model) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addOrderDownloadCenter(model);

        // 2、上传文件
        String fileUrl = this.exportOrderFile(model);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param model
     * @return
     * @throws IOException
     */
    public String exportOrderFile(ScanCodeException model) throws IOException {
        List<ScanCodeException> list = this.selectList(SearchUtil.getSearch(model));
        if (!AirUtils.hv(list)) {
            ScanCodeException scanCodeException = new ScanCodeException();
            list.add(scanCodeException);
        }

        FileItem fileItem = ExcelTools.exportByFile(list, 1);
        String fileName = (new StringBuffer()).append("YCSJ").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();

        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }


    /**
     * 添加下载中心数据
     *
     * @param model
     * @return
     */
    public OrderDownloadCenterModel addOrderDownloadCenter(ScanCodeException model) {
        OrderDownloadCenterModel downloadCenterModel = model.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.EXCEPTIONDATA.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 获取经销商串货日统计数据
     * @param searchMap
     * @return
     */
    public PageInfo<DealerFlowProductExportResp> getDealerFlowProductDays(Map<String,Object> searchMap,int pageNo,int pageSize){
        PageHelper.startPage(pageNo, pageSize);
        List<DealerFlowProductExportResp> list= scanCodeExceptionDao.getDealerFlowProductDays(searchMap);
        PageInfo<DealerFlowProductExportResp> page = new PageInfo(list);
        return page;
    }

    /**
     * 导出
     *
     * @param model
     * @return
     * @throws IOException
     */
    @Async
    public void exportFlowProduct(DealerFlowProductExportReq model) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addFlowProductList(model);

        // 2、上传文件
        String fileUrl = this.exportFlowProductDaysFile(model);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 添加下载中心数据
     *
     * @param model
     * @return
     */
    public OrderDownloadCenterModel addFlowProductList(DealerFlowProductExportReq model) {
        OrderDownloadCenterModel downloadCenterModel = model.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.DEALERPRODUCTFLOWDAYS.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }


    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param model
     * @return
     * @throws IOException
     */
    public String exportFlowProductDaysFile(DealerFlowProductExportReq model) throws IOException {
        List<DealerFlowProductExportResp> list = scanCodeExceptionDao.getDealerFlowProductDays(SearchUtil.getSearch(model));

        FileItem fileItem = ExcelTools.exportByFile(list, 1);
        String fileName="经销商串货产品流向日统计表.xls";
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }


    /**
     * 获取经销商串货统计数据
     * @param searchMap
     * @return
     */
    public PageInfo<DealerFlowProductResp> getDealerFlowProductList(Map<String,Object> searchMap,int pageNo,int pageSize){
        PageHelper.startPage(pageNo, pageSize);
        List<DealerFlowProductResp> list= scanCodeExceptionDao.getDealerFlowProduct(searchMap);
        PageInfo<DealerFlowProductResp> page = new PageInfo(list);
        return page;
    }



    /**
     * 导出
     *
     * @param model
     * @return
     * @throws IOException
     */
    @Async
    public void exportFlowProductList(DealerFlowProductExportReq model) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addDealerFlowProduct(model);

        // 2、上传文件
        String fileUrl = this.exportFlowProductFile(model);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 添加下载中心数据
     *
     * @param model
     * @return
     */
    public OrderDownloadCenterModel addDealerFlowProduct(DealerFlowProductExportReq model) {
        OrderDownloadCenterModel downloadCenterModel = model.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.DEALERPRODUCTFLOWLIST.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }


    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param model
     * @return
     * @throws IOException
     */
    public String exportFlowProductFile(DealerFlowProductExportReq model) throws IOException {
        List<DealerFlowProductResp> list = scanCodeExceptionDao.getDealerFlowProduct(SearchUtil.getSearch(model));

        FileItem fileItem = ExcelTools.exportByFile(list, 1);
        String fileName="经销商串货产品流向统计表.xls";
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }


}