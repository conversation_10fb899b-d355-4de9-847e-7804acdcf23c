package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.model.GroupActivityRewardModel;

/**
 * Description: 拼团活动奖励业务层
 *
 * <AUTHOR>
 * @create 2020/2/23 15:46
 */
public interface GroupActivityRewardService {

    /**
     * 新增
     */
    int insert(GroupActivityRewardModel groupActivityRewardModel);

    /**
     * 删除
     */
    int delete(int id);

    /**
     * 更新
     */
    int update(GroupActivityRewardModel groupActivityRewardModel);

    /**
     * 根据主键 id 查询
     */
    GroupActivityRewardModel load(int id);

    /**
     * 根据活动id获取赠品信息
     * @param activityId
     * @return
     */
    GroupActivityRewardModel findByActivityId(Integer activityId);

    GroupActivityRewardModel getUnlockGoodsData(Integer activityId, Integer shopId, Integer companyId);
}
