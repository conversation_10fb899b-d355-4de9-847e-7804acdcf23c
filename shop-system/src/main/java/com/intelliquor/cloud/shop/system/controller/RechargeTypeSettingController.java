package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.intelliquor.cloud.shop.system.model.RechargeTypeSettingModel;
import com.intelliquor.cloud.shop.system.service.RechargeTypeSettingService;

import java.util.List;


/**
 * 描述：充值类型设置控制层
 *
 * <AUTHOR>
 * @date 2020-02-23
 */
@Api(tags = {"充值类型设置操作接口"}, description = "充值类型设置操作接口")
@RestController
@RequestMapping("/system/rechargeTypeSetting")
public class RechargeTypeSettingController {

    @Autowired
    private RechargeTypeSettingService rechargeTypeSettingService;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<RechargeTypeSettingModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                      @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                      RechargeTypeSettingModel model) {
        if (model.getCompanyId() == null) {
            throw new BusinessException("公司id不能为空");
        }
        PageHelper.startPage(page, limit);
        List<RechargeTypeSettingModel> list = rechargeTypeSettingService.selectList(SearchUtil.getSearch(model));
        PageInfo<RechargeTypeSettingModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<RechargeTypeSettingModel>> getList(RechargeTypeSettingModel model) {
        if (model.getCompanyId() == null) {
            throw new BusinessException("公司id不能为空");
        }
        List<RechargeTypeSettingModel> list = rechargeTypeSettingService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(RechargeTypeSettingModel model) {
        rechargeTypeSettingService.insert(model);
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息", httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(RechargeTypeSettingModel model) {
        rechargeTypeSettingService.update(model);
        return Response.ok("更新成功");
    }

    @ApiOperation(value = "更新类型名称信息", notes = "更新类型名称信息", httpMethod = "POST")
    @RequestMapping(value = "/updateTypeName")
    public Response<String> updateTypeName(@RequestBody RechargeTypeSettingModel model) {
        if (StringUtils.isBlank(model.getTypeName())) {
            throw new BusinessException("类型名称不能为空");
        }
        rechargeTypeSettingService.updateTypeName(model);
        return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息", httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
        rechargeTypeSettingService.delete(id);
        return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息", httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<RechargeTypeSettingModel> getById(@RequestParam(value = "id") Integer id) {
        RechargeTypeSettingModel model = rechargeTypeSettingService.getById(id);
        return Response.ok(model);
    }

    @ApiOperation(value = "根据公司ID查询信息", notes = "根据公司ID查询信息", httpMethod = "GET")
    @RequestMapping(value = "/getByCompanyId")
    public Response<List<RechargeTypeSettingModel>> getByCompanyId(@RequestParam(value = "companyId") Integer companyId,@RequestParam(value = "rechargeTypeId") Integer rechargeTypeId
    ) {
        List<RechargeTypeSettingModel> model = rechargeTypeSettingService.getByCompanyId(companyId,rechargeTypeId);
        return Response.ok(model);
    }
}