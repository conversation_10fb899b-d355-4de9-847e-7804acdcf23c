package com.intelliquor.cloud.shop.system.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Auther: tianms
 * @Date: 2021/04/20 10:38
 * @Description: 员工请求对象
 */
@Data
public class SauceStaffUserReq {

    /**
     * 商户id
     */
    @NotNull
    private Integer companyId;

    /**
     * 终端店id
     */
    @NotNull
    private Integer shopId;

    /**
     * 员工名
     */
    private String staffName;

    /**
     * 状态(0-启用 1-禁用)
     */
    private Integer status;
}
