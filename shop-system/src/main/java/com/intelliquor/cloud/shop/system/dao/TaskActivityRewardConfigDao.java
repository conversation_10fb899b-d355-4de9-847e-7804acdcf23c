package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.TaskActivityRewardConfigModel;

import java.util.List;
import java.util.Map;

/**
 * 描述：解锁红包配置 Dao接口
 *
 * <AUTHOR>
 * @date 2020-01-21
 */
public interface TaskActivityRewardConfigDao {


    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<TaskActivityRewardConfigModel> selectList(Map<String, Object> searchMap);

    /**
     * 新增
     *
     * @param model
     * @return
     */
    Integer insert(TaskActivityRewardConfigModel model);

    /**
     * 更新
     *
     * @param model
     * @return
     */
    Integer update(TaskActivityRewardConfigModel model);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer delete(Integer id);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    TaskActivityRewardConfigModel getById(Integer id);

}