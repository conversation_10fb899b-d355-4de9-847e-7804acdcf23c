package com.intelliquor.cloud.shop.system.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.utils.AirUtils;
import com.intelliquor.cloud.shop.common.utils.OssUtil;
import com.intelliquor.cloud.shop.system.dao.OrderDownloadCenterDao;
import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单下载中心 逻辑层
 *
 * <AUTHOR>
 * @date 2019/11/4 16:53
 * @desc
 */
@Service
@Slf4j
public class OrderDownloadCenterService {

    @Autowired
    private OrderDownloadCenterDao downloadCenterDao;

    @Value("${local.order.folder}")
    private String orderFolder;

    @Autowired
    private OssUtil ossUtil;

    /**
     * 添加 订单下载中心
     *
     * @param downloadCenterModel
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int addOrderDownloadCenter(OrderDownloadCenterModel downloadCenterModel) {
        OrderDownloadCenterModel orderDownloadCenter = downloadCenterDao.findOrderDownloadCenter(downloadCenterModel);
        int flag = 0;

        downloadCenterModel.setStatus((short) 0);
        downloadCenterModel.setFilePath("");
        downloadCenterModel.setUpdateTime(new Date());
        downloadCenterModel.setCreateTime(new Date());

        // 存在修改，不存在添加
        if (AirUtils.hv(orderDownloadCenter)) {
            flag = downloadCenterDao.updOrderDownloadCenter(downloadCenterModel);
        } else {
            flag = downloadCenterDao.addOrderDownloadCenter(downloadCenterModel);
        }
        return flag;
    }

    /**
     * 订单下载中心列表
     *
     * @param searchMap
     * @return
     */
    public List<OrderDownloadCenterModel> findOrderDownloadCenterList(Map<String, Object> searchMap) {
        List<OrderDownloadCenterModel> downloadCenterModelList = downloadCenterDao.findOrderDownloadCenterList(searchMap);
        return downloadCenterModelList;
    }

    /**
     * 更新单据状态
     *
     * @param downloadCenterModel
     * @return
     */
    public int updOrderDownloadCenter(OrderDownloadCenterModel downloadCenterModel) {
        downloadCenterModel.setStatus((short) 1); // 已生成
        downloadCenterModel.setUpdateTime(new Date());
        return downloadCenterDao.updOrderDownloadCenter(downloadCenterModel);
    }

    /**
     * 上传文件
     *
     * @param fileName 文件名
     * @param workbook 生成的Excel流
     * @return 上传文件地址
     * @throws IOException
     */
    public String getFileUrl(String fileName, Workbook workbook) throws IOException {
        long startTime = System.currentTimeMillis();   //获取开始时间
        // 将文件写入本地文件夹
        String filePath = "." + File.separator + orderFolder;
        File dir = new File(filePath);
        if (!dir.exists()) {
            dir.mkdir();
            log.info("orderFolder 文件夹创建完毕！");
        }
        String filePathName = filePath + File.separator + fileName;
        FileOutputStream output = new FileOutputStream(filePathName);
        workbook.write(output);//写入磁盘
        output.close();
        log.info("文件已存入！");

        // 如果文件写入成功，则上传至 OSS
        File file = new File(filePathName);

        //打印文件大小
        String printSize = getPrintSize(file.length());
        System.out.println("excel文件大小：" + printSize);
        long endTime = System.currentTimeMillis(); //获取结束时间
        System.out.println("excel文件写入本地运行时长： " + (endTime - startTime) + "ms");
        String fileUrl = "";
        if (file.exists()) {
            long startTime2 = System.currentTimeMillis();   //获取开始时间
            fileUrl = ossUtil.UploadFile(file, fileName);
            long endTime2 = System.currentTimeMillis(); //获取结束时间
            System.out.println("excel文件上传oss运行时长： " + (endTime2 - startTime2) + "ms");
        }
        // 上传成功后，删除本地文件
        if (AirUtils.hv(fileUrl)) {
            file.delete();
        }
        System.out.println("下载地址：" + fileUrl);
        return fileUrl;
    }

    /**
     * 上传文件
     *
     * @param fileName 文件名
     * @param bytes    生成的Excel字节
     * @return 上传文件地址
     * @throws IOException
     */
    public String getFileUrl(String fileName, byte[] bytes) throws IOException {
        long startTime = System.currentTimeMillis();   //获取开始时间
        // 将文件写入本地文件夹
        String filePath = "." + File.separator + orderFolder;
        File dir = new File(filePath);
        if (!dir.exists()) {
            dir.mkdir();
            log.info("orderFolder 文件夹创建完毕！");
        }
        String filePathName = filePath + File.separator + fileName;
        FileOutputStream output = new FileOutputStream(filePathName);
        output.write(bytes);//写入磁盘
        output.close();
        log.info("文件已存入！");

        // 如果文件写入成功，则上传至 OSS
        File file = new File(filePathName);

        //打印文件大小
        String printSize = getPrintSize(file.length());
        System.out.println("excel文件大小：" + printSize);
        long endTime = System.currentTimeMillis(); //获取结束时间
        System.out.println("excel文件写入本地运行时长： " + (endTime - startTime) + "ms");


        String fileUrl = "";
        if (file.exists()) {
            long startTime2 = System.currentTimeMillis();   //获取开始时间
            fileUrl = ossUtil.UploadFile(file, fileName);
            long endTime2 = System.currentTimeMillis(); //获取结束时间
            System.out.println("excel文件上传oss运行时长： " + (endTime2 - startTime2) + "ms");
        }
        long startTime3 = System.currentTimeMillis();   //获取开始时间
        // 上传成功后，删除本地文件
        if (AirUtils.hv(fileUrl)) {
            file.delete();
        }
        long endTime3 = System.currentTimeMillis(); //获取结束时间
        System.out.println("excel文件删除运行时长： " + (endTime3 - startTime3) + "ms");
        System.out.println("下载地址：" + fileUrl);
        return fileUrl;
    }

    /**
     * 临时文件上传oss
     * @param filePathName
     * @param fileName
     * @return
     */
    public String getFileUrl(String filePathName,String fileName){
        File file = new File(filePathName);
        //打印文件大小
        String printSize = getPrintSize(file.length());
        log.info("excel文件大小：{}" , printSize);
        String fileUrl = "";
        if (file.exists()) {
            long startTime2 = System.currentTimeMillis();   //获取开始时间
            fileUrl = ossUtil.UploadFile(file, fileName);
            long endTime2 = System.currentTimeMillis(); //获取结束时间
            log.info("excel文件上传oss运行时长：{} " , (endTime2 - startTime2) + "ms");
        }
        long startTime3 = System.currentTimeMillis();   //获取开始时间
        // 上传成功后，删除本地文件
        if (AirUtils.hv(fileUrl)) {
            file.delete();
        }
        long endTime3 = System.currentTimeMillis(); //获取结束时间
        log.info("excel本地文件删除运行时长： {}" , (endTime3 - startTime3) + "ms");
        log.info("下载地址：{}" , fileUrl);
        return fileUrl;
    }


    /**
     *      * 获取文件大小
     *      * 
     *      * @param size
     *      * @return
     *     
     */
    public static String getPrintSize(long size) {
        // 如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        if (size < 1024) {
            return String.valueOf(size) + "B";
        } else {
            size = size / 1024;
        }
        // 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
        // 因为还没有到达要使用另一个单位的时候
        // 接下去以此类推
        if (size < 1024) {
            return String.valueOf(size) + "KB";
        } else {
            size = size / 1024;
        }
        if (size < 1024) {
            // 因为如果以MB为单位的话，要保留最后1位小数，
            // 因此，把此数乘以100之后再取余
            size = size * 100;
            return String.valueOf((size / 100)) + "." + String.valueOf((size % 100)) + "MB";
        } else {
            // 否则如果要以GB为单位的，先除于1024再作同样的处理
            size = size * 100 / 1024;
            return String.valueOf((size / 100)) + "." + String.valueOf((size % 100)) + "GB";
        }
    }

    /**
     * 获取临时文件路径
     * @param fileName
     * @return
     */
    public String getTempFileUrl(String fileName){
        String filePath = "." + File.separator + orderFolder;
        File dir = new File(filePath);
        if (!dir.exists()) {
            dir.mkdirs();
            log.info("orderFolder 文件夹创建完毕！");
        }
        String filePathName = filePath + File.separator + fileName;
        return filePathName;
    }
}
