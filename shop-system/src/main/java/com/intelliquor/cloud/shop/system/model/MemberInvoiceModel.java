package com.intelliquor.cloud.shop.system.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: 开票管理
 *
 * <AUTHOR>
 * @create 2020/4/23 17:33
 */

@Data
public class MemberInvoiceModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 开票金额
     */
    private BigDecimal amount;

    /**
     * 经办人
     */
    private String operator;

    /**
     * 经办人电话
     */
    private String operatorTel;

    /**
     * 代办人
     */
    private String agent;

    /**
     * 代办人电话
     */
    private String agentTel;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 状态：0待审核、1已通过、2已拒绝、3-审核通过但优市打款失败
     */
    private Integer status;

    /**
     * 开户行
     */
    @NotNull
    private String openingBank;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 记录类型：1开票，2提现
     */
    private Integer type;

    /**
     * 0未删除 1已删除
     */
    private Integer isDelete;

    /**
     * 终端id
     */
    private Integer shopId;

    /**
     * 商户id
     */
    private Integer companyId;

    @ApiModelProperty(value = "申请来源 0-云商 1-优市金服")
    private Integer source;

    /**
     * 额度类型：0授信额度，1云仓额度 ,2云仓保证金 3 线下金额 4-预售卡 5-签订协议
     */
    private Integer amountType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 类目
     */
    private Integer categoryType;

    /**
     * 审核时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    private String beginCheckTime;
    private String endCheckTime;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String beginCreateTime;
    private String endCreateTime;

    /**
     * 用于优市的加密校验
     */
    private String signature;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public MemberInvoiceModel() {
    }

    /**
     * 预售卡中，开票金额中来源于可提现金额的金额
     */
    private BigDecimal preSaleSourceDrawAmount;

    /**
     * 经销商编码，与dealerCode一致
     */
    private String dealerNo;

}
