package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.system.model.req.CloudBanquetPromoterReq;
import com.intelliquor.cloud.shop.system.model.resp.CloudBanquetGuoFenPromoterResp;
import com.intelliquor.cloud.shop.system.model.resp.CloudBanquetPromoterAppletResp;

/**
 * 云宴推介人服务层接口
 * <AUTHOR>
 * @date 2022-5-16
 */
public interface CloudBanquetPromoterService {

    /**
     * 云宴推介人界面相关接口 查询终端店以及终端店的云宴信息
     *
     * @param cloudBanquetPromoterReq 云宴推介人界面请求参数类
     * @return Response<CloudBanquetPromoterAppletControllerResp> 云宴推介人界面结果参数类
     */
    public Response<CloudBanquetPromoterAppletResp> selectPromoterByShopId(CloudBanquetPromoterReq cloudBanquetPromoterReq);

    /**
     * 云宴推介人界面相关接口 查询终端店以及终端店的云宴信息
     * @param promoterId 推介人的唯一标识id
     * @param companyId 公司id
     * @return Response<CloudBanquetPromoterAppletControllerResp> 云宴推介人界面结果参数类
     */
    public Response<CloudBanquetGuoFenPromoterResp> selectPromoterByPromoterId(String promoterId,
                                                                               Integer companyId);

    
}