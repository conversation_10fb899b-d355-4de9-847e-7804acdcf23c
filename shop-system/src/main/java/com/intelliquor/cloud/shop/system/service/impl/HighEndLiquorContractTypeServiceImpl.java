package com.intelliquor.cloud.shop.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.enums.DeleteFlagEnum;
import com.intelliquor.cloud.shop.system.dao.HighEndLiquorContractTypeDao;
import com.intelliquor.cloud.shop.system.model.HighEndLiquorContractTypeModel;
import com.intelliquor.cloud.shop.system.service.HighEndLiquorContractTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2024/7/26 14:38
 */
@Slf4j
@Service
public class HighEndLiquorContractTypeServiceImpl extends ServiceImpl<HighEndLiquorContractTypeDao, HighEndLiquorContractTypeModel> implements HighEndLiquorContractTypeService {

    @Override
    public List<HighEndLiquorContractTypeModel> queryAll() {
        return lambdaQuery()
                .eq(HighEndLiquorContractTypeModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getKey())
                .list();
    }

    @Override
    public List<HighEndLiquorContractTypeModel> queryByFenceCategory(int fenceCategory) {
        return lambdaQuery()
                .eq(HighEndLiquorContractTypeModel::getFenceCategory, fenceCategory)
                .eq(HighEndLiquorContractTypeModel::getDeleteFlag,  DeleteFlagEnum.NOT_DELETE.getKey())
                .list();
    }
}
