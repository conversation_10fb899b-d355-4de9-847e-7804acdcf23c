package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.common.utils.AirUtils;
import com.intelliquor.cloud.shop.common.utils.HttpUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.intelliquor.cloud.shop.system.dao.GoodsDao;
import com.intelliquor.cloud.shop.system.model.GoodsModel;

import java.net.URLEncoder;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.List;

/**
* 描述：商品管理 服务实现层
* <AUTHOR>
* @date 2019-08-01
*/
@Service
public class GoodsService{

    @Autowired
    private GoodsDao goodsDao;

    /**
    * 查询数据
    *
    * @return
    */
    public List<GoodsModel> selectList(Map<String, Object> searchMap) {
        List<GoodsModel> list = goodsDao.selectList(searchMap);
        // 前端要求 添加个value属性
        if (AirUtils.hv(list)) {
            for (GoodsModel goodsModel : list) {
                goodsModel.setValue(goodsModel.getGoodsName());
            }
        }
        return list;
    }

    /**
     * 查询数据
     *
     * @return
     */
    public List<GoodsModel> selectGoodsList(Map<String, Object> searchMap) {
        return goodsDao.selectGoodsList(searchMap);
    }


    /**
    * 新增数据
    *
    * @param model
    */
    public void insert(GoodsModel model) {
        goodsDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    public void update(GoodsModel model) {
        goodsDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    public void delete(Integer id) {
        goodsDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    public GoodsModel getById(Integer id) {
        return goodsDao.getById(id);
    }

    @Async
    public void goodsSyncService()throws Exception{

        String url = new String("http://221.1.64.180:6010/EAS8.asmx/GetProduct2?number=&group_number=&lastupdatetime="+ URLEncoder.encode("2010-01-01 00:00:00")+"&pass=aHMzNWFrZWpka2lxdXJncnRoZ3l3d2l2OHh2anAyaWs1Z2h4Nm4xNHQ2YjhtMjY3djc=");
        String httpResult = HttpUtils.doGet(url);
        httpResult = httpResult.replaceAll("\n", "").replaceAll("\r", "").replaceAll("\t", "");
        Document doc = DocumentHelper.parseText(httpResult);
        Element root = doc.getRootElement();
        //拿到根节点的名称
        System.out.println("根节点：" + root.getName());
        Element diffgramElement = root.element("diffgram");
        System.out.println("子节点：" + diffgramElement.getName());
        Element newDataSetElement = diffgramElement.element("NewDataSet");
        List<Element> elements = newDataSetElement.elements();
        for(Iterator<Element> it = elements.iterator(); it.hasNext();){
            Element element = it.next();
            String productNumber = element.element("FNUMBER").getText();
            String productName = element.element("FNAME").getText();
            GoodsModel goodsInfo = goodsDao.getByCode(productNumber);
            if(goodsInfo == null) {
                goodsInfo = new GoodsModel();
                //商品名称
                goodsInfo.setGoodsName(productName);
                //商品编号
                goodsInfo.setGoodsCode(productNumber);
                //指定为后台商品
                goodsInfo.setType(1);
                //设置商品为关闭状态
                goodsInfo.setIsShow(1);
                //设置商品创建时间
                goodsInfo.setCreateTime(new Date());
                //设置商品更新时间
                goodsInfo.setUpdateTime(new Date());
                goodsInfo.setIsDelete(0);
                goodsInfo.setCompanyId(66);// TODO: 2019/8/27 暂时默认66
                //设置
                goodsDao.insert(goodsInfo);
            }
        }
    }
}
