package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.model.NewsTypeModel;

import java.util.List;
import java.util.Map;

/**
 * 描述：云店内容类型 服务实现层
 *
 * <AUTHOR>
 * @date 2021-01-17
 */
public interface NewsTypeService {


    /**
     * 查询数据
     *
     * @return
     */
    List<NewsTypeModel> selectList(Map<String, Object> searchMap);


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(NewsTypeModel model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(NewsTypeModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    NewsTypeModel getById(Integer id);
}