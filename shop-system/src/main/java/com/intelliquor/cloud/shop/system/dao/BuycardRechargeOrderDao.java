package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.BuycardRechargeOrderModel;
import com.intelliquor.cloud.shop.system.model.resp.BuycardRechargeOrderPageResp;

import java.util.Map;
import java.util.List;
/**
* 描述：进货卡充值订单 Dao接口
* <AUTHOR>
* @date 2019-10-09
*/
public interface BuycardRechargeOrderDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<BuycardRechargeOrderPageResp> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(BuycardRechargeOrderModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(BuycardRechargeOrderModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    BuycardRechargeOrderModel getById(Integer id);

    /**
     * 根据orderNo查询
     *
     * @param orderNo
     * @return
     */
    BuycardRechargeOrderModel getByOrderNo(String orderNo);

}