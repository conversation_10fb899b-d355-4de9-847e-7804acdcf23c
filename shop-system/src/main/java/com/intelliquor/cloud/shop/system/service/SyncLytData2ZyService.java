package com.intelliquor.cloud.shop.system.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.intelliquor.cloud.shop.common.dao.ShopDao;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.system.dao.DealerDao;
import com.intelliquor.cloud.shop.system.model.DealerModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SyncLytData2ZyService {

    @Value("${lyt-company.id}")
    private Integer companyId ;

    @Value("${lyt.model.data.save.url}")
    private String modelDataSaveUrl ;

    @Value("${lyt.delaer.model.id}")
    private Integer dealerModelId ;

    @Value("${lyt.shop.model.id}")
    private Integer shopModelId ;

    @Value("${lyt.token}")
    private String token;

    @Autowired
    private DealerDao dealerDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private RestTemplate restTemplate;


    /**
     * 同步终端店数据至智盈
     * 同步修改时间为当天的
     */
    public void syncShop2Zy(String date){
        Map<String ,Object> serachMap = Maps.newHashMap();
        serachMap.put("companyId",companyId);
        serachMap.put("sortCode","id");
        serachMap.put("sortRole","asc");
        Date startDate = TimeUtilis.getDateByForm(date,"yyyy-MM-dd");
        long diff = TimeUtilis.datemindate(new Date(),startDate);
        for(int j=0;j<=diff;j++) {
            log.info("处理日期{}",startDate);
            serachMap.put("dateRegion",DateUtils.convert2String(startDate,"yyyy-MM-dd"));
            List<ShopModel> shopModelList = shopDao.selectList(serachMap);
            if(CollectionUtils.isNotEmpty(shopModelList)){
                try{
                    for (int i = 0;i<shopModelList.size() ;i++){
                        ShopModel model = shopModelList.get(i);
                        JSONObject jsonObject = new JSONObject();
                        if(model.getChannelId() != null){
                            jsonObject.put("channel",String.valueOf(model.getChannelId()));
                        }
                        if(StringUtils.isNotBlank(model.getChannelName())){
                            jsonObject.put("channel_name_88",model.getChannelName());
                        }
                        if(StringUtils.isNotBlank(model.getAddress())){
                            jsonObject.put("company_address",model.getAddress());
                        }
                        jsonObject.put("lastupdate_time",DateUtils.convert2String(model.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
                        if(StringUtils.isNotBlank(model.getLinkphone())){
                            jsonObject.put("link_phone_86",model.getLinkphone());
                        }
                        if(StringUtils.isNotBlank(model.getLinkman())){
                            jsonObject.put("link_man_85",model.getLinkman());
                        }
                        jsonObject.put("shopname",model.getName());
                        jsonObject.put("shopId_84",model.getId()+"");
                        jsonObject.put("sys_create_time",DateUtils.convert2String(model.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("source_system","lyt");
                        if(StringUtils.isNotBlank(model.getDiyCode())){
                            jsonObject.put("source_system_id",model.getDiyCode());
                        }
                        JSONObject cuk = new JSONObject();
                        cuk.put("f","shopId_84");
                        cuk.put("v",model.getId()+"");
                        jsonObject.put("cuk_data",cuk);
                        log.info("添加终端店:{}",jsonObject.toJSONString());
                        addModelData(companyId, shopModelId, jsonObject);
                    }
                }catch (Exception e){
                    log.error("同步失败{}",e);
                }
            }
            startDate = TimeUtilis.addDays(startDate, 1);
        }

    }

    /**
     * 同步经销商数据至智盈
     */
    public void syncDealer2Zy(String date){
        Map<String ,Object> serachMap = Maps.newHashMap();
        serachMap.put("companyId",companyId);
        serachMap.put("sortCode","id");
        serachMap.put("sortRole","asc");
        if(StringUtils.isBlank(date)){
            date = DateUtils.convert2String(new Date(),"yyyy-MM-dd");
        }
        serachMap.put("dateRegion",date);
        List<DealerModel> dealerModelList = dealerDao.selectList(serachMap);
        if(CollectionUtils.isNotEmpty(dealerModelList)){
            try{
                for (int i = 0;i<dealerModelList.size() ;i++){
                    DealerModel model = dealerModelList.get(i);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("dealer_code",model.getDealerCode());
                    jsonObject.put("dealer_id",String.valueOf(model.getId()));
                    jsonObject.put("dealer_name",model.getDealerName());
                    jsonObject.put("source_system","lyt");
                    jsonObject.put("source_system_id",model.getOtherNumber());
                    JSONObject cuk = new JSONObject();
                    cuk.put("f","dealer_code");
                    cuk.put("v",model.getDealerCode());
                    jsonObject.put("cuk_data",cuk);
                    log.info("添加经销商:{}",jsonObject.toJSONString());
                    addModelData(companyId, dealerModelId, jsonObject);
                }
            }catch (Exception e){
                log.error("同步失败{}",e);
            }
        }
    }

    private void addModelData(int userId, Integer modelId, JSONObject jsonObject) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("token", token);
            headers.set("userId", userId+"");
            headers.set("modelId", modelId+"");
            HttpEntity<JSONObject> entity = new HttpEntity<>(jsonObject,headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(modelDataSaveUrl, entity, String.class);
            log.info("responseEntity:{}",JSONObject.toJSONString(responseEntity));
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                JSONObject response = JSONObject.parseObject(responseEntity.getBody());
                if (!"200".equals(response.getString("code"))) {
                    log.error("同步智盈失败-{}", JSON.toJSONString(response));
                }
            }
        } catch (Exception e) {
            log.error("同步智盈失败-{}", e);
            throw new BusinessException("同步失败");
        }

    }
}
