package com.intelliquor.cloud.shop.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.QueryInterceptor;
import com.intelliquor.cloud.shop.common.dao.CommonCloudDealerInfoDao;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.CloudDealerInfoModel;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.DealerInfoModel;
import com.intelliquor.cloud.shop.system.service.DataCenterService;
import com.intelliquor.cloud.shop.system.service.DealerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 描述：经销商信息控制层
 *
 * <AUTHOR>
 * @date 2019-11-21
 */
@Api(tags = {"经销商信息操作接口"}, description = "经销商信息操作接口")
@RestController
@RequestMapping("/dealerInfo")
public class DealerInfoController {

    @Autowired
    private DealerInfoService dealerInfoService;

    @Autowired
    private DataCenterService dataCenterService;

    @Autowired
    private CommonCloudDealerInfoDao cloudDealerInfoDao;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<DealerInfoModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                             @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                             DealerInfoModel model) {

        PageHelper.startPage(page, limit);
        List<DealerInfoModel> list = dealerInfoService.selectList(SearchUtil.getSearch(model));
        PageInfo<DealerInfoModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<DealerInfoModel>> getList(DealerInfoModel model) {
        List<DealerInfoModel> list = dealerInfoService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(@RequestBody DealerInfoModel model) {
        //查询是否已存在经销商
        DealerInfoModel searchModel = new DealerInfoModel();
        searchModel.setDealerCode(model.getDealerCode());
        List<DealerInfoModel> list = dealerInfoService.selectList(SearchUtil.getSearch(searchModel));
        if (!CollectionUtils.isEmpty(list)) {
            return Response.fail("当前经销商已存在");
        }
        dealerInfoService.insert(model);
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "单独保存经销商信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/insert")
    public Response<String> insert(@RequestBody DealerInfoModel model) {
        //查询是否已存在经销商
        DealerInfoModel searchModel = new DealerInfoModel();
        searchModel.setDealerCode(model.getDealerCode());
        List<DealerInfoModel> list = dealerInfoService.selectList(SearchUtil.getSearch(searchModel));
        if (!CollectionUtils.isEmpty(list)) {
            return Response.fail("当前经销商已存在");
        }
        dealerInfoService.insertDealer(model);
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "保存经销商区域信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/insertDealer")
    public Response<String> insertDealerArea(@RequestParam(value = "dealerCode") String dealerCode, @RequestParam(value = "areaJson") String areaJson) {
        //应该还需要检查区域是否重复。。

        dealerInfoService.insertDealerArea(dealerCode, areaJson);
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息", httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(@RequestBody DealerInfoModel model) {
        dealerInfoService.update(model);
        return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息", httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
        dealerInfoService.delete(id);
        return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息", httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<DealerInfoModel> getById(@RequestParam(value = "id") Integer id) {
        DealerInfoModel model = dealerInfoService.getById(id);
        return Response.ok(model);
    }

    @ApiOperation(value = "从智盈查询经销商信息", notes = "从智盈查询经销商信息", httpMethod = "GET")
    @RequestMapping(value = "/getDealerList")
    public Response<List<DealerInfoModel>> getDealerList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                         @RequestParam(value = "limit", defaultValue = "100") int limit,
                                                         String dealerName, Integer companyId) {
        List<DealerInfoModel> list = dataCenterService.getDealerList(page, limit, dealerName, companyId);
        return Response.ok(list);
    }

    @ApiOperation(value = "根据经销商编码查询合伙人的上级是否是体验中心", notes = "根据经销商编码查询合伙人的上级是否是体验中心", httpMethod = "GET")
    @GetMapping("/getSuperDealerTypeByDealerCode")
    public Response<CloudDealerInfoModel> getSuperDealerTypeByDealerCode(@RequestParam String dealerCode) {

        QueryWrapper<CloudDealerInfoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dealer_code", dealerCode);
        CloudDealerInfoModel dealer = cloudDealerInfoDao.selectOne(queryWrapper);

        Map<String, Object> search = new HashMap<>();
        search.put("dealerId", dealer.getId());
        List<CloudDealerInfoModel> cloudDealerInfoModels = cloudDealerInfoDao.selectSupplierList(search);

        return Response.ok(cloudDealerInfoModels.get(0));
    }

    @ApiOperation(value = "根据经销商编码查询体验中心信息", notes = "根据经销商编码查询体验中心信息", httpMethod = "GET")
    @RequestMapping(value = "/getSuperDealerInfoByDealerCode")
    public Response<CloudDealerInfoModel> getSuperDealerInfoByDealerCode(@RequestParam String dealerCode) {
        CloudDealerInfoModel model = cloudDealerInfoDao.getSuperDealerInfoByDealerCode(dealerCode);
        return Response.ok(model);
    }

}
