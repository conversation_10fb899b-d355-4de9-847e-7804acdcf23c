package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.GrouponDateReport;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * 拼团数据报表
 * <AUTHOR>
 * @date 2020/2/4 14:24
 * @desc
 */

@Component
public interface GrouponDataReportDao {

    Double countGroupShopNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    Double countCompGroupShopNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> countGroupShopNumOfArea(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> countSaleNumOfShopId(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> countUserNumOfShopId(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> countCompGroupNumOfArea(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> countGroupNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> countCompGroupNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    Double countCompGroupAllNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("isComplete") Integer isComplete , @Param("activityId") Integer activityId);

    @Select("SELECT format( sum( order_amount ) / 10000, 4 ) FROM t_groupon_order WHERE order_status IN ( 1, 2, 3, 4 ) AND pay_time BETWEEN #{startTime} AND #{endTime}")
    BigDecimal sumOrderAmount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    @Select("SELECT sum( qty ) FROM t_groupon_order_detail d LEFT JOIN t_groupon_order o ON o.order_code = d.order_code WHERE o.order_status IN ( 1, 2, 3, 4 ) AND o.pay_time BETWEEN #{startTime} AND #{endTime}")
    int sumSaleNum(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> sumBuyNumByOpenId(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

    List<GrouponDateReport> countOpenIdByArea(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("activityId") Integer activityId);

}
