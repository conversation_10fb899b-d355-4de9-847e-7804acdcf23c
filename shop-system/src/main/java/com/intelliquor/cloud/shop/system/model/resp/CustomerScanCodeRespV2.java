package com.intelliquor.cloud.shop.system.model.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-08-21
 * 消费者扫码统计实体类
 */
@Data
public class CustomerScanCodeRespV2 {

    /**
     * 省
     */
    @Excel(name="省",orderNum = "0")
    private String province;
    /**
     * 市
     */
    @Excel(name="市",orderNum = "1")
    private String city;
    /**
     * 区
     */
    @Excel(name="区",orderNum = "2")
    private String district;
    /**
     * 扫码时间
     */
    @Excel(name="扫码时间",orderNum = "3",width = 20)
    private String scanTime;
    /**
     * 产品编号
     */
    @Excel(name="产品编号",orderNum = "4",width = 20)
    private String goodsCode;
    /**
     * 产品名称
     */
    @Excel(name="产品名称",orderNum = "5",width = 50)
    private String goodsName;

    /**
     * 活动名称
     */
    @Excel(name="活动名称",orderNum = "6",width = 50)
    private String activityName;
    /**
     * 消费者扫码瓶数
     */
    @Excel(name="消费者扫码瓶数",orderNum = "7",width = 20)
    private String scanCount;
    /**
     * 扫码金额
     */
    @Excel(name="扫码金额",orderNum = "8")
    private String scanAmount;
    /**
     *未兑奖瓶数
     */
    @Excel(name="未兑奖瓶数",orderNum = "9",width = 15)
    private String receivedNotCount = "0";
    /**
     * 未兑奖金额
     */
    @Excel(name="未兑奖金额",orderNum = "10",width = 15)
    private String receivedNotAmount = "0.0";

    /**
     * 活动id
     */
    private Integer activityId;



}
