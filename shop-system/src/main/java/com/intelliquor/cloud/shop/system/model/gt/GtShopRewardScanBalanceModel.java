package com.intelliquor.cloud.shop.system.model.gt;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 描述：联盟扫码批次表实体类
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
public class GtShopRewardScanBalanceModel implements Serializable {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "流水号")
    private String transaction;

    @ApiModelProperty(value = "出库类别 1是厂家发送给经销商 2 是经销商发送给分销商")
    private Integer outType;

    @ApiModelProperty(value = "从哪个经销商发货")
    private String fromDealerCode;

    @ApiModelProperty(value = "到那个核心分销商")
    private String toDealerCode;

    @ApiModelProperty(value = "扫码数")
    private Integer codeCount;

    @ApiModelProperty(value = "瓶数量")
    private Integer bottleNum;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "奖励积分")
    private Integer score;

    @ApiModelProperty(value = "奖励类型1:定额红包 2-随机红包")
    private Integer balanceType;

    @ApiModelProperty(value = "状态(0:正常1:异常)")
    private Integer status;

    @ApiModelProperty(value = "是否删除(0:否1:是)")
    private Integer isDelete;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扫码分销商ID")
    private Integer dealerId;

    @ApiModelProperty(value = "经销商用户id")
    private Integer dealerStaffId;

    @ApiModelProperty(value = "扫码类型1:分销商扫码")
    private Integer scanType;

    @ApiModelProperty(value = "")
    private Integer companyId;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "使用状态")
    private Integer useStatus;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "id";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";

    @ApiModelProperty(value = "经销商/分销商名称")
    private String dealerName;

    @ApiModelProperty(value = "业务区域")
    private String dealerArea;

    @ApiModelProperty(value = "业务区域名")
    private String dealerAreaName;

    @ApiModelProperty(value = "经销商/分销商编码")
    private String dealerCode;

    @ApiModelProperty(value = "类型。1：经销商，2：分销商")
    private Integer type;

    /**
     * 批次详情
     */
    List<GtShopDealerScanCodeDetailModel> scanCodeDetailModels;

    @ApiModelProperty(value = "数据日期,创建日期的 date格式")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateTime;
}