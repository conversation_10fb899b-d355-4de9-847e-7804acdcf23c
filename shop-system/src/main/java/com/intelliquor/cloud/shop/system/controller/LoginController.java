package com.intelliquor.cloud.shop.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.AssocArray;
import com.intelliquor.cloud.shop.common.utils.HttpUtils;
import com.intelliquor.cloud.shop.common.utils.PHPSerializer;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.system.model.UserModel;
import com.intelliquor.cloud.shop.system.model.dto.LoginUser;
import com.intelliquor.cloud.shop.system.service.UserService;
import com.intelliquor.cloud.shop.system.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2019/6/28
 */
@Api(tags = {"用户登录接口"}, description = "用户登录接口")
@RestController
public class LoginController {

    @Value("${zhiying.url}")
    private String url;


    @Autowired
    private StringRedisTemplate template;

    @ApiOperation(value = "登录", notes = "登录", httpMethod = "POST")
    @RequestMapping(value = "/login")
    public Response<LoginUser> login(@RequestBody UserModel model) {
        if (StringUtils.isEmpty(model.getUsername())) {
            return Response.fail("用户名不能为空");
        }
        if (StringUtils.isEmpty(model.getPassword())) {
            return Response.fail("密码不能为空");
        }
        LoginUser user = new LoginUser();
        Map<String, Object> map = new HashMap<>();
        map.put("username", model.getUsername());
        map.put("password", model.getPassword());
        map.put("remember",1);
        String param = JSONObject.toJSONString(map);
        String str = HttpUtils.doRestfulPost(url, param);
        System.out.println(str);
        if (StringUtils.isNotBlank(str)) {
            JSONObject result = JSONObject.parseObject(str);
            if (!"0".equals(result.get("code").toString())) {
                return Response.fail((String) result.get("error"));
            } else {
                JSONObject data = (JSONObject) result.get("data");
                String authKey = data.get("authKey").toString();
                String sessionId = data.get("sessionId").toString();
                JSONObject userInfo = (JSONObject) data.get("userInfo");
                user = JSONObject.toJavaObject(userInfo, LoginUser.class);
                user.setAuthKey(authKey);
                user.setSessionId(sessionId);
            }
        }
        return Response.ok(user);
    }

    @ApiOperation(value = "检查登录", notes = "检查登录", httpMethod = "GET")
    @RequestMapping(value = "/checkLogin")
    public Response<String> checkLogin(String authKey, String sessionId) {
        if (StringUtils.isEmpty(sessionId) && StringUtils.isEmpty(authKey)) {
            return Response.fail("参数缺失");
        }
        String sessionIdStr = template.opsForValue().get(sessionId);
        System.out.println(sessionIdStr);
        String prefix=sessionIdStr.substring(0,6);
        sessionIdStr = sessionIdStr.substring(6);
        PHPSerializer p = new PHPSerializer();
        Map userInfoMap = null;
        try {
            userInfoMap = (Map) p.unserialize(sessionIdStr.getBytes(), Map.class);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        if (userInfoMap == null) {
            //无效
            return Response.fail("解析失败");
        }
        AssocArray authInfo = (AssocArray) userInfoMap.get("Auth_" + authKey);
        if (authInfo == null) {
            //无效
            return Response.fail("登录已过期,请重新登录");
        } else if (authInfo.get("auth_expire") == null) {
            //没有有效期无效
            return Response.fail("登录已过期,请重新登录");
        } else {
            Integer authExpire = (Integer) authInfo.get("auth_expire");
            if (authExpire < 0) {
                //永久有效
                return Response.ok("登陆成功");
            } else {
                int now = (int) (System.currentTimeMillis() / 1000);
                if (authExpire < now) {
                    //登录过期
                    return Response.fail("登录已过期,请重新登录");
                }
            }
        }

        return Response.ok("登陆成功");
    }
    @ApiOperation(value = "登出", notes = "登出", httpMethod = "GET")
    @RequestMapping(value = "/removeToken")
    public Response<String> removeToken(){
        return Response.ok();
    }
}
