package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.ConsumerDrawRecordModel;
import com.intelliquor.cloud.shop.system.model.resp.BossDrawPrizeData;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
import java.util.List;
/**
* 描述：红星消费者抽奖 Dao接口
* <AUTHOR>
* @date 2020-10-21
*/
public interface ConsumerDrawRecordDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<ConsumerDrawRecordModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(ConsumerDrawRecordModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(ConsumerDrawRecordModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    ConsumerDrawRecordModel getById(Integer id);


    /**
     * 查询同步智盈最后一条记录的ID
     *
     * @return
     */
    Integer getMaxZyRecordId(@Param("companyId") Integer companyId);

    /**
     * 判断码信息是否存在
     *
     * @param qrcode
     * @param companyId
     * @return
     */
    int isQrcodeExist(@Param("qrcode") String qrcode, @Param("companyId") Integer companyId);


    /**
     * 查询开瓶数量
     * @param searchMap
     * @return
     */
    Integer  queryOpenNum(Map<String,Object> searchMap);


    /**
     * 查询开瓶数量
     * @param searchMap
     * @return
     */
    Integer  queryBottlePrize(Map<String,Object> searchMap);


    /**
     * 查询开瓶数量
     * @param searchMap
     * @return
     */
    BossDrawPrizeData queryCashPrize(Map<String,Object> searchMap);





}