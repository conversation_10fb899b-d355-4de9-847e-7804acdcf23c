<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.RewardScanCodeRecordDao">

    <sql id="baseColumn">
        id,
        qrcode,
        type,
        remark,
        create_time,
        codeType,
        lon,
        lat,
        company_id,
        shop_id
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.RewardScanCodeRecordModel">
        <id property="id" column="id"/>
        <result property="qrcode" column="qrcode"/>
        <result property="type" column="type"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="codeType" column="codeType"/>
        <result property="lon" column="lon"/>
        <result property="lat" column="lat"/>
        <result property="companyId" column="company_id"/>
        <result property="scanTime" column="scan_time"/>
        <result property="count" column="count"/>
        <result property="shopId" column="shop_id"/>

    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="companyId != null and companyId != '' ">
                AND company_id =  #{companyId}
            </if>
            <if test="type != null and type != '' ">
                AND type =  #{type}
            </if>
            <if test=" typeArray != null and typeArray != '' ">
                AND type in
                <foreach item="item" collection="typeArray" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null and startTime !='' ">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_scan_code_record
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.RewardScanCodeRecordModel">
        INSERT INTO t_reward_scan_code_record(
            qrcode,
            type,
            remark,
            codeType,
            lon,
            lat,
            company_id,
            shop_id
        )VALUES(
            #{qrcode},
            #{type},
            #{remark},
            #{codeType},
            #{lon},
            #{lat},
            #{companyId},
            #{shopId}
        )
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.RewardScanCodeRecordModel">
        UPDATE t_reward_scan_code_record
        <set>
            <if test="qrcode != null and qrcode !=''  ">
                qrcode = #{qrcode},
            </if>
            <if test="type != null and type !=''  ">
                type = #{type},
            </if>
            <if test="remark != null and remark !=''  ">
                remark = #{remark},
            </if>
            <if test="createTime != null and createTime !=''  ">
                create_time = #{createTime},
            </if>
            <if test="codeType != null and codeType !=''  ">
                codeType = #{codeType},
            </if>
            <if test="lon != null and lon !=''  ">
                lon = #{lon},
            </if>
            <if test="lat != null and lat !=''  ">
                lat = #{lat},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_reward_scan_code_record
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_scan_code_record
        WHERE id = #{id}
    </select>

    <select id="queryCountByCodeAndType" resultType="int">
        SELECT
        count(1)
        FROM t_reward_scan_code_record
        WHERE qrcode = #{qrcode} and type = #{type} and company_id = #{companyId}
    </select>

    <select id="queryCountByCodeAndTypeAndShopId" resultType="int">
        SELECT
        count(1)
        FROM t_reward_scan_code_record
        WHERE qrcode = #{qrcode} and type = #{type} and company_id = #{companyId} and shop_id=#{shopId}
    </select>

    <select id="queryCountByCode" resultType="int">
        SELECT
        count(1)
        FROM t_reward_scan_code_record r
        left join t_member_shop s on s.id=r.shop_id
        WHERE r.qrcode = #{qrcode} and r.type = #{type} and r.company_id = #{companyId} and r.shop_id!=#{shopId} and s.shop_type=0
    </select>


    <select id="invalidRecordReport" resultMap="baseResultMap">
        SELECT
        r.shop_id,
        date_format( r.`create_time`, '%Y-%m-%d' ) AS scan_time,
        r.type,
        count( r.id ) AS count,
        r.company_id
        FROM
        t_reward_scan_code_record r
        LEFT JOIN t_member_shop s ON r.shop_id = s.id
        WHERE r.company_id =  #{companyId}
        AND r.shop_id IS NOT NULL AND r.qrcode &lt;&gt; ''
        <if test="shopType != null and shopType != '' ">
            AND s.shop_type = #{shopType}
        </if>
        <if test="startTime != null and startTime !='' ">
            AND r.create_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != '' ">
            AND r.create_time &lt;= #{endTime}
        </if>
        GROUP BY scan_time, r.shop_id, r.type
        ORDER BY scan_time ASC,r.shop_id ASC, r.type ASC
    </select>

</mapper>