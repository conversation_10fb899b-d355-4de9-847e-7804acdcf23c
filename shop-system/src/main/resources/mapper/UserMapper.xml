<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.UserDao">

    <sql id="baseColumn">
        id,
        username,
        real_name,
        phone,
        status,
        create_time,
        last_login_time,
        deleted
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.UserModel">
            <id property="id" column="id"/>
            <result property="username" column="username"/>
            <result property="password" column="password"/>
            <result property="realName" column="real_name"/>
            <result property="phone" column="phone"/>
            <result property="status" column="status"/>
            <result property="createTime" column="create_time"/>
            <result property="lastLoginTime" column="last_login_time"/>
            <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="username != null and username !=''  ">
                and username like CONCAT('%',#{username},'%' )
            </if>
            <if test="realName != null and realName !=''  ">
                and real_name like CONCAT('%',#{realName},'%' )
            </if>
            <if test="status != null and status !=''  ">
                and status = #{status}
            </if>
            <if test="1==1">
                and deleted = 0
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_user
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.UserModel">
        INSERT INTO t_system_user(
            username,
            password,
            real_name,
            phone,
            status,
            create_time,
            last_login_time,
            deleted
        )VALUES(
            #{username},
            #{password},
            #{realName},
            #{phone},
            #{status},
            #{createTime},
            #{lastLoginTime},
            #{deleted}
        )
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.UserModel">
        UPDATE t_system_user
        <set>
            <if test="username != null and username !=''  ">
            username = #{username},
            </if>
            <if test="password != null and password !=''  ">
            password = #{password},
            </if>
            <if test="realName != null and realName !=''  ">
            real_name = #{realName},
            </if>
            <if test="phone != null and phone !=''  ">
            phone = #{phone},
            </if>
            <if test="status != null and status !=''  ">
            status = #{status},
            </if>
            <if test="createTime != null">
            create_time = #{createTime},
            </if>
            <if test="lastLoginTime != null">
            last_login_time = #{lastLoginTime},
            </if>
            <if test="deleted != null">
            deleted = #{deleted},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_system_user
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_user
        WHERE id = #{id}
    </select>

    <select id="getByUsername" resultMap="baseResultMap">
        SELECT
        id,
        username,
        real_name,
        password,
        phone,
        status,
        create_time,
        last_login_time,
        deleted
        FROM t_system_user
        WHERE username = #{username}
    </select>

    <select id="getUserRoleIds" resultType="java.lang.Integer">
        select
        role_id
        from
        t_system_user_role
        where user_id = #{userId}
    </select>

    <insert id="saveRole" parameterType="java.util.Map">
        insert into t_system_user_role(user_id,role_id)values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.userId},
            #{item.roleId}
            )
        </foreach>
    </insert>

    <delete id="deleteRole" parameterType="integer">
        delete from t_system_user_role where user_id = #{userId}
    </delete>
</mapper>