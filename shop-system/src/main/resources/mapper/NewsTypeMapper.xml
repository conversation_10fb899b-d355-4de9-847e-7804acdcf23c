<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.NewsTypeDao">

    <sql id="baseColumn">
        id,
        name,
        sort,
        status,
        is_delete,
        company_id,
        create_time,
        update_time
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.NewsTypeModel">
            <id property="id" column="id"/>
            <result property="name" column="name"/>
            <result property="sort" column="sort"/>
            <result property="status" column="status"/>
            <result property="isDelete" column="is_delete"/>
            <result property="companyId" column="company_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
            <if test="name !=null and name!=''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="status != null ">
                and status = #{status}
            </if>
            <if test="isDelete != null ">
                and is_delete = #{isDelete}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_news_type
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}, create_time DESC
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.NewsTypeModel">
        INSERT INTO t_system_news_type
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="name != null ">
            name,
            </if>
            <if test="sort != null ">
            sort,
            </if>
            <if test="status != null ">
            status,
            </if>
            <if test="isDelete != null ">
            is_delete,
            </if>
            <if test="companyId != null ">
            company_id,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="updateTime != null ">
            update_time
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="name != null ">
            #{name},
            </if>
            <if test="sort != null ">
            #{sort},
            </if>
            <if test="status != null ">
            #{status},
            </if>
            <if test="isDelete != null ">
            #{isDelete},
            </if>
            <if test="companyId != null ">
            #{companyId},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="updateTime != null ">
            #{updateTime}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.NewsTypeModel">
        UPDATE t_system_news_type
        <set>
            <if test="name != null ">
            name = #{name},
            </if>
            <if test="sort != null ">
            sort = #{sort},
            </if>
            <if test="status != null ">
            status = #{status},
            </if>
            <if test="isDelete != null ">
            is_delete = #{isDelete},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
            <if test="updateTime != null ">
            update_time = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
            <if test="companyId!=null">
                and company_id = #{companyId}
            </if>
    </update>

    <delete id="delete" parameterType="integer">
        UPDATE t_system_news_type SET is_delete = 0
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_news_type
        WHERE id = #{id}
    </select>

    <select id="checkUnique" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_news_type
        WHERE is_delete = 1
        <if test="companyId != null">
            and company_id = #{companyId}
        </if>
        <if test="name != null">
            and name = #{name}
        </if>
        <if test="id != null">
            and id &lt;&gt; #{id}
        </if>
        limit 1
    </select>

</mapper>