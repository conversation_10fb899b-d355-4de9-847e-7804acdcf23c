<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.StorageMarketRangeDao" >
  <resultMap id="BaseResultMap" type="com.intelliquor.cloud.shop.system.model.StorageMarketRange" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="mobile" property="mobile" jdbcType="VARCHAR" />
    <result column="shop_id" property="shopId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <if test="fields == null" >
      id, type, mobile, shop_id, create_time
    </if>
    <if test="fields != null" >
      ${fields}
    </if>
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.intelliquor.cloud.shop.system.model.StorageMarketRangeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_storage_market_range
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    id,type,mobile,shop_id,create_time
    from t_storage_market_range
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from t_storage_market_range
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.intelliquor.cloud.shop.system.model.StorageMarketRangeExample" >
    delete from t_storage_market_range
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.StorageMarketRange" >
    insert into t_storage_market_range (id, type, mobile, 
      shop_id, create_time)
    values (#{id,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{mobile,jdbcType=VARCHAR}, 
      #{shopId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.intelliquor.cloud.shop.system.model.StorageMarketRange" >
    insert into t_storage_market_range
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="mobile != null" >
        mobile,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.intelliquor.cloud.shop.system.model.StorageMarketRangeExample" resultType="java.lang.Integer" >
    select count(*) from t_storage_market_range
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update t_storage_market_range
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.mobile != null" >
        mobile = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.shopId != null" >
        shop_id = #{record.shopId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update t_storage_market_range
    set id = #{record.id,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      mobile = #{record.mobile,jdbcType=VARCHAR},
      shop_id = #{record.shopId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.intelliquor.cloud.shop.system.model.StorageMarketRange" >
    update t_storage_market_range
    <set >
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="mobile != null" >
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.intelliquor.cloud.shop.system.model.StorageMarketRange" >
    update t_storage_market_range
    set type = #{type,jdbcType=INTEGER},
      mobile = #{mobile,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert">
      INSERT INTO t_storage_market_range(
      type,
      mobile,
      shop_id
      )values
      <foreach collection="list" item="item" index="index" separator=",">
        (
        #{item.type},
        #{item.mobile},
        #{item.shopId}
        )
      </foreach>
      ON DUPLICATE KEY UPDATE
      type=VALUES(type),
      mobile=VALUES(mobile),
      shop_id=VALUES(shop_id)
  </insert>
  <select id="searchList" resultType="com.intelliquor.cloud.shop.system.model.StorageMarketRange">
    SELECT ms.name AS shop_name,smr.* FROM t_storage_market_range smr
    LEFT JOIN t_member_shop ms ON smr.shop_id=ms.id
    <where>
      <if test="shopName!=null and shopName!=''">
        and ms.name like CONCAT ('%',#{shopName},'%' )
      </if>
      <if test="mobile!=null and mobile!=''">
        and smr.mobile like CONCAT ('%',#{mobile},'%' )
      </if>
    </where>
    order by create_time desc
  </select>
</mapper>