<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.RewardGuideScanRecordDao">

    <sql id="baseColumn">
        id,
        guide_id,
        shop_user_id,
        shop_id,
        goods_code,
        code_type,
        code,
        reward_type,
        reward,
        company_id,
        create_time,
        update_time,
        inner_code,
        outer_code
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.RewardGuideScanRecordModel">
        <id property="id" column="id"/>
        <result property="guideId" column="guide_id"/>
        <result property="shopUserId" column="shop_user_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="goodsCode" column="goods_code"/>
        <result property="codeType" column="code_type"/>
        <result property="code" column="code"/>
        <result property="rewardType" column="reward_type"/>
        <result property="reward" column="reward"/>
        <result property="companyId" column="company_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="innerCode" column="inner_code"/>
        <result property="outerCode" column="outer_code"/>

    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_guide_scan_record
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.RewardGuideScanRecordModel">
        INSERT INTO t_reward_guide_scan_record(
            guide_id,
            shop_user_id,
            shop_id,
            goods_code,
            code_type,
            code,
            reward_type,
            reward,
            company_id,
            inner_code,
            outer_code
        )VALUES(
            #{guideId},
            #{shopUserId},
            #{shopId},
            #{goodsCode},
            #{codeType},
            #{code},
            #{rewardType},
            #{reward},
            #{companyId},
            #{innerCode},
            #{outerCode}
        )
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.RewardGuideScanRecordModel">
        UPDATE t_reward_guide_scan_record
        <set>
            <if test="guideId != null ">
                guide_id = #{guideId},
            </if>
            <if test="shopUserId != null ">
                shop_user_id = #{shopUserId},
            </if>
            <if test="shopId != null ">
                shop_id = #{shopId},
            </if>
            <if test="goodsCode != null ">
                goods_code = #{goodsCode},
            </if>
            <if test="codeType != null ">
                code_type = #{codeType},
            </if>
            <if test="code != null ">
                code = #{code},
            </if>
            <if test="rewardType != null ">
                reward_type = #{rewardType},
            </if>
            <if test="reward != null ">
                reward = #{reward},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId},
            </if>
            <if test="innerCode != null ">
                inner_code = #{innerCode},
            </if>
            <if test="outerCode != null ">
                outer_code = #{outerCode},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_reward_guide_scan_record
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_guide_scan_record
        WHERE id = #{id}
    </select>


    <select id="getByCode" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_guide_scan_record
        WHERE inner_code = #{code} or outer_code = #{code} limit 1
     </select>

</mapper>