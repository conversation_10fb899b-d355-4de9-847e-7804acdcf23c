<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.ShopVirtualLimitDao">

    <sql id="baseColumn">
        id,
        shop_id,
        applyAvailVirtualLimit,
        applyVirtualLimit,
        update_time
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.ShopVirtualLimitModel">
            <id property="id" column="id"/>
            <result property="shopId" column="shop_id"/>
            <result property="applyAvailVirtualLimit" column="applyAvailVirtualLimit"/>
            <result property="applyVirtualLimit" column="applyVirtualLimit"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_shop_virtual_limit
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.ShopVirtualLimitModel">
        INSERT INTO t_system_shop_virtual_limit
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="shopId != null ">
            shop_id,
            </if>
            <if test="applyAvailVirtualLimit != null ">
            applyAvailVirtualLimit,
            </if>
            <if test="applyVirtualLimit != null ">
            applyVirtualLimit,
            </if>
            <if test="updateTime != null ">
            update_time
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="shopId != null ">
            #{shopId},
            </if>
            <if test="applyAvailVirtualLimit != null ">
            #{applyAvailVirtualLimit},
            </if>
            <if test="applyVirtualLimit != null ">
            #{applyVirtualLimit},
            </if>
            <if test="updateTime != null ">
            #{updateTime}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.ShopVirtualLimitModel">
        UPDATE t_system_shop_virtual_limit
        <set>
            <if test="shopId != null ">
            shop_id = #{shopId},
            </if>
            <if test="applyAvailVirtualLimit != null ">
            applyAvailVirtualLimit = #{applyAvailVirtualLimit},
            </if>
            <if test="applyVirtualLimit != null ">
            applyVirtualLimit = #{applyVirtualLimit},
            </if>
            <if test="updateTime != null ">
            update_time = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_system_shop_virtual_limit
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_shop_virtual_limit
        WHERE id = #{id}
    </select>

    <select id="getByShopId" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_system_shop_virtual_limit
        WHERE shop_id = #{shopId}
    </select>

</mapper>