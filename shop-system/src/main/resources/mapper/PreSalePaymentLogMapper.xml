<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.PreSalePaymentLogDao">

    <sql id="baseColumn">
        id,
        type,
        content,
        create_time,
        result
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.PreSalePaymentLogModel">
            <id property="id" column="id"/>
            <result property="type" column="type"/>
            <result property="content" column="content"/>
            <result property="createTime" column="create_time"/>
            <result property="result" column="result"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_pre_sale_payment_logs
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.PreSalePaymentLogModel">
        INSERT INTO t_pre_sale_payment_logs
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="type != null ">
            type,
            </if>
            <if test="content != null ">
            content,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="result != null ">
            result
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="type != null ">
            #{type},
            </if>
            <if test="content != null ">
            #{content},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="result != null ">
            #{result}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.PreSalePaymentLogModel">
        UPDATE t_pre_sale_payment_logs
        <set>
            <if test="type != null ">
            type = #{type},
            </if>
            <if test="content != null ">
            content = #{content},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
            <if test="result != null ">
            result = #{result},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_pre_sale_payment_logs
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_pre_sale_payment_logs
        WHERE id = #{id}
    </select>

</mapper>