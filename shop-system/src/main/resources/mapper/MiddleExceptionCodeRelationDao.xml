<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.MiddleExceptionCodeRelationDao">

    <sql id="baseColumn">
        id,
        exception_id,
        code,
        box_code,
        create_time
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.RewardCodeRelationModel">
            <id property="id" column="id"/>
            <result property="exceptionId" column="exception_id"/>
            <result property="code" column="code"/>
            <result property="boxCode" column="box_code"/>
            <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="exceptionId!=null">
                exception_id=#{exceptionId}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_middle_exception_code_relation
        <include refid="selectiveWhere"/>
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.RewardCodeRelationModel">
        INSERT INTO t_middle_exception_code_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="exceptionId != null ">
            exception_id,
            </if>
            <if test="code != null ">
            code,
            </if>
            <if test="boxCode != null ">
            box_code,
            </if>
            <if test="createTime != null ">
            create_time
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="exceptionId != null ">
            #{exceptionId},
            </if>
            <if test="code != null ">
            #{code},
            </if>
            <if test="boxCode != null ">
            #{boxCode},
            </if>
            <if test="createTime != null ">
            #{createTime}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.RewardCodeRelationModel">
        UPDATE t_middle_exception_code_relation
        <set>
            <if test="exceptionId != null ">
            exception_id = #{exceptionId},
            </if>
            <if test="code != null ">
            code = #{code},
            </if>
            <if test="boxCode != null ">
            box_code = #{boxCode},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_middle_exception_code_relation
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_middle_exception_code_relation
        WHERE id = #{id}
    </select>


    <insert id="batchInsert"  >
        insert into t_middle_exception_code_relation (exception_id,code,box_code)
        values
        <foreach collection="models" item="model" separator=",">
            (#{model.exceptionId},#{model.code},#{model.boxCode})
        </foreach>
    </insert>


</mapper>