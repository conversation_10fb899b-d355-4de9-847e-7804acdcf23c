<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.system.dao.TerminalScanReturnApplyAwardDao">

    <insert id="batchInsert">
        INSERT INTO t_terminal_scan_return_apply_award(
        balance_id,
        shop_id,
        shop_virtual_amount,
        dealer_code,
        dealer_id,
        dealer_virtual_amount,
        type,
        company_id
        )values
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.balanceId},
            #{item.shopId},
            #{item.shopVirtualAmount},
            #{item.dealerCode},
            #{item.dealerId},
            #{item.dealerVirtualAmount},
            #{item.type},
            #{item.companyId})
        </foreach>
    </insert>
</mapper>
