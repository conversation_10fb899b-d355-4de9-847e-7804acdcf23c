package com.intelliquor.cloud.shop.applet.controller;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.common.valid.AddGroupValid;
import com.intelliquor.cloud.shop.salesmanManage.model.WaiterReportRecordModel;
import com.intelliquor.cloud.shop.salesmanManage.model.req.WaiterReportRecordReq;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.WaiterReportRecordResp;
import com.intelliquor.cloud.shop.salesmanManage.service.WaiterReportRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务员小程序 - 服务员信息提报
 *
 * @Auther: tianms
 * @Date: 2021/05/13 09:46
 */
@RestController
@RequestMapping("/applet/waiter/report")
public class WaiterReportRecordAppletController {

    @Autowired
    private WaiterReportRecordService waiterReportRecordService;

    @Autowired
    private UserContext userContext;

    /**
     * 服务员注册
     *
     * @param waiterReportRecordModel
     * @return com.intelliquor.cloud.shop.common.exception.RestResponse<java.lang.String>
     * @auther: tms
     * @date: 2021/05/13 09:50
     */
    @PostMapping("/register")
    public RestResponse<String> register(@Validated(value = {AddGroupValid.class}) @RequestBody WaiterReportRecordModel waiterReportRecordModel) {
        waiterReportRecordService.register(waiterReportRecordModel);
        return RestResponse.success("提交成功");
    }

    /**
     * 查询已邀请列表
     *
     * @param page
     * @param limit
     * @param req
     * @return com.intelliquor.cloud.shop.common.exception.RestResponse<com.github.pagehelper.PageInfo < com.intelliquor.cloud.shop.salesmanManage.model.resp.WaiterReportRecordResp>>
     * @auther: tms
     * @date: 2021/05/13 17:23
     */
    @GetMapping("/queryReportList")
    public RestResponse<PageInfo<WaiterReportRecordResp>> queryReportList(@RequestParam(value = "page", required = true) Integer page,
                                                                          @RequestParam(value = "limit", required = true) Integer limit,
                                                                          WaiterReportRecordReq req) {
        if (StringUtils.isNotEmpty(req.getReportStartTime())) {
            req.setReportEndTime(req.getReportStartTime() + " 23:59:59");
            req.setReportStartTime(req.getReportStartTime() + " 00:00:00");
        }
        req.setCompanyId(userContext.getUserInfo().getCompanyId().intValue());
        PageMethod.startPage(page, limit);
        List<WaiterReportRecordResp> list = waiterReportRecordService.queryList(SearchUtil.getSearch(req));
        PageInfo<WaiterReportRecordResp> pageInfo = new PageInfo<>(list);
        return RestResponse.success(pageInfo);
    }

}
