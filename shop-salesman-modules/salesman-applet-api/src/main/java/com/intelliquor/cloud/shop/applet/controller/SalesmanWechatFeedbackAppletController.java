package com.intelliquor.cloud.shop.applet.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanWechatFeedbackModel;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanWechatFeedbackResp;
import com.intelliquor.cloud.shop.salesmanManage.service.SalesmanWechatFeedbackService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 云销问题反馈
 *
 * <AUTHOR>
 * @date 2021-04-02
 */
@RestController
@RequestMapping("/applet/salesmanWechatFeedback")
public class SalesmanWechatFeedbackAppletController {

    @Autowired
    private SalesmanWechatFeedbackService salesmanWechatFeedbackService;

    @Autowired
    private UserContext userContext;


    /**
     * 分页查询
     *
     * @param page
     * @param limit
     * @param model
     * @return
     */
    @GetMapping(value = "/getListByPage")
    public PageResponse<List<SalesmanWechatFeedbackResp>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                        @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                        SalesmanWechatFeedbackResp model) {
        model.setCompanyId(userContext.getUserInfo().getIntCompanyId());
        PageHelper.startPage(page, limit);
        List<SalesmanWechatFeedbackResp> list = salesmanWechatFeedbackService.selectList(SearchUtil.getSearch(model));
        PageInfo<SalesmanWechatFeedbackResp> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<SalesmanWechatFeedbackResp>> getList(SalesmanWechatFeedbackModel model) {
        List<SalesmanWechatFeedbackResp> list = salesmanWechatFeedbackService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @PostMapping(value = "/save")
    public Response<String> save(@RequestBody SalesmanWechatFeedbackModel model) {
        model.setCompanyId(userContext.getUserInfo().getIntCompanyId());
        salesmanWechatFeedbackService.insert(model);
        return Response.ok("保存成功");
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/getById")
    public Response<SalesmanWechatFeedbackResp> getById(@RequestParam(value = "id") Integer id) {
        SalesmanWechatFeedbackResp model = salesmanWechatFeedbackService.getById(id);
        return Response.ok(model);
    }

}