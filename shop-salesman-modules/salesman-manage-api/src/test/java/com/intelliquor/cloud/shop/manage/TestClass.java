package com.intelliquor.cloud.shop.manage;

import com.alibaba.fastjson.JSON;
import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanAppletMenuConfigModel;
import com.intelliquor.cloud.shop.salesmanManage.service.SalesmanAppletMenuConfigService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: duyi
 * @Date: 2021/02/25 15:29
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestClass {

    @Autowired
    private SalesmanAppletMenuConfigService salesmanAppletMenuConfigService;

    @Test
    public void insertSalesmanMenu() {
        List<SalesmanAppletMenuConfigModel> req = new ArrayList<>();

        // ----------------------------------------------------首页---------------------------------------------
        SalesmanAppletMenuConfigModel salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("首页banner");
        salesmanAppletMenuConfigModel.setUri("");
        salesmanAppletMenuConfigModel.setIconUrl("");
        salesmanAppletMenuConfigModel.setParentId(1);
        salesmanAppletMenuConfigModel.setSort(1);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("今日工作");
        salesmanAppletMenuConfigModel.setUri("");
        salesmanAppletMenuConfigModel.setIconUrl("");
        salesmanAppletMenuConfigModel.setParentId(1);
        salesmanAppletMenuConfigModel.setSort(2);
        req.add(salesmanAppletMenuConfigModel);
        // ----------------------------------------------------首页---------------------------------------------
        // ----------------------------------------------------拜访---------------------------------------------
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("常规拜访");
        salesmanAppletMenuConfigModel.setUri("/pages/visit/visit");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/cgbf.png");
        salesmanAppletMenuConfigModel.setParentId(2);
        salesmanAppletMenuConfigModel.setSort(1);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("潜在拜访");
        salesmanAppletMenuConfigModel.setUri("/pages/latentVisit/latentVisit");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/qzbf.png");
        salesmanAppletMenuConfigModel.setParentId(2);
        salesmanAppletMenuConfigModel.setSort(2);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("陌生拜访");
        salesmanAppletMenuConfigModel.setUri("/pages/strangeVisit/strangeVisit");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/msbf.png");
        salesmanAppletMenuConfigModel.setParentId(2);
        salesmanAppletMenuConfigModel.setSort(3);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("拜访记录");
        salesmanAppletMenuConfigModel.setUri("/pages/visitRecord/visitRecord");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/bfjl.png");
        salesmanAppletMenuConfigModel.setParentId(2);
        salesmanAppletMenuConfigModel.setSort(4);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("拜访计划");
        salesmanAppletMenuConfigModel.setUri("/pages/visitPlan/visitPlan");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/plan.png");
        salesmanAppletMenuConfigModel.setParentId(2);
        salesmanAppletMenuConfigModel.setSort(5);
        req.add(salesmanAppletMenuConfigModel);
        // ----------------------------------------------------拜访---------------------------------------------
        // ----------------------------------------------------客户---------------------------------------------
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("终端管理");
        salesmanAppletMenuConfigModel.setUri("/pages/terminal/terminalManage");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/zdgl.png");
        salesmanAppletMenuConfigModel.setParentId(3);
        salesmanAppletMenuConfigModel.setSort(1);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("终端提报");
        salesmanAppletMenuConfigModel.setUri("/pages/terminalReported/terminalReported");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/zdtb.png");
        salesmanAppletMenuConfigModel.setParentId(3);
        salesmanAppletMenuConfigModel.setSort(2);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("经销商");
        salesmanAppletMenuConfigModel.setUri("/pages/distributor/distributor");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/jxs.png");
        salesmanAppletMenuConfigModel.setParentId(3);
        salesmanAppletMenuConfigModel.setSort(3);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("客户管理");
        salesmanAppletMenuConfigModel.setUri("/pages/customer/customerMange");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/khgl.png");
        salesmanAppletMenuConfigModel.setParentId(3);
        salesmanAppletMenuConfigModel.setSort(4);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("客户提报");
        salesmanAppletMenuConfigModel.setUri("/pages/customer/customerHand");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/khtb.png");
        salesmanAppletMenuConfigModel.setParentId(3);
        salesmanAppletMenuConfigModel.setSort(5);
        req.add(salesmanAppletMenuConfigModel);
        // ----------------------------------------------------客户---------------------------------------------
        // ----------------------------------------------------工具---------------------------------------------
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("每日预警");
        salesmanAppletMenuConfigModel.setUri("/pages/warning/warning");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/mryj.png");
        salesmanAppletMenuConfigModel.setParentId(4);
        salesmanAppletMenuConfigModel.setSort(1);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("稽查");
        salesmanAppletMenuConfigModel.setUri("/pages/Inspection/Inspection");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/jicha.png");
        salesmanAppletMenuConfigModel.setParentId(4);
        salesmanAppletMenuConfigModel.setSort(2);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("品鉴会");
        salesmanAppletMenuConfigModel.setUri("/pages/tastingMeet/tastingMeet");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/pinjianjiu.png");
        salesmanAppletMenuConfigModel.setParentId(4);
        salesmanAppletMenuConfigModel.setSort(3);
        req.add(salesmanAppletMenuConfigModel);
        salesmanAppletMenuConfigModel = new SalesmanAppletMenuConfigModel();
        salesmanAppletMenuConfigModel.setName("赠送品鉴酒");
        salesmanAppletMenuConfigModel.setUri("/pages/tastingWine/tastingWine");
        salesmanAppletMenuConfigModel.setIconUrl("http://member.intelliquor.com/yunxiao/pjj.png");
        salesmanAppletMenuConfigModel.setParentId(4);
        salesmanAppletMenuConfigModel.setSort(4);
        req.add(salesmanAppletMenuConfigModel);
        // ----------------------------------------------------工具---------------------------------------------
        System.out.println(JSON.toJSON(req));
        req.forEach(model -> {
            salesmanAppletMenuConfigService.insert(model);
        });
    }

}
