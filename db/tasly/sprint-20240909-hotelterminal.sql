ALTER TABLE t_terminal_shop MODIFY COLUMN shop_type int(3) DEFAULT 0 NOT NULL COMMENT '0:渠道终端 1:餐饮终端 2:团购终端 3:企业终端 4:连锁终端 5:会员终端 6:渠道终端会员 7:连锁终端会员 8:非会员虚拟终端 9:超级终端 10 连锁型餐饮酒店 11 特色型餐饮酒店 12 商务型餐饮酒店 13 宴席型餐饮酒店';
ALTER TABLE t_terminal_shop ADD food_business_license_code varchar(255) NOT NULL COMMENT '食品经营许可证编码';
ALTER TABLE t_terminal_shop ADD food_license_code varchar(255) NOT NULL COMMENT '食品经营许可证-社会信用代码';


CREATE TABLE
    t_hotel_activity_reward_config
(
    id                 INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    contract_type_code INT            NOT NULL COMMENT '合同类型编码',
    contract_type_name VARCHAR(255)   NOT NULL COMMENT '合同类型名称',
    goods_code         VARCHAR(255)   NOT NULL COMMENT '商品SKU',
    activity_type      INT            NOT NULL COMMENT '活动类型， 1，动销奖励，2开瓶奖励',
    reward_type        INT            NOT NULL COMMENT '奖励类型，1，红包，2，实物，3，积分',
    reward             DECIMAL(12, 6) NOT NULL COMMENT '奖励金额',
    `status`           INT COMMENT '状态标识符号 0：禁用 1：启用',
    delete_flag        INT COMMENT '删除标识 0：未删除 1：已删除',
    remark             VARCHAR(500) COMMENT '备注',
    create_user        VARCHAR(255) COMMENT '创建者',
    create_time        DATETIME COMMENT '创建时间',
    update_user        VARCHAR(255) COMMENT '更新者',
    update_time        DATETIME COMMENT '更新时间'
) COMMENT = '酒店餐饮终端活动奖励配置表';


create index IDX_CONTRACT_TYPE
    on t_hotel_activity_reward_config (contract_type_code);

create index IDX_CONTRACT_GOODS_ACTIVITY_TYPE
    on t_hotel_activity_reward_config (contract_type_code, goods_code, activity_type);



-- 插入数据
INSERT INTO t_hotel_activity_reward_config (contract_type_code,
                                            contract_type_name,
                                            goods_code,
                                            reward,
                                            reward_type,
                                            activity_type,
                                            `status`,
                                            delete_flag,
                                            create_time,
                                            update_time)
VALUES (2, '常规渠道经销合同', '10030891', 5, 3, 1, 1, 0, now(), now()),
       (24, '常规渠道综合经销合同', '10030891', 5, 3, 1, 1, 0, now(), now()),
       (24, '常规渠道综合经销合同', '10030431', 3, 3, 1, 1, 0, now(), now()),
       (24, '常规渠道综合经销合同', '10012526', 3, 3, 1, 1, 0, now(), now()),
       (3, '国台酱酒经销合同', '10030431', 3, 3, 1, 1, 0, now(), now()),
       (26, '国台酱酒经销合同', '10012526', 3, 3, 1, 1, 0, now(), now()),
       (26, '餐饮渠道经销合同', '10030891', 5, 3, 1, 1, 0, now(), now()),
       (26, '餐饮渠道经销合同', '10030431', 3, 3, 1, 1, 0, now(), now()),
       (26, '餐饮渠道经销合同', '10012526', 3, 3, 1, 1, 0, now(), now());

-- 插入数据
INSERT INTO t_hotel_activity_reward_config (contract_type_code,
                                            contract_type_name,
                                            goods_code,
                                            reward,
                                            reward_type,
                                            activity_type,
                                            `status`,
                                            delete_flag,
                                            create_time,
                                            update_time)
VALUES (2, '常规渠道经销合同', '10030891', 70, 3, 2, 1, 0, now(), now()),
       (24, '常规渠道综合经销合同', '10030891', 70, 3, 2, 1, 0, now(), now()),
       (24, '常规渠道综合经销合同', '10030431', 42, 3, 2, 1, 0, now(), now()),
       (24, '常规渠道综合经销合同', '10012526', 42, 3, 2, 1, 0, now(), now()),
       (3, '国台酱酒经销合同', '10030431', 42, 3, 2, 1, 0, now(), now()),
       (26, '国台酱酒经销合同', '10012526', 42, 3, 2, 1, 0, now(), now()),
       (26, '餐饮渠道经销合同', '10030891', 70, 3, 2, 1, 0, now(), now()),
       (26, '餐饮渠道经销合同', '10030431', 42, 3, 2, 1, 0, now(), now()),
       (26, '餐饮渠道经销合同', '10012526', 42, 3, 2, 1, 0, now(), now());


ALTER TABLE t_reward_activity MODIFY activity_name varchar(200) NULL COMMENT '活动名称';


ALTER TABLE t_terminal_product_protocol_config ADD policy_type int(10) DEFAULT 1 NULL COMMENT '政策类型：1-常规政策 2-酒店渠道政策';

ALTER TABLE t_terminal_product_protocol_relation ADD policy_type int(10) DEFAULT 1 NULL COMMENT '政策类型：1-常规政策 2-酒店渠道政策';

ALTER TABLE t_terminal_product_protocol_config MODIFY COLUMN protocol_product_code varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '协议产品分类(varchar)：0-国标；1-酱酒；3-高端酒';

INSERT INTO t_terminal_product_protocol_config
(protocol_code, protocol_product_code, protocol_start_time, protocol_end_time, protocol_status, protocol_type, market_type, terminal_type, display_type, priority_level, level_code, display_surface, quarter_scan_in_num, reward_type, reward_product_code, reward_amount, reward_point, year_scan_in_num, first_order_scan_in_num, packet_reward, year_packet_reward, create_time, create_user_id, update_time, update_user_id, is_delete, company_id, reward_amount_unit, version, stage_stock_achieved_standard, reward_detail, policy_type)
VALUES('2024_0_0_2_1', '0', '2024-09-10 00:00:00', '2024-12-31 23:59:59', 1, 2, NULL, 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, 100, NULL, NULL, NULL, '2024-09-10 00:00:00', NULL, '2024-09-10 00:00:00', NULL, 0, 50, NULL, 1, NULL, NULL, 2);

INSERT INTO t_terminal_product_protocol_config
(protocol_code, protocol_product_code, protocol_start_time, protocol_end_time, protocol_status, protocol_type, market_type, terminal_type, display_type, priority_level, level_code, display_surface, quarter_scan_in_num, reward_type, reward_product_code, reward_amount, reward_point, year_scan_in_num, first_order_scan_in_num, packet_reward, year_packet_reward, create_time, create_user_id, update_time, update_user_id, is_delete, company_id, reward_amount_unit, version, stage_stock_achieved_standard, reward_detail, policy_type)
VALUES('2024_0_0_2_2', '0', '2024-09-10 00:00:00', '2024-12-31 23:59:59', 1, 2, NULL, 2, NULL, NULL, '2', NULL, NULL, NULL, NULL, NULL, NULL, 50, NULL, NULL, NULL, '2024-09-10 00:00:00', NULL, '2024-09-10 00:00:00', NULL, 0, 50, NULL, 1, NULL, NULL, 2);

INSERT INTO t_terminal_product_protocol_config
(protocol_code, protocol_product_code, protocol_start_time, protocol_end_time, protocol_status, protocol_type, market_type, terminal_type, display_type, priority_level, level_code, display_surface, quarter_scan_in_num, reward_type, reward_product_code, reward_amount, reward_point, year_scan_in_num, first_order_scan_in_num, packet_reward, year_packet_reward, create_time, create_user_id, update_time, update_user_id, is_delete, company_id, reward_amount_unit, version, stage_stock_achieved_standard, reward_detail, policy_type)
VALUES('2024_0_0_2_3', '0', '2024-09-10 00:00:00', '2024-12-31 23:59:59', 1, 2, NULL, 2, NULL, NULL, '3', NULL, NULL, NULL, NULL, NULL, NULL, 20, NULL, NULL, NULL, '2024-09-10 00:00:00', NULL, '2024-09-10 00:00:00', NULL, 0, 50, NULL, 1, NULL, NULL, 2);

INSERT INTO t_terminal_product_protocol_config
(protocol_code, protocol_product_code, protocol_start_time, protocol_end_time, protocol_status, protocol_type, market_type, terminal_type, display_type, priority_level, level_code, display_surface, quarter_scan_in_num, reward_type, reward_product_code, reward_amount, reward_point, year_scan_in_num, first_order_scan_in_num, packet_reward, year_packet_reward, create_time, create_user_id, update_time, update_user_id, is_delete, company_id, reward_amount_unit, version, stage_stock_achieved_standard, reward_detail, policy_type)
VALUES('2024_1_0_2_1', '1', '2024-09-10 00:00:00', '2024-12-31 23:59:59', 1, 2, NULL, 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, 100, NULL, NULL, NULL, '2024-09-10 00:00:00', NULL, '2024-09-10 00:00:00', NULL, 0, 50, NULL, 1, NULL, NULL, 2);

INSERT INTO t_terminal_product_protocol_config
(protocol_code, protocol_product_code, protocol_start_time, protocol_end_time, protocol_status, protocol_type, market_type, terminal_type, display_type, priority_level, level_code, display_surface, quarter_scan_in_num, reward_type, reward_product_code, reward_amount, reward_point, year_scan_in_num, first_order_scan_in_num, packet_reward, year_packet_reward, create_time, create_user_id, update_time, update_user_id, is_delete, company_id, reward_amount_unit, version, stage_stock_achieved_standard, reward_detail, policy_type)
VALUES('2024_1_0_2_2', '1', '2024-09-10 00:00:00', '2024-12-31 23:59:59', 1, 2, NULL, 2, NULL, NULL, '2', NULL, NULL, NULL, NULL, NULL, NULL, 50, NULL, NULL, NULL, '2024-09-10 00:00:00', NULL, '2024-09-10 00:00:00', NULL, 0, 50, NULL, 1, NULL, NULL, 2);

INSERT INTO t_terminal_product_protocol_config
(protocol_code, protocol_product_code, protocol_start_time, protocol_end_time, protocol_status, protocol_type, market_type, terminal_type, display_type, priority_level, level_code, display_surface, quarter_scan_in_num, reward_type, reward_product_code, reward_amount, reward_point, year_scan_in_num, first_order_scan_in_num, packet_reward, year_packet_reward, create_time, create_user_id, update_time, update_user_id, is_delete, company_id, reward_amount_unit, version, stage_stock_achieved_standard, reward_detail, policy_type)
VALUES('2024_1_0_2_3', '1', '2024-09-10 00:00:00', '2024-12-31 23:59:59', 1, 2, NULL, 2, NULL, NULL, '3', NULL, NULL, NULL, NULL, NULL, NULL, 20, NULL, NULL, NULL, '2024-09-10 00:00:00', NULL, '2024-09-10 00:00:00', NULL, 0, 50, NULL, 1, NULL, NULL, 2);

-- 将禁用且已激活终端修改为未激活和预备终端
UPDATE t_terminal_shop tts
    join t_member_shop tms on tms.id = tts.member_shop_id
    join t_terminal_shop_info_schedule ttsis on ttsis.terminal_shop_id = tts.id
    SET
        tts.status = 0,
        tts.is_prepare = 1,
        tts.remark = CASE
        WHEN tts.remark IS NOT NULL AND tts.remark <> '' THEN CONCAT(tts.remark, '；-', '将停用且激活终端修改为未激活')
        ELSE '将停用且激活终端修改为未激活'
END
WHERE
    tts.is_delete = 0
    and ttsis.status = 1
    AND tms.status = 1;


-- 更新副表终端修改为未激活
UPDATE t_terminal_shop_info_schedule ttsis
    JOIN
    t_terminal_shop tts ON tts.id = ttsis.terminal_shop_id
    SET
        ttsis.status = 0,
        ttsis.is_prepare = 1,
        ttsis.remark = CASE
        WHEN ttsis.remark IS NOT NULL AND ttsis.remark <> '' THEN CONCAT(ttsis.remark, '；-', '将停用且激活终端修改为未激活')
        ELSE '将停用且激活终端修改为未激活'
END
WHERE tts.remark LIKE "%将停用且激活终端修改为未激活%";

-- 将member_shop终端修改为预备终端
UPDATE t_member_shop tms
    join t_terminal_shop tts on tts.member_shop_id = tms.id
    SET
        tms.is_prepare = 1,
        tms.remark = CASE
        WHEN tms.remark IS NOT NULL AND tms.remark <> '' THEN CONCAT(tms.remark, '；-', '将停用且激活终端修改为未激活')
        ELSE '将停用且激活终端修改为未激活'
END
where tts.remark LIKE "%将停用且激活终端修改为未激活%";
