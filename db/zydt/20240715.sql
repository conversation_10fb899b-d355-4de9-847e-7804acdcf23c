ALTER TABLE `t_terminal_reward_record`
    ADD INDEX `source_IDX`(`source`) USING BTREE,
    ADD INDEX `delete_IDX`(`is_delete`) USING BTREE;
ALTER TABLE `t_activity_reward_record`
    ADD INDEX `idx_event_type`(`event_type`),
    ADD INDEX `idx_activity_type`(`activity_type`),
    ADD INDEX `idx_account_type`(`account_type`);
ALTER TABLE `t_display_result`
    ADD INDEX `serial_num`(`serial_num`, `is_delete`) USING BTREE;
ALTER TABLE `t_terminal_reward_record`
    ADD INDEX `compare_IDX`(`is_compare`) USING BTREE;
ALTER TABLE `t_terminal_scan_detail`
    ADD INDEX `t_terminal_scan_detail_code_in_IDX`(`code_in`, `is_delete`, `return_goods`) USING BTREE;
ALTER TABLE `t_terminal_reward_calc_detail`
    ADD INDEX `t_id`(`t_id`) USING BTREE;
ALTER TABLE `t_terminal_sku_check`
    ADD INDEX `idx_status_date`(`terminal_shop_id`, `create_time`, `check_status`) USING BTREE;
ALTER TABLE `t_activity_reward_record`
    ADD INDEX `relation_rewardType_send_account_contract`(`reward_type`, `send_status`, `is_delete`, `account_type`, `contract_type`, `business_id`) USING BTREE;
