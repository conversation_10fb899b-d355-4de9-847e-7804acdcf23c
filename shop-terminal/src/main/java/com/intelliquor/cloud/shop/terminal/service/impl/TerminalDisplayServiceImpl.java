package com.intelliquor.cloud.shop.terminal.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.IsDelete;
import com.intelliquor.cloud.shop.common.enums.ProtocolCheckStatusEnum;
import com.intelliquor.cloud.shop.common.enums.ProtocolTypeEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.message.MessageUtil;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopContract25Resp;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopContractResp;
import com.intelliquor.cloud.shop.common.utils.CodeConstant;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.terminal.dao.*;
import com.intelliquor.cloud.shop.terminal.model.DealerContractRelModel;
import com.intelliquor.cloud.shop.terminal.model.*;
import com.intelliquor.cloud.shop.terminal.model.enums.DisplayProtocolTypeEnum;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalProductConfigReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalSkuCheckReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolActiveRelationResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolModelResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalSkuCheckAppealResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalSkuCheckResp;
import com.intelliquor.cloud.shop.terminal.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 终端采集信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TerminalDisplayServiceImpl extends ServiceImpl<TerminalDisplayDao, TerminalSkuCheck>
        implements ITerminalDisplayService {

    private final TerminalDisplayDao displayDao;

    private final TerminalShopDao terminalShopDao;

    private final TerminalShopContractDao terminalShopContractDao;

    private final TerminalSkuModelService skuModelService;

    private final UserContext userContext;

    private final TerminalProtocolDao terminalProtocolDao;

    private final TerminalSkuModeDao terminalSkuModeDao;

    private final TerminalProductProtocolDao terminalProductProtocolDao;

    private final TerminalShopLevelDao terminalShopLevelDao;

    private final TerminalVisitRecordDao terminalVisitRecordDao;

    private final TerminalAccountManagerDao terminalAccountManagerDao;

    private final ShopDealerOrderCommonDao shopDealerOrderCommonDao;

    private final TerminalSkuCheckAppealService terminalSkuCheckAppealService;

    @Resource
    private DealerContractRelDao dealerContractRelDao;

    @Autowired
    private TerminalSkuCheckService terminalSkuCheckService;

    @Autowired
    private ITerminalProtocolService terminalProtocolService;

    @Autowired
    private TerminalProductProtocolRelationServiceImpl terminalProductProtocolRelationService;

    @Autowired
    private TerminalProductProtocolConfigDao terminalProductProtocolConfigDao;

    @Autowired
    private TerminalProductService terminalProductService;

    @Autowired
    private  ContractTypeDao contractTypeDao;

    @Autowired
    private TerminalProtocolActivityRelationService terminalProtocolActivityRelationService;
    @Autowired
    private ITerminalProtocolActivityRelationService iTerminalProtocolActivityRelationService;

    @Autowired
    private TerminalProtocolActivityConfigDao terminalProtocolActivityConfigDao;

    @Autowired
    private TerminalDisplayActivityConfigDao terminalDisplayActivityConfigDao;

    @Autowired
    private TerminalProtocolActivityDao terminalProtocolActivityDao;

    @Autowired
    private TerminalProtocolActivityProductMappingDao terminalProtocolActivityProductMappingDao;

    @Autowired
    private TerminalProtocolActivityRewardMappingDao terminalProtocolActivityRewardMappingDao;

    @Autowired
    private TerminalProtocolActivityRewardConfigDao terminalProtocolActivityRewardConfigDao;


    @Override
    public List<TerminalSkuCheckResp> selectDisplayList(TerminalSkuCheckReq req, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        List<TerminalSkuCheckResp> terminalSkuCheckList = new ArrayList<>();
        // 1：查相同终端第一次提交；2：查相同终端最后一次提交
        if (req.getStatus() == 1) {
            terminalSkuCheckList = displayDao.selectMinDisplayList(req);
        } else if (req.getStatus() == 2) {
            terminalSkuCheckList = displayDao.selectMaxDisplayList(req);
        } else {
            terminalSkuCheckList = displayDao.selectDisplayList(req);
        }
        List<Integer> idList = terminalSkuCheckList.stream().map(e -> e.getId()).collect(Collectors.toList());
        if (idList != null && idList.size() > 0) {
            // 查询AI陈列数
            List<TerminalSkuCheckResp> aiSkuList = displayDao.selectAiSkuCountByCheckIds(idList);
            List<GtAiGoodsShelfSKUModel> skuModelList = terminalSkuModeDao.selectList(new QueryWrapper<GtAiGoodsShelfSKUModel>().in("check_id", idList));
            Map<Integer, List<GtAiGoodsShelfSKUModel>> listMap = skuModelList.stream().collect(Collectors.groupingBy(GtAiGoodsShelfSKUModel::getCheckId));
            terminalSkuCheckList.forEach(e -> {
                // AI陈列数赋值
                if (aiSkuList != null && aiSkuList.size() > 0) {
                    for (TerminalSkuCheckResp skuCheckResp : aiSkuList) {
                        if (e.getId().equals(skuCheckResp.getId())) {
                            e.setAiSkuTotal(skuCheckResp.getAiSkuTotal());
                            break;
                        }
                    }
                }
                if (listMap.containsKey(e.getId())) {
                    List<GtAiGoodsShelfSKUModel> skuModels = listMap.get(e.getId());
                    String[] displayImgArr = skuModels.stream().map(el -> el.getPhotoUrl()).toArray(String[]::new);
                    e.setDisplayImgList(displayImgArr);
                }
            });
        }
        return terminalSkuCheckList;
    }

    @Override
    public Map<String, Object> getTerminalSkuCheckDetailById2(Integer id) {
        if (id == null) {
            throw new BusinessException("400", "id不能为空，id：" + id);
        }
        // 陈列审核信息（主表：t_terminal_sku_check）
        TerminalSkuCheck displayModel = getById(id);
        if (displayModel == null) {
            throw new BusinessException("400", "未查询到该条陈列审核数据！id为：" + id);
        }
        TerminalSkuCheckResp displayResp = new TerminalSkuCheckResp();
        BeanUtils.copyProperties(displayModel, displayResp);
        // 查询AI陈列数（2023年5月21号后的数据可以直接取 t_terminal_sku_check 的 ai_sku_total 字段）
        Integer skuCount = displayDao.selectAiSkuCountByCheckId(displayModel.getId());
        displayResp.setAiSkuTotal(skuCount == null ? 0 : skuCount);
        // 查询陈列申诉列表
        if (displayModel.getAppealNumber() > 0) {
            LambdaQueryWrapper<TerminalSkuCheckAppealModel> lambdaQueryWrapper = new LambdaQueryWrapper<TerminalSkuCheckAppealModel>().eq(TerminalSkuCheckAppealModel::getCheckId, displayModel.getId());
            List<TerminalSkuCheckAppealModel> skuCheckAppealList = terminalSkuCheckAppealService.list(lambdaQueryWrapper);
            displayResp.setSkuCheckAppealList(skuCheckAppealList);
        }
        // 查询上传的陈列图片列表，t_terminal_shop表的id
        Integer terminalShopId = displayModel.getTerminalShopId();
        LambdaQueryWrapper<GtAiGoodsShelfSKUModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GtAiGoodsShelfSKUModel::getCheckId, id);
        List<GtAiGoodsShelfSKUModel> list = skuModelService.list(queryWrapper);
        if (list != null && list.size() > 0) {
            List<String> photoList = list.stream().map(GtAiGoodsShelfSKUModel::getPhotoUrl).collect(Collectors.toList());
            String[] displayImgList = ArrayUtil.toArray(photoList, String.class);
            displayResp.setDisplayImgList(displayImgList);
        }
        // 终端信息
        TerminalShopModel terminalShopModel = terminalShopDao.selectById(terminalShopId);

        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectById(terminalShopModel.getCreateUser());

        if (Objects.nonNull(terminalAccountManagerModel)) {
            if ((terminalAccountManagerModel.getParentId().equals(0))) {
                displayResp.setAccountManagerName(terminalAccountManagerModel.getName());
            } else {
                TerminalAccountManagerModel terminalAccountManagerModel1 = terminalAccountManagerDao.selectById(terminalAccountManagerModel.getParentId());
                displayResp.setAccountManagerName(terminalAccountManagerModel1.getName());
            }

        }

        // 合同信息
        TerminalShopContractResp contractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopId);
        if (Objects.nonNull(contractModel)) {
            DealerContractRelModel dealerContractRelModel = dealerContractRelDao.selectOne(
                    new QueryWrapper<DealerContractRelModel>()
                            .eq("contract_code", contractModel.getContractCode()).last("limit 1"));
            if (Objects.nonNull(dealerContractRelModel)) {
                displayResp.setAffiliateName(dealerContractRelModel.getAffiliateName());
                displayResp.setRegionName(dealerContractRelModel.getRegionName());
            }
        }
        /**
         * 查询拜访表 t_terminal_visit_record 的信息（2023.05.21后的数据才能关联）
         * 表 t_terminal_sku_check 的 visit_record_id 字段关联 t_terminal_visit_record 的主键id
         */
        TerminalVisitRecordModel visitRecordModel = null;
        int distance = 0;
        Integer shopReceivedQyt = 0;
        if (displayModel.getVisitRecordId() != null) {
            visitRecordModel = terminalVisitRecordDao.getById(displayModel.getVisitRecordId());

            // 查询终端信息
            TerminalShopModel terminalInfo = terminalProtocolService.getTerminalInfoByMemberShopId(visitRecordModel.getShopId());
            // 查询陈列信息
            QueryWrapper<TerminalSkuCheck> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("visit_record_id", visitRecordModel.getId());
            queryWrapper1.orderByAsc("create_time");
            queryWrapper1.last("limit 1");
            TerminalSkuCheck skuCheck = terminalSkuCheckService.getOne(queryWrapper1);
            // 旧数据没有关联拜访表，只能这样兼容旧数据
            Date oldDate = DateUtils.convert2Date("2023-05-21 00:00:00", "yyyy-MM-dd HH:mm:ss");
            Date newDate = visitRecordModel.getCreateTime();
            if (terminalInfo.getId() != null && skuCheck == null && (newDate.compareTo(oldDate) < 0)) {
                // 查询陈列信息
                QueryWrapper<TerminalSkuCheck> queryWrapper2 = new QueryWrapper<>();
                queryWrapper2.eq("terminal_shop_id", terminalInfo.getId());
                queryWrapper2.eq("DATE_FORMAT(create_time,'%Y-%m-%d')",
                        DateUtils.convert2StringYYYYMMdd(visitRecordModel.getSignOutTime() == null ? newDate : visitRecordModel.getSignOutTime()));
                queryWrapper2.last("limit 1");
                skuCheck = terminalSkuCheckService.getOne(queryWrapper2);
            }
            visitRecordModel.setSkuCheck(skuCheck);
            if (!(skuCheck == null || skuCheck.getId() == null)) {
                //            List<GtAiGoodsShelfSKUModel> list1 = skuModelService.getSkuInfoListByShopId(mainProtocol.getTerminalShopId());
                List<GtAiGoodsShelfSKUModel> list1 = skuModelService.getSkuInfoListByCheckId(skuCheck.getId());
                visitRecordModel.setSkuPhotoList(list1);
            }
        } else {
            if (terminalShopModel.getMemberShopId() != null) {
                visitRecordModel = terminalVisitRecordDao.getVisitRecordByShopId(terminalShopModel.getMemberShopId());
                // 查询当前终端今年已收货的数量（瓶）
                shopReceivedQyt = shopDealerOrderCommonDao.getShopReceivedQytByShopIdAndYear(terminalShopModel.getMemberShopId(), String.valueOf(DateUtils.getYear(new Date())));
            }
        }
        /**
         * 查询拜访表信息，取出 签到经纬度 和 签退经纬度，计算两点之间的距离
         * 终端的信息：terminalShopModel；拜访的信息：visitRecordModel
         */
        if (visitRecordModel != null && terminalShopModel != null) {
            try {
                Double.parseDouble(visitRecordModel.getSignOutLongitude());
                Double longitude = visitRecordModel.getSignLongitude() == null ? Double.parseDouble(visitRecordModel.getSignOutLongitude()) : visitRecordModel.getSignLongitude();
                Double latitude = visitRecordModel.getSignLatitude() == null ? Double.parseDouble(visitRecordModel.getSignOutLatitude()) : visitRecordModel.getSignLatitude();
                distance = getDistance(terminalShopModel.getLongitude(), terminalShopModel.getLatitude(), longitude, latitude);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        // 查询未删除的所有协议（主协议、附加协议）
        List<TerminalProtocolModelResp> protocolModelList = new ArrayList<>();
        ImmutableMap<String, Object> protocolMap = null;
        BigDecimal totalDisplayAmount = new BigDecimal("0");
        Integer totalDisplaySurface = 0;
        Integer yearScanInNum = 0;
//        protocolModelList = terminalProtocolDao.selectProtocolListByTerminalShopId(terminalShopId);
        protocolModelList = terminalProtocolDao.selectProtocolListByTerminalShopId2(terminalShopId);
        if (protocolModelList != null && protocolModelList.size() > 0) { // 统计 审核通过 的数据
            for (TerminalProtocolModelResp protocolModel : protocolModelList) {
                if (protocolModel.getCheckStatus() == 1) {
                    totalDisplayAmount = totalDisplayAmount.add(protocolModel.getDisplayAmount() == null ? new BigDecimal("0") : protocolModel.getDisplayAmount());
                    totalDisplaySurface += (protocolModel.getDisplaySurface() == null ? 0 : protocolModel.getDisplaySurface());
                    yearScanInNum += (protocolModel.getYearScanInNum() == null ? 0 : protocolModel.getYearScanInNum());
                }
            }
        }

        totalDisplaySurface = terminalProtocolService.getSumDisplaySurface(terminalShopModel);

        /**
         * totalDisplayAmount：陈列月度奖励（所有协议之和）
         * totalDisplaySurface：陈列面要求（所有协议之和）
         * aiSkuTotal：AI识别陈列面
         * distance：拍照距离
         * shopReceivedQyt：当前终端今年已收货的数量（瓶）
         * reqReceivedQyt：当前终端要求收货的数量（瓶）
         * reqExecuteNum：要求执行次数
         * executeNum：已执行次数
         */
//        protocolMap = ImmutableMap.of("totalDisplayAmount", totalDisplayAmount.intValue(), "totalDisplaySurface", totalDisplaySurface,
//                "aiSkuTotal",(skuCount == null ? 0 : skuCount),"distance",distance,
//                "shopReceivedQyt", (shopReceivedQyt == null ? 0 : shopReceivedQyt));
        protocolMap = ImmutableMap.<String, Object>builder()
                .put("totalDisplayAmount", totalDisplayAmount.intValue())
                .put("totalDisplaySurface", totalDisplaySurface)
                .put("aiSkuTotal", (skuCount == null ? 0 : skuCount))
                .put("distance", distance)
                .put("shopReceivedQyt", (shopReceivedQyt == null ? 0 : shopReceivedQyt))
                .put("reqReceivedQyt", yearScanInNum * 6)
                .put("reqExecuteNum", displayResp.getReqExecuteNum() == null ? 0 : displayResp.getReqExecuteNum())
                .put("executeNum", displayResp.getExecuteNum() == null ? 0 : displayResp.getExecuteNum())
                .build();
        Map<String, Object> result = new HashMap<>();
        result.put("displayModel", displayResp); // 陈列信息
        result.put("terminalShopModel", terminalShopModel); // 终端信息
        result.put("contractModel", contractModel); // 合同信息
        result.put("protocolModel", protocolModelList); // 协议数据（主协议、附加协议）
        result.put("visitModel", visitRecordModel); // t_terminal_visit_record 表的数据
        result.put("displayRewardModel", protocolMap); // 统计的 队列要求及奖励
        return result;
    }

    @Override
    public Map<String, Object> getTerminalSkuCheckDetailById22024Agreement(Integer id){
        // 获取当前时间
        Date now = new Date();

        if (id == null) {
            throw new BusinessException("400", "id不能为空，id：" + id);
        }
        // 陈列审核信息（主表：t_terminal_sku_check）
        TerminalSkuCheck displayModel = getById(id);
        if (displayModel == null) {
            throw new BusinessException("400", "未查询到该条陈列审核数据！id为：" + id);
        }
        TerminalSkuCheckResp displayResp = new TerminalSkuCheckResp();
        BeanUtils.copyProperties(displayModel, displayResp);
        // 查询AI陈列数（2023年5月21号后的数据可以直接取 t_terminal_sku_check 的 ai_sku_total 字段）
        Integer skuCount = displayDao.selectAiSkuCountByCheckId(displayModel.getId());
        displayResp.setAiSkuTotal(skuCount == null ? 0 : skuCount);
        // 查询陈列申诉列表
        if (displayModel.getAppealNumber() > 0) {
            LambdaQueryWrapper<TerminalSkuCheckAppealModel> lambdaQueryWrapper = new LambdaQueryWrapper<TerminalSkuCheckAppealModel>().eq(TerminalSkuCheckAppealModel::getCheckId, displayModel.getId());
            List<TerminalSkuCheckAppealModel> skuCheckAppealList = terminalSkuCheckAppealService.list(lambdaQueryWrapper);
            displayResp.setSkuCheckAppealList(skuCheckAppealList);
        }
        // 查询上传的陈列图片列表，t_terminal_shop表的id
        Integer terminalShopId = displayModel.getTerminalShopId();
        LambdaQueryWrapper<GtAiGoodsShelfSKUModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GtAiGoodsShelfSKUModel::getCheckId, id);
        List<GtAiGoodsShelfSKUModel> list = skuModelService.list(queryWrapper);
        if (list != null && list.size() > 0) {
            List<String> photoList = list.stream().map(GtAiGoodsShelfSKUModel::getPhotoUrl).collect(Collectors.toList());
            String[] displayImgList = ArrayUtil.toArray(photoList, String.class);
            displayResp.setDisplayImgList(displayImgList);
        }
        // 终端信息
        TerminalShopModel terminalShopModel = terminalShopDao.selectById(terminalShopId);

        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectById(terminalShopModel.getCreateUser());

        if (Objects.nonNull(terminalAccountManagerModel)) {
            if ((terminalAccountManagerModel.getParentId().equals(0))) {
                displayResp.setAccountManagerName(terminalAccountManagerModel.getName());
            } else {
                TerminalAccountManagerModel terminalAccountManagerModel1 = terminalAccountManagerDao.selectById(terminalAccountManagerModel.getParentId());
                displayResp.setAccountManagerName(terminalAccountManagerModel1.getName());
            }

        }

        // 合同信息
        TerminalShopContract25Resp contractModel25 = new TerminalShopContract25Resp();
        TerminalShopContractResp contractModel = new TerminalShopContractResp();
        contractModel25 = terminalShopContractDao.selectTerminalShopContract25Resp(terminalShopId);
        if (Objects.nonNull(contractModel25)) {
            DealerContractRelModel dealerContractRelModel = dealerContractRelDao.selectOne(
                    new QueryWrapper<DealerContractRelModel>()
                            .eq("contract_code", contractModel25.getContractCode()).last("limit 1"));
            if (Objects.nonNull(dealerContractRelModel)) {
                displayResp.setAffiliateName(dealerContractRelModel.getAffiliateName());
                displayResp.setRegionName(dealerContractRelModel.getRegionName());
            }
            // 合同类型更换为小合同类型
            String contractTypeName = contractTypeDao.contractTypeNames(dealerContractRelModel.getContractType().toString());
            contractModel25.setContractTypeName(contractTypeName);

        }
        contractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopId);
        if (Objects.nonNull(contractModel)) {
            DealerContractRelModel dealerContractRelModel = dealerContractRelDao.selectOne(
                    new QueryWrapper<DealerContractRelModel>()
                            .eq("contract_code", contractModel.getContractCode()).last("limit 1"));
            if (Objects.nonNull(dealerContractRelModel)) {
                displayResp.setAffiliateName(dealerContractRelModel.getAffiliateName());
                displayResp.setRegionName(dealerContractRelModel.getRegionName());
            }
        }

        /**
         * 查询拜访表 t_terminal_visit_record 的信息（2023.05.21后的数据才能关联）
         * 表 t_terminal_sku_check 的 visit_record_id 字段关联 t_terminal_visit_record 的主键id
         */
        TerminalVisitRecordModel visitRecordModel = null;
        int distance = 0;
        Integer shopReceivedQyt = 0;
        if (displayModel.getVisitRecordId() != null) {
            visitRecordModel = terminalVisitRecordDao.getById(displayModel.getVisitRecordId());

            // 查询终端信息
            TerminalShopModel terminalInfo = terminalProtocolService.getTerminalInfoByMemberShopId(visitRecordModel.getShopId());
            // 查询陈列信息
            QueryWrapper<TerminalSkuCheck> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("visit_record_id", visitRecordModel.getId());
            queryWrapper1.orderByAsc("create_time");
            queryWrapper1.last("limit 1");
            TerminalSkuCheck skuCheck = terminalSkuCheckService.getOne(queryWrapper1);
            // 旧数据没有关联拜访表，只能这样兼容旧数据
            Date oldDate = DateUtils.convert2Date("2023-05-21 00:00:00", "yyyy-MM-dd HH:mm:ss");
            Date newDate = visitRecordModel.getCreateTime();
            if (terminalInfo.getId() != null && skuCheck == null && (newDate.compareTo(oldDate) < 0)) {
                // 查询陈列信息
                QueryWrapper<TerminalSkuCheck> queryWrapper2 = new QueryWrapper<>();
                queryWrapper2.eq("terminal_shop_id", terminalInfo.getId());
                queryWrapper2.eq("DATE_FORMAT(create_time,'%Y-%m-%d')",
                        DateUtils.convert2StringYYYYMMdd(visitRecordModel.getSignOutTime() == null ? newDate : visitRecordModel.getSignOutTime()));
                queryWrapper2.last("limit 1");
                skuCheck = terminalSkuCheckService.getOne(queryWrapper2);
            }
            visitRecordModel.setSkuCheck(skuCheck);
            if (!(skuCheck == null || skuCheck.getId() == null)) {
                //            List<GtAiGoodsShelfSKUModel> list1 = skuModelService.getSkuInfoListByShopId(mainProtocol.getTerminalShopId());
                List<GtAiGoodsShelfSKUModel> list1 = skuModelService.getSkuInfoListByCheckId(skuCheck.getId());
                visitRecordModel.setSkuPhotoList(list1);
            }
        } else {
            if (terminalShopModel.getMemberShopId() != null) {
                visitRecordModel = terminalVisitRecordDao.getVisitRecordByShopId(terminalShopModel.getMemberShopId());
                // 查询当前终端今年已收货的数量（瓶）
                shopReceivedQyt = shopDealerOrderCommonDao.getShopReceivedQytByShopIdAndYear(terminalShopModel.getMemberShopId(), String.valueOf(DateUtils.getYear(new Date())));
            }
        }
        /**
         * 查询拜访表信息，取出 签到经纬度 和 签退经纬度，计算两点之间的距离
         * 终端的信息：terminalShopModel；拜访的信息：visitRecordModel
         */
        if (visitRecordModel != null && terminalShopModel != null) {
            try {
                Double.parseDouble(visitRecordModel.getSignOutLongitude());
                Double longitude = visitRecordModel.getSignLongitude() == null ? Double.parseDouble(visitRecordModel.getSignOutLongitude()) : visitRecordModel.getSignLongitude();
                Double latitude = visitRecordModel.getSignLatitude() == null ? Double.parseDouble(visitRecordModel.getSignOutLatitude()) : visitRecordModel.getSignLatitude();
                distance = getDistance(terminalShopModel.getLongitude(), terminalShopModel.getLatitude(), longitude, latitude);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        // 查询未删除的所有协议（主协议、附加协议）
        List<TerminalProtocolModelResp> protocolModelList = new ArrayList<>();
        ImmutableMap<String, Object> protocolMap = null;
        Integer totalDisplayAmount = 0;// 奖励产品
        Integer totalDisplaySurface = 0; // 陈列面要求
        long yearScanInNum = 0;
//        protocolModelList = terminalProtocolDao.selectProtocolListByTerminalShopId(terminalShopId);
//        protocolModelList = terminalProtocolDao.selectProtocolListByTerminalShopId2(terminalShopId);

        int displaySurface = 0;
        // 目前是设定的25年1月1日，实际上线需改成25年3月1日
        if (displayModel.getCreateTime().after(DateUtils.convert2Date("2025-03-01 00:00:00", "yyyy-MM-dd HH:mm:ss"))) {
            // 25年协议
//            TerminalProtocolActivityRelationModel terminalProtocolActivityRelationModel = terminalProtocolActivityRelationService.getTerminalProtocolActivityRelationByTerminalShopId(terminalShopId,displayModel.getCreateTime());

            TerminalProtocolActivityRelationModel terminalProtocolActivityRelationModelByTerminalShopModel = new TerminalProtocolActivityRelationModel();
            terminalProtocolActivityRelationModelByTerminalShopModel.setMemberShopId(Long.valueOf(terminalShopModel.getMemberShopId()));
            List<TerminalProtocolActiveRelationResp> terminalProtocolActiveRelationRespList = iTerminalProtocolActivityRelationService.getTerminalProtocolActivityRelationRespListByShopIdAndTime(terminalProtocolActivityRelationModelByTerminalShopModel, DateUtils.dateConvertLocalDateTime(displayModel.getCreateTime()));
            log.info("memberShopId: {}, 终端协议活动关系数据：{}", terminalShopModel.getMemberShopId(), JSON.toJSONString(terminalProtocolActiveRelationRespList));
            //如果没有查询到对应的关系数据
            if(CollectionUtils.isNotEmpty(terminalProtocolActiveRelationRespList)){
                displaySurface = terminalProtocolActiveRelationRespList.stream().map(terminalProtocolActiveRelationResp -> {
                    return terminalProtocolActiveRelationResp.getDisplaySurface();
                }).reduce(0, (a, b) -> a + b);
            }
            log.info("陈列面要求：{}", displaySurface);
            if(Objects.isNull(terminalProtocolActiveRelationRespList)){
                protocolModelList.add(null);
            }else{
                for (TerminalProtocolActiveRelationResp terminalProtocolActiveRelationResp : terminalProtocolActiveRelationRespList) {
                    LocalDateTime effectiveTime = terminalProtocolActiveRelationResp.getEffectiveTime();
                    Date effectiveTimeTodate = Date.from(effectiveTime.atZone(ZoneId.systemDefault()).toInstant());
                    // 陈列面
//                    Integer addisplaySurface = terminalProtocolActivityRelationService.getAddisplaySurfaceByActivity(terminalProtocolActivityRelationModel, displayModel.getCreateTime());
                    // 陈列奖品
//                TerminalProductModel terminalProductModel = terminalProtocolActivityRelationService.getTerminalProductByProtocolActivity(terminalProtocolActivityRelationModel);
                    // 如果活动配置、奖励配置等的数据状态为逻辑删除，那么也需要带出来，因为审批的时候要展示历史生效协议
                    // 根据活动协议关系查询活动协议内容配置
                    LambdaQueryWrapper<TerminalProtocolActivityConfigModel> terminalProtocolActivityConfigModelQueryWrapper = new LambdaQueryWrapper<>();
                    terminalProtocolActivityConfigModelQueryWrapper.eq(TerminalProtocolActivityConfigModel::getId,terminalProtocolActiveRelationResp.getActivityConfigId());
//                    terminalProtocolActivityConfigModelQueryWrapper.eq(TerminalProtocolActivityConfigModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                    TerminalProtocolActivityConfigModel terminalProtocolActivityConfigModel = terminalProtocolActivityConfigDao.selectOne(terminalProtocolActivityConfigModelQueryWrapper);
                    log.info("活动协议内容配置：{}", JSON.toJSONString(terminalProtocolActivityConfigModel));

                    LambdaQueryWrapper<TerminalDisplayActivityConfigModel> terminalDisplayActivityConfigQueryWrapper = new LambdaQueryWrapper<>();
                    terminalDisplayActivityConfigQueryWrapper.eq(TerminalDisplayActivityConfigModel::getId,terminalProtocolActivityConfigModel.getDisplayTypeId());
//                    terminalDisplayActivityConfigQueryWrapper.eq(TerminalDisplayActivityConfigModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                    TerminalDisplayActivityConfigModel terminalDisplayActivityConfigModel = terminalDisplayActivityConfigDao.selectOne(terminalDisplayActivityConfigQueryWrapper);
                    log.info("陈列类型配置：{}", JSON.toJSONString(terminalDisplayActivityConfigModel));

                    // 根据活动协议内容配置查询活动协议内容
                    LambdaQueryWrapper<TerminalActivityModel> terminalActivityQueryWrapper = new LambdaQueryWrapper<>();
                    terminalActivityQueryWrapper.eq(TerminalActivityModel::getId,terminalProtocolActivityConfigModel.getActivityId());
//                    terminalActivityQueryWrapper.eq(TerminalActivityModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                    TerminalActivityModel terminalActivityModel = terminalProtocolActivityDao.selectOne(terminalActivityQueryWrapper);
                    log.info("活动协议内容：{}", JSON.toJSONString(terminalActivityModel));

                    // 根据活动协议内容查询活动和产品对照sku
                    LambdaQueryWrapper<TerminalProtocolActivityProductMappingModel> terminalProtocolActivityProductMappingQueryWrapper = new LambdaQueryWrapper<>();
                    terminalProtocolActivityProductMappingQueryWrapper.eq(TerminalProtocolActivityProductMappingModel::getActivityId,terminalActivityModel.getId());
                    terminalProtocolActivityProductMappingQueryWrapper.eq(TerminalProtocolActivityProductMappingModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                    List<TerminalProtocolActivityProductMappingModel> terminalProtocolActivityProductMappingModelList = terminalProtocolActivityProductMappingDao.selectList(terminalProtocolActivityProductMappingQueryWrapper);
                    log.info("活动协议产品对照：{}", JSON.toJSONString(terminalProtocolActivityProductMappingModelList));
                    // 根据sku查询产品
                    List<TerminalProductModel> terminalProductModelList = new ArrayList<>();
                    for (TerminalProtocolActivityProductMappingModel terminalProtocolActivityProductMappingModel : terminalProtocolActivityProductMappingModelList) {
                        TerminalProductConfigReq terminalProductConfigReq = new TerminalProductConfigReq();
                        terminalProductConfigReq.setProductCode(terminalProtocolActivityProductMappingModel.getProductSku());
                        List<TerminalProductModel> terminalProductModels = terminalProductService.selectListByCode(terminalProductConfigReq);
                        if(Objects.nonNull(terminalProductModels) && terminalProductModels.size() > 0){
                            terminalProductModelList.add(terminalProductModels.get(0));
                        }
                    }
                    // 将产品名称拼接
                    String terminalProtocolActivityProductName = "";
                    for (TerminalProductModel terminalProductModel : terminalProductModelList) {
                        terminalProtocolActivityProductName += terminalProductModel.getProductName() + ",";
                    }
                    terminalProtocolActivityProductName = terminalProtocolActivityProductName.substring(0,terminalProtocolActivityProductName.length()-1);
                    log.info("活动协议产品名称：{}", terminalProtocolActivityProductName);
                    // 根据活动协议内容配置查询活动配置和奖励对照关系
                    LambdaQueryWrapper<TerminalProtocolActivityRewardMappingModel> terminalProtocolActivityRewardMappingQueryWrapper = new LambdaQueryWrapper<>();
                    terminalProtocolActivityRewardMappingQueryWrapper.eq(TerminalProtocolActivityRewardMappingModel::getActivityConfigId, terminalProtocolActivityConfigModel.getId());
                    terminalProtocolActivityProductMappingQueryWrapper.eq(TerminalProtocolActivityProductMappingModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                    TerminalProtocolActivityRewardMappingModel terminalProtocolActivityRewardMappingModel = terminalProtocolActivityRewardMappingDao.selectOne(terminalProtocolActivityRewardMappingQueryWrapper);
                    log.info("活动配置和奖励对照关系：{}", JSON.toJSONString(terminalProtocolActivityRewardMappingModel));

                    // 活动配置和奖励对照关系会有空的情况，需要处理没有奖励的场景
                    TerminalProtocolActivityRewardConfigModel terminalProtocolActivityRewardConfigModel = new TerminalProtocolActivityRewardConfigModel();
                    if (ObjectUtil.isNotNull(terminalProtocolActivityRewardMappingModel)) {
                        // 根据奖励对照关系查询协议活动奖励配置
                        LambdaQueryWrapper<TerminalProtocolActivityRewardConfigModel> terminalProtocolActivityRewardConfigQueryWrapper = new LambdaQueryWrapper<>();
                        terminalProtocolActivityRewardConfigQueryWrapper.eq(TerminalProtocolActivityRewardConfigModel::getId, terminalProtocolActivityRewardMappingModel.getRewardConfigId());
                    terminalProtocolActivityRewardConfigQueryWrapper.eq(TerminalProtocolActivityRewardConfigModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                        terminalProtocolActivityRewardConfigModel = terminalProtocolActivityRewardConfigDao.selectOne(terminalProtocolActivityRewardConfigQueryWrapper);
                        log.info("协议活动奖励配置：{}", JSON.toJSONString(terminalProtocolActivityRewardConfigModel));
                    } else {
                        log.info("活动配置和奖励对照关系为空");
                    }

                    // 组装数据
                    TerminalProtocolModelResp terminalProtocolModelResp = new TerminalProtocolModelResp();
                    // 协议类型，陈列类型，协议产品，陈列面要求，奖励产品，数量，生效日期，协议图片
                    // 协议类型
                    terminalProtocolModelResp.setProtocolTypeRelation(terminalProtocolActiveRelationResp.getProtocolType());
                    terminalProtocolModelResp.setProtocolTypeNameRealtion(ProtocolTypeEnum.getValue(terminalProtocolActiveRelationResp.getProtocolType()));
                    // 陈列类型
                    terminalProtocolModelResp.setDisplayType(terminalDisplayActivityConfigModel.getId().intValue());
                    terminalProtocolModelResp.setDisplayTypeName(terminalDisplayActivityConfigModel.getTypeName());
                    // 协议产品
//                terminalProtocolModelResp.setProtocolProductCode(terminalProductModel.getProductCode());
                    terminalProtocolModelResp.setProtocolProductName(terminalProtocolActivityProductName);
                    // 陈列面要求
                    terminalProtocolModelResp.setDisplaySurface(terminalDisplayActivityConfigModel.getDisplaySurface());
                    // 奖励产品
                    terminalProtocolModelResp.setRewardProductName(terminalProtocolActivityRewardConfigModel.getRewardProductName());
                    // 数量
                    Integer rewardAmount = terminalProtocolActivityRewardConfigModel.getRewardAmount();
                    if (ObjectUtil.isNotNull(rewardAmount)) {
                        terminalProtocolModelResp.setRewardAmount(Long.valueOf(rewardAmount));
                    } else {
                        terminalProtocolModelResp.setRewardAmount(null);
                    }
                    // 生效日期
                    terminalProtocolModelResp.setEffectiveTime(effectiveTimeTodate);
                    // 协议图片
                    terminalProtocolModelResp.setProtocolImage(terminalProtocolActiveRelationResp.getProtocolImage());
                    // 年包量要求
                    terminalProtocolModelResp.setYearScanInNum(terminalProtocolActivityConfigModel.getYearScanInNum() == null ? 0 : terminalProtocolActivityConfigModel.getYearScanInNum().intValue());
                    // 终端收货要求数量（瓶）
                    yearScanInNum += (terminalProtocolActiveRelationResp.getYearScanInNum() == null ? 0 : terminalProtocolActiveRelationResp.getYearScanInNum());//
                    // 陈列要求及奖励-陈列面要求
                    totalDisplaySurface = displaySurface;
                    protocolModelList.add(terminalProtocolModelResp);
                }
            }
        } else {
            // 24年协议 兼容老数据
            TerminalProductProtocolRelationModel terminalProductProtocolRelationObj = terminalProductProtocolRelationService.getTerminalProductProtocolRelationObjByTerminalShopId(terminalShopId,displayModel.getCreateTime());
            if(Objects.isNull(terminalProductProtocolRelationObj)){
                protocolModelList.add(null);
            }else{
                Date effectiveTime = terminalProductProtocolRelationObj.getEffectiveTime();
                Integer addisplaySurface = terminalProductProtocolRelationService.getAddisplaySurface(effectiveTime, displayModel.getCreateTime());
                LambdaQueryWrapper<TerminalProductProtocolConfigModel> terminalProductProtocolConfigQueryWrapper = new LambdaQueryWrapper<>();

                terminalProductProtocolConfigQueryWrapper.eq(TerminalProductProtocolConfigModel::getProtocolCode,terminalProductProtocolRelationObj.getProtocolCode());
                terminalProductProtocolConfigQueryWrapper.eq(TerminalProductProtocolConfigModel::getIsDelete, IsDelete.DELETE_FALSE.getCode());
                // 从产品协议表(t_terminal_product_protocol_config)中查询(protocol_code = terminalProductProtocolRelationObj.getProtocolCode())到对应的数据
                TerminalProductProtocolConfigModel terminalProductProtocolConfigObj = terminalProductProtocolConfigDao.selectOne(terminalProductProtocolConfigQueryWrapper);

                TerminalProtocolModelResp terminalProtocolModelResp = new TerminalProtocolModelResp();

                terminalProtocolModelResp.setProtocolTypeRelation(terminalProductProtocolConfigObj.getProtocolType());//协议类型
                terminalProtocolModelResp.setProtocolTypeNameRealtion(ProtocolTypeEnum.getValue(terminalProductProtocolConfigObj.getProtocolType()));

                terminalProtocolModelResp.setDisplayType(terminalProductProtocolConfigObj.getDisplayType());// 陈列类型
                terminalProtocolModelResp.setDisplayTypeName(DisplayProtocolTypeEnum.getValue(terminalProductProtocolConfigObj.getDisplayType()));

                terminalProtocolModelResp.setProtocolProductCode(terminalProductProtocolConfigObj.getProtocolProductCode());//协议产品
                if(Objects.equals(terminalProductProtocolConfigObj.getProtocolProductCode(),"0")){
                    terminalProtocolModelResp.setProtocolProductName("国标");
                }
                if(Objects.equals(terminalProductProtocolConfigObj.getProtocolProductCode(),"1")){
                    terminalProtocolModelResp.setProtocolProductName("酱酒");
                }

                terminalProtocolModelResp.setDisplaySurface(terminalProductProtocolConfigObj.getDisplaySurface());// 陈列面要求

                terminalProtocolModelResp.setRewardProductCode(terminalProductProtocolConfigObj.getRewardProductCode());// 奖励产品sku
                TerminalProductConfigReq terminalProductConfigReq = new TerminalProductConfigReq();
                terminalProductConfigReq.setProductCode(terminalProductProtocolConfigObj.getRewardProductCode());
                List<TerminalProductModel> terminalProductModels = terminalProductService.selectListByCode(terminalProductConfigReq);
                if(Objects.nonNull(terminalProductModels) && terminalProductModels.size() > 0){
                    terminalProtocolModelResp.setRewardProductName(terminalProductModels.get(0).getProductName());// 奖励产品名称
                }
                terminalProtocolModelResp.setRewardAmount(terminalProductProtocolConfigObj.getRewardAmount());// 奖励产品数量

                terminalProtocolModelResp.setCheckStatus(terminalProductProtocolRelationObj.getCheckStatus());// 审批状态
                terminalProtocolModelResp.setCheckStatusName(ProtocolCheckStatusEnum.getValue(terminalProductProtocolRelationObj.getCheckStatus()));// 审批状态名字

                terminalProtocolModelResp.setEffectiveTime(terminalProductProtocolRelationObj.getEffectiveTime());// 生效时间

                terminalProtocolModelResp.setProtocolImage(terminalProductProtocolRelationObj.getProtocolImage());// 协议照片

                yearScanInNum += (terminalProductProtocolConfigObj.getYearScanInNum() == null ? 0 : terminalProductProtocolConfigObj.getYearScanInNum());//
                totalDisplaySurface = terminalProductProtocolConfigObj.getDisplaySurface() + addisplaySurface;

                protocolModelList.add(terminalProtocolModelResp);
            }
        }
//        if (protocolModelList != null && protocolModelList.size() > 0) { // 统计 审核通过 的数据
//            for (TerminalProtocolModelResp protocolModel : protocolModelList) {
//                if (protocolModel.getCheckStatus() == 1) {
//                    totalDisplayAmount = totalDisplayAmount.add(protocolModel.getDisplayAmount() == null ? new BigDecimal("0") : protocolModel.getDisplayAmount());
//                    totalDisplaySurface += (protocolModel.getDisplaySurface() == null ? 0 : protocolModel.getDisplaySurface());
//                    yearScanInNum += (protocolModel.getYearScanInNum() == null ? 0 : protocolModel.getYearScanInNum());
//                }
//            }
//        }
//        totalDisplaySurface = terminalProtocolService.getSumDisplaySurface(terminalShopModel);

        /**
         * totalDisplayAmount：陈列月度奖励（所有协议之和）
         * totalDisplaySurface：陈列面要求（所有协议之和）
         * aiSkuTotal：AI识别陈列面
         * distance：拍照距离
         * shopReceivedQyt：当前终端今年已收货的数量（瓶）
         * reqReceivedQyt：当前终端要求收货的数量（瓶）
         * reqExecuteNum：要求执行次数
         * executeNum：已执行次数
         */
//        protocolMap = ImmutableMap.of("totalDisplayAmount", totalDisplayAmount.intValue(), "totalDisplaySurface", totalDisplaySurface,
//                "aiSkuTotal",(skuCount == null ? 0 : skuCount),"distance",distance,
//                "shopReceivedQyt", (shopReceivedQyt == null ? 0 : shopReceivedQyt));
        protocolMap = ImmutableMap.<String, Object>builder()
                .put("totalDisplayAmount", totalDisplayAmount.intValue())
                .put("totalDisplaySurface", totalDisplaySurface)
                .put("aiSkuTotal", (skuCount == null ? 0 : skuCount))
                .put("distance", distance)
                .put("shopReceivedQyt", (shopReceivedQyt == null ? 0 : shopReceivedQyt))
                .put("reqReceivedQyt", yearScanInNum)
                .put("reqExecuteNum", displayResp.getReqExecuteNum() == null ? 0 : displayResp.getReqExecuteNum())
                .put("executeNum", displayResp.getExecuteNum() == null ? 0 : displayResp.getExecuteNum())
                .build();
        Map<String, Object> result = new HashMap<>();
        result.put("displayModel", displayResp); // 陈列信息
        result.put("terminalShopModel", terminalShopModel); // 终端信息
//        if (now.after(DateUtils.convert2Date("2025-03-01 00:00:00", "yyyy-MM-dd HH:mm:ss"))) {
        if(displayModel.getCreateTime().after(DateUtils.convert2Date("2025-03-01 00:00:00", "yyyy-MM-dd HH:mm:ss"))){
            result.put("contractModel", contractModel25); // 合同信息
        } else {
            result.put("contractModel", contractModel); // 合同信息
        }
        result.put("protocolModel", protocolModelList); // 协议数据（主协议、附加协议）
        result.put("visitModel", visitRecordModel); // t_terminal_visit_record 表的数据
        result.put("displayRewardModel", protocolMap); // 统计的 队列要求及奖励
        return result;
    }

    @Override
    public Map<String, Object> getTerminalSkuCheckDetailById(Integer id) {
        // 陈列信息
        TerminalSkuCheck displayModel = getById(id);
        TerminalSkuCheckResp displayResp = new TerminalSkuCheckResp();
        BeanUtils.copyProperties(displayModel, displayResp);
        // 查询AI陈列数
        Integer skuCount = displayDao.selectAiSkuCountByCheckId(displayModel.getId());
        displayResp.setAiSkuTotal(skuCount == null ? 0 : skuCount);
        // 查询上传的陈列图片列表，t_terminal_shop表的id
        Integer terminalShopId = displayModel.getTerminalShopId();
        LambdaQueryWrapper<GtAiGoodsShelfSKUModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GtAiGoodsShelfSKUModel::getTerminalShopId, terminalShopId);
        List<GtAiGoodsShelfSKUModel> list = skuModelService.list(queryWrapper);
        List<String> newPhotoList = new ArrayList<>();
        newPhotoList.add(displayModel.getHeadImg());
        List<String> photoList = list.stream().map(GtAiGoodsShelfSKUModel::getPhotoUrl).collect(Collectors.toList());
        newPhotoList.addAll(photoList);
        String[] displayImgList = ArrayUtil.toArray(newPhotoList, String.class);
        displayResp.setDisplayImgList(displayImgList);
        // 终端信息
        TerminalShopModel terminalShopModel = terminalShopDao.selectById(terminalShopId);
        // 合同信息
        TerminalShopContractResp contractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopId);
        // 主协议
        LambdaQueryWrapper<TerminalProtocolModel> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(TerminalProtocolModel::getProtocolType, 0);
        queryWrapper1.eq(TerminalProtocolModel::getCheckStatus, 1);
        queryWrapper1.eq(TerminalProtocolModel::getDeleteStatus, 0);
        queryWrapper1.eq(TerminalProtocolModel::getTerminalShopId, terminalShopId);
        TerminalProtocolModel protocolModel = terminalProtocolDao.selectOne(queryWrapper1);
        ImmutableMap<String, Object> protocolMap;
        if (protocolModel != null) {
            String protocolName = CodeConstant.PRODUCT_PROTOCOL_TYPE_MAP.get(protocolModel.getProtocolProperty());
            String levelName = "";
            // 查询等级名称
            if (StringUtils.isNotEmpty(protocolModel.getLevelCode()) && !"0".equals(protocolModel.getLevelCode())) {
                levelName = terminalProtocolDao.getLevelNameByLevelCode(protocolModel.getLevelCode());
                TerminalProtocolModel terminalProtocolModel = terminalProtocolDao.selectById(protocolModel.getLevelCode());
                // 查询终端产品协议配置
                Long productProtocolConfigId = terminalProtocolModel.getProductProtocolConfigId();
                TerminalProductProtocolModel terminalProductProtocolModel = terminalProductProtocolDao.selectOneById(productProtocolConfigId.intValue());
                if (terminalProductProtocolModel != null) {
                    displayResp.setDisplayAmount(terminalProductProtocolModel.getDisplayAmount()); // 月度奖励
                    displayResp.setDisplaySurface(String.valueOf(terminalProductProtocolModel.getDisplaySurface())); // 陈面数
                }
                // 查询终端级别信息
                LambdaQueryWrapper<TerminalShopLevelModel> queryWrapper2 = new LambdaQueryWrapper<>();
                queryWrapper2.eq(TerminalShopLevelModel::getIsDelete, 0);
                queryWrapper2.eq(TerminalShopLevelModel::getLevelName, levelName);
                TerminalShopLevelModel terminalShopLevelModel = terminalShopLevelDao.selectOne(queryWrapper2);
                if (terminalShopLevelModel != null) {
                    displayResp.setReqExecuteNum(terminalShopLevelModel.getVisitNumber()); // 要求执行指出次数
                }
            }
            log.info("当前的产品编码--{}", protocolModel.getProductType());
            String protocolTypeName = CodeConstant.PROTOCOL_PRODUCT_TYPE_MAP.get(protocolModel.getProductType());
            protocolMap = ImmutableMap.of("protocolName", protocolName, "levelName", levelName, "protocolTypeName", protocolTypeName, "protocolImage", protocolModel.getProtocolImage());
        } else {
            protocolMap = ImmutableMap.of("protocolName", "", "levelName", "", "protocolTypeName", "", "protocolImage", "");
        }
        // 查询拜访表信息，取出 签到经纬度 和 签退经纬度，计算两点之间的距离
        int distance = 0;
        Integer memberShopId = terminalShopModel.getMemberShopId();
        TerminalVisitRecordModel visitRecordModel = null;
        if (memberShopId != null) {
            visitRecordModel = terminalVisitRecordDao.getVisitRecordByShopId(memberShopId);
        }
        if (visitRecordModel != null && terminalShopModel != null) {
            try {
                Double.parseDouble(visitRecordModel.getSignOutLongitude());
                Double longitude = visitRecordModel.getSignLongitude() == null ? Double.parseDouble(visitRecordModel.getSignOutLongitude()) : visitRecordModel.getSignLongitude();
                Double latitude = visitRecordModel.getSignLatitude() == null ? Double.parseDouble(visitRecordModel.getSignOutLatitude()) : visitRecordModel.getSignLatitude();
                distance = getDistance(terminalShopModel.getLongitude(), terminalShopModel.getLatitude(), longitude, latitude);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            visitRecordModel.setRemark(String.valueOf(distance)); // 距离信息存放在备注属性，后期可修改
        }
        Map<String, Object> result = new HashMap<>();
        result.put("displayModel", displayResp);
        result.put("terminalShopModel", terminalShopModel);
        result.put("contractModel", contractModel);
        result.put("protocolModel", protocolModel);
        result.put("protocol", protocolMap);
        result.put("visitModel", visitRecordModel);
        return result;
    }

    /**
     * 根据经纬度，计算两点间的距离
     *
     * @param firstLongitude  第一个点的经度
     * @param firstLatitude   第一个点的纬度
     * @param secondLongitude 第二个点的经度
     * @param secondLatitude  第二个点的纬度
     * @return 返回距离 单位米
     */
    public int getDistance(Double firstLongitude, Double firstLatitude, Double secondLongitude, Double secondLatitude) {
        int result = 0;
        if (firstLongitude != null && firstLatitude != null && secondLongitude != null && secondLatitude != null) {
            GlobalCoordinates source = new GlobalCoordinates(firstLongitude, firstLatitude); // 第一个坐标
            GlobalCoordinates target = new GlobalCoordinates(secondLongitude, secondLatitude); // 第二个坐标
            //创建GeodeticCalculator，调用计算方法，传入坐标系、经纬度用于计算距离
            GeodeticCurve geoCurve = new GeodeticCalculator().calculateGeodeticCurve(Ellipsoid.Sphere, source, target);
            // 计算坐标
            result = (int) geoCurve.getEllipsoidalDistance();
        }
        return result;
    }

    /**
     * @Description:废弃
     */
    public Map<String, Object> getTerminalSkuCheckDetailById_old(Integer id) {
        // 陈列信息
        TerminalSkuCheck displayModel = getById(id);
        TerminalSkuCheckResp displayResp = new TerminalSkuCheckResp();
        BeanUtils.copyProperties(displayModel, displayResp);
        // 查询上传的陈列图片列表
        Integer terminalShopId = displayModel.getTerminalShopId();
        LambdaQueryWrapper<GtAiGoodsShelfSKUModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GtAiGoodsShelfSKUModel::getTerminalShopId, terminalShopId);
        List<GtAiGoodsShelfSKUModel> list = skuModelService.list(queryWrapper);
        List<String> photoList = list.stream().map(GtAiGoodsShelfSKUModel::getPhotoUrl).collect(Collectors.toList());
        String[] displayImgList = ArrayUtil.toArray(photoList, String.class);
        displayResp.setDisplayImgList(displayImgList);
        // 终端信息
        TerminalShopModel terminalShopModel = terminalShopDao.selectById(terminalShopId);
        // 合同信息
        TerminalShopContractResp contractModel = terminalShopContractDao.selectTerminalShopContractResp(terminalShopId);
        // 主协议
        LambdaQueryWrapper<TerminalProtocolModel> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(TerminalProtocolModel::getProtocolType, 0);
        queryWrapper1.eq(TerminalProtocolModel::getCheckStatus, 1);
        queryWrapper1.eq(TerminalProtocolModel::getDeleteStatus, 0);
        queryWrapper1.eq(TerminalProtocolModel::getTerminalShopId, terminalShopId);
        TerminalProtocolModel protocolModel = terminalProtocolDao.selectOne(queryWrapper1);
        ImmutableMap<String, Object> protocolMap;
        if (protocolModel != null) {
            String protocolName = CodeConstant.PRODUCT_PROTOCOL_TYPE_MAP.get(protocolModel.getProtocolProperty());
            // 查询等级名称
            String levelName = terminalProtocolDao.getLevelNameByLevelCode(protocolModel.getLevelCode());
            protocolMap = ImmutableMap.of("protocolName", protocolName, "levelName", levelName);
        } else {
            protocolMap = ImmutableMap.of("protocolName", "", "levelName", "");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("displayModel", displayResp);
        result.put("terminalShopModel", terminalShopModel);
        result.put("contractModel", contractModel);
        result.put("protocolModel", protocolModel);
        result.put("protocol", protocolMap);
        return result;
    }

    /**
     * 校验参数
     *
     * @param req 参数
     * @return 陈列审核数据
     */
    private TerminalSkuCheck verifyParams(TerminalSkuCheckReq req) {
        // checkStatus状态：1-通过、2-不通过、3-申诉通过、4-申诉不通过、5-直接通过
        if (req.getId() == null) {
            throw new BusinessException("400", "陈列审核主键id不能为空！");
        }
        if (req.getCheckStatus() == null || req.getCheckStatus() <= 0 || req.getCheckStatus() > 5) {
            throw new BusinessException("400", "审核状态只能为：1、2、3、4、5");
        }
        if (req.getCheckStatus() == 2 || req.getCheckStatus() == 4 || req.getCheckStatus() == 5) {
            if (StringUtils.isEmpty(req.getCheckReason())) {
                throw new BusinessException("400", "审核状态为2、4、5，审核原因必填！");
            }
        }
        if (req.getCheckStatus() == 3 || req.getCheckStatus() == 4) {
            if (req.getAppealId() == null) {
                throw new BusinessException("400", "申诉数据appealId为必传！");
            }
            TerminalSkuCheckAppealModel appealModel = terminalSkuCheckAppealService.getById(req.getAppealId());
            if (appealModel == null) {
                throw new BusinessException("400", "申诉id错误，未查询到id为：" + req.getAppealId() + "的陈列申诉数据！");
            }
            if (appealModel.getCheckStatus() == 1) {
                throw new BusinessException("400", "该条申诉记录已审核通过，不能再次审核！");
            }
        }
        TerminalSkuCheck skuCheck = getById(req.getId());
        if (skuCheck == null) {
            throw new BusinessException("400", "id错误，未查询到id为：" + req.getId() + "的陈列审核数据！");
        }
        // 已经审核并且不是抽查
        if (skuCheck.getCheckStatus() == 1 && (req.getRecheck()==null || req.getRecheck()==0)) {
            throw new BusinessException("400", "该条记录已审核通过，不能再次审核！");
        }
        return skuCheck;
    }

    /**
     * 陈列审核
     * id：t_terminal_sku_check的主键id
     * checkStatus状态：1-通过、2-不通过、3-申诉通过、4-申诉不通过、5-直接通过
     * checkReason：审核原因
     * appealId：申诉表 t_terminal_sku_check_appeal 的主键id
     *
     * @param req 参数
     */
    @Override
    @Transactional(rollbackFor = {BusinessException.class, RuntimeException.class, Exception.class})
    public void approvalDisplay(TerminalSkuCheckReq req) {
        TerminalSkuCheck skuCheck = verifyParams(req); // 校验请求参数
        Integer checkStatus = req.getCheckStatus();
        // 处理不同状态的审核
        if (checkStatus == 3 || checkStatus == 4) {
            TerminalSkuCheckAppealModel appealModel = new TerminalSkuCheckAppealModel();
            appealModel.setId(req.getAppealId());
            appealModel.setCheckStatus(checkStatus == 3 ? 1 : 2);
            appealModel.setCheckReason(req.getCheckReason());
            appealModel.setCheckTime(new Date());
            appealModel.setCheckUser(req.getCheckUser());
            appealModel.setCheckName(req.getCheckName());
            appealModel.setCheckPhone(req.getCheckPhone());
            boolean update = terminalSkuCheckAppealService.updateById(appealModel);
            if (!update) {
                throw new BusinessException("400", "申诉数据更新失败，请重试！主键id：" + req.getAppealId());
            }
            if (checkStatus == 3) {
                req.setCheckStatus(1);
            } else {
                req.setCheckStatus(2);
            }
        } else {
            if (checkStatus == 5) {
                req.setCheckStatus(1);
            }
        }
        // 抽查
        if (req.getRecheck() != null && req.getRecheck() == 1) {
            // 查出以前的审批结果保存下来，同时更新新的审核结果
            LambdaQueryWrapper<TerminalSkuCheck> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TerminalSkuCheck::getId, req.getId());
            TerminalSkuCheck terminalSkuCheck = displayDao.selectOne(queryWrapper);
            Map<String, Object> preRecheckContent = new HashMap<>();
            preRecheckContent.put("check_status", terminalSkuCheck.getCheckStatus());
            preRecheckContent.put("check_reason", terminalSkuCheck.getCheckReason());
            req.setPreRecheckContent(JSON.toJSONString(preRecheckContent));
        }
        displayDao.approvalDisplay2(req);

        // 审核（checkStatus为 1 审核通过；checkStatus为 2 审核不通过）发送短信
        if (req.getCheckStatus() == 1) { // 审核通过
            sendMessage(req.getId(), 1);
        } else if (req.getCheckStatus() == 2) { // 审核不通过
            sendMessage(req.getId(), 2);
        }
    }

    /**
     * 发送短信
     *
     * @param checkId t_terminal_sku_check表的主键id
     * @param status  状态（1为通过，2为不通过）
     */
    public void sendMessage(Integer checkId, int status) {
        try {
            // 查询 terminal_shop_id、创建人、创建时间
            TerminalSkuCheck terminalSkuCheck = displayDao.selectById(checkId);
            // 查询终端店铺名称
            TerminalShopModel shopModel = terminalShopDao.selectById(terminalSkuCheck.getTerminalShopId());
            // 查询电话号码
            TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectById(terminalSkuCheck.getCreateUser());
            Integer type = accountManagerModel.getType();
            // 只给 业务代表 和 客户经理 发送短信
            if (type == 0 || type == 4) {
                boolean flag = false;
                TerminalAccountManagerModel accountManagerModel2 = null;
                // 如果是业务代表，则查询出业务代表关联的客户经理
                if (type == 4) {
                    accountManagerModel2 = terminalAccountManagerDao.selectById(accountManagerModel.getParentId());
                    if (!(accountManagerModel2 == null || accountManagerModel2.getPhone() == null)) {
                        flag = true;
                    }
                }
                String yearMonthDay = DateUtils.convert2StringYYYYMMdd(terminalSkuCheck.getCreateTime());
                // 短信模板："【国台】您好，@@的陈列拍照已审批通过。"
                StringBuilder sb = new StringBuilder();
                sb.append("【国台】您好，");
                sb.append(shopModel.getShopName());
                sb.append(yearMonthDay.substring(0, 4));// 2023-04-15
                sb.append("年");
                sb.append(yearMonthDay.substring(5, 7));
                sb.append("月");
                sb.append(yearMonthDay.substring(8, 10));
                sb.append("日");
                if (status == 1) {
                    sb.append("的陈列拍照已审批通过。");
                } else if (status == 2) {
                    sb.append("的陈列拍照审批不通过。");
                }
                // 发送短信
                if (accountManagerModel.getPhone().length() == 11) {
                    MessageUtil.sendMessageByPhoneAndContent(accountManagerModel.getPhone(), sb.toString());
                    log.info("陈列审核：创建用户：{}，审核数据（t_terminal_sku_check主键id）：{}，成功。手机号：{}。",
                            terminalSkuCheck.getCreateUser(), checkId, accountManagerModel.getPhone());
                }
                if (flag && accountManagerModel2.getPhone().length() == 11) {
                    MessageUtil.sendMessageByPhoneAndContent(accountManagerModel2.getPhone(), sb.toString());
                    log.info("陈列审核：创建用户：{}，审核数据（t_terminal_sku_check主键id）：{}，成功。手机号：{}。",
                            terminalSkuCheck.getCreateUser(), checkId, accountManagerModel2.getPhone());
                }
            } else {
                log.info("该用户类型为：{}，用户id：{}，无需发送短信！审核数据（t_terminal_sku_check主键id）：{}。",
                        accountManagerModel.getType(), accountManagerModel.getId(), checkId);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public void fixDisplayNum(TerminalSkuCheckReq req) {
        if (req.getId() == null) {
            throw new BusinessException("400", "id不能为空");
        }
        if (req.getSkuTotal() == null) {
            throw new BusinessException("400", "修正数据不能为空");
        }
        TerminalSkuCheck skuCheck = getById(req.getId());
        if (skuCheck == null) {
            throw new BusinessException("400", "id错误");
        }
        // 已经审核并且不是抽查
        if (skuCheck.getCheckStatus() > 0 && (req.getRecheck()==null || req.getRecheck()==0)) {
            throw new BusinessException("400", "已审核过的数据无法再次修正");
        }
        // 抽查
        if (req.getRecheck() == 1) {
            // 查出以前的审批结果保存下来，同时更新新的审核结果
            LambdaQueryWrapper<TerminalSkuCheck> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TerminalSkuCheck::getId, req.getId());
            TerminalSkuCheck terminalSkuCheck = displayDao.selectOne(queryWrapper);
            req.setPreRecheckSkuTotal(terminalSkuCheck.getSkuTotal());
        }
        displayDao.fixDisplayNum(req);
    }

    @Override
    public List<TerminalSkuCheckAppealResp> selectAppealDisplayList(TerminalSkuCheckReq req, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        List<TerminalSkuCheckAppealResp> terminalSkuCheckList = new ArrayList<>();
        terminalSkuCheckList = displayDao.selectAppealDisplayList3(req);
        /**
         * terminalSkuCheckList = displayDao.selectAppealDisplayList(req);
         *         List<Integer> idList = terminalSkuCheckList.stream().map(e -> e.getId()).collect(Collectors.toList());
         *         if (idList != null && idList.size() > 0) {
         *             // 查询AI陈列数
         *             List<TerminalSkuCheckResp> aiSkuList =  displayDao.selectAiSkuCountByCheckIds(idList);
         *             List<GtAiGoodsShelfSKUModel> skuModelList = terminalSkuModeDao.selectList(new QueryWrapper<GtAiGoodsShelfSKUModel>().in("check_id", idList));
         *             Map<Integer, List<GtAiGoodsShelfSKUModel>> listMap = skuModelList.stream().collect(Collectors.groupingBy(GtAiGoodsShelfSKUModel::getCheckId));
         *             terminalSkuCheckList.forEach(e->{
         *                 // AI陈列数赋值
         *                 if (aiSkuList != null && aiSkuList.size() > 0) {
         *                     for (TerminalSkuCheckResp skuCheckResp : aiSkuList) {
         *                         if (e.getId().equals(skuCheckResp.getId())) {
         *                             e.setAiSkuTotal(skuCheckResp.getAiSkuTotal());
         *                             break;
         *                         }
         *                     }
         *                 }
         *                 if(listMap.containsKey(e.getId())){
         *                     List<GtAiGoodsShelfSKUModel> skuModels = listMap.get(e.getId());
         *                     String[] displayImgArr = skuModels.stream().map(el -> el.getPhotoUrl()).toArray(String[]::new);
         *                     e.setDisplayImgList(displayImgArr);
         *                 }
         *             });
         *         }
         */
        return terminalSkuCheckList;
    }

    /**
     * 查询陈列统计数据
     *
     * @return
     */
    @Override
    public Map<String, Integer> selectAppealDisplayTotal() {
        return displayDao.selectAppealDisplayTotal();
    }

    @Override
    public List<TerminalSkuCheckResp> selectTerminalSkuCheckList(TerminalSkuCheckReq req, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        req.setCheckUser(userContext.getUserInfo().getUserId());
        req.setCheckName(userContext.getUserInfo().getUsername());
        return displayDao.selectTerminalSkuCheckList(req);
    }

    @Override
    public Map<String, Integer> findAppealDisplayTotal(TerminalSkuCheckReq req) {
        req.setCheckUser(userContext.getUserInfo().getUserId());
        req.setCheckName(userContext.getUserInfo().getUsername());
        return displayDao.findAppealDisplayTotal(req);
    }
}
