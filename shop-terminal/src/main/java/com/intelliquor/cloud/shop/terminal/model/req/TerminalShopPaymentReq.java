package com.intelliquor.cloud.shop.terminal.model.req;

import lombok.Data;

/**
 * 终端店铺 收款信息 请求参数封装
 * @Date：2023-05-06 18:15
 * @author：Panys
 * @version：1.0
 */
@Data
public class TerminalShopPaymentReq {

    /**
     * t_terminal_shop表的主键id
     */
    private Integer terminalShopId;

    private Integer receivingPaymentType; // 收款方式(0:支付宝 1:微信 2:银行)

    private String receivingPaymentName; // 收款人名称

    private String receivingPaymentAccount; // 收款人账户

    private String receivingPaymentBank; // 收款银行

    private String receivingPaymentAccountPicture; // 收款人账户图片
}
