package com.intelliquor.cloud.shop.terminal.service;

import com.intelliquor.cloud.shop.terminal.model.GtAiGoodsShelfSKUModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalDisplayPictureModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalDisplayPictureReq;

/**
 * <p>
 * 陈列拍照图片地址信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
public interface TerminalDisplayPictureService extends IService<TerminalDisplayPictureModel> {

    Integer selectReShoot(String pictureAddress);

    GtAiGoodsShelfSKUModel selectGoodsShelfSKU(String pictureAddress);
}
