package com.intelliquor.cloud.shop.terminal.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 营业执照识别结果 0失败 1成功
 *
 * <AUTHOR>
 * @since 2024/08/16 14:05
 */
@Getter
@AllArgsConstructor
public enum LicenseResultEnum {
    //回调状态（1、成功  0、失败）

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAIL(0, "失败")
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (LicenseResultEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

}
