package com.intelliquor.cloud.shop.terminal.controller.applet;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerScoreCommonModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.req.TerminalAccountManagerScoreReq;
import com.intelliquor.cloud.shop.common.model.resp.TerminalAccountManagerScoreResp;
import com.intelliquor.cloud.shop.common.service.ITerminalAccountManagerScoreCommonService;
import com.intelliquor.cloud.shop.terminal.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业务员评分表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
@RestController
@RequestMapping("/managerScore")
public class TerminalAccountManagerScoreController {

    @Resource
    private ITerminalAccountManagerScoreCommonService terminalAccountManagerScoreCommonService;

    @Resource
    private UserContext userContext;

    /**
     * 查询评分列表
     *
     * @param req   请求参数
     * @param page  当前页
     * @param limit 每页条数
     * @return RestResponse<PageInfo < TerminalAccountManagerScoreResp>>
     */
    @GetMapping("/list")
    public RestResponse<PageInfo<TerminalAccountManagerScoreResp>> getList(TerminalAccountManagerScoreReq req,
                                                                           @RequestParam(value = "page") Integer page,
                                                                           @RequestParam(value = "limit") Integer limit) {
        if (StringUtils.isBlank(req.getYearMonth())) {
            //转换为YYYY-MM格式
            req.setYearMonth(new java.text.SimpleDateFormat("yyyy-MM").format(new Date()));
        }
        req.setCurrentUserId(userContext.getTerminalModel().getId());
        List<TerminalAccountManagerScoreResp> list = terminalAccountManagerScoreCommonService.getList(req, page, limit);
        PageInfo<TerminalAccountManagerScoreResp> pageInfo = new PageInfo<>(list);
        return RestResponse.success(pageInfo);
    }

    /**
     * 保存评分
     *
     * @param req 请求参数
     * @return RestResponse<String>
     */
    @PostMapping("/save")
    public RestResponse<String> saveTerminalAccountManagerScore(@RequestBody TerminalAccountManagerScoreCommonModel req) {

        if (Objects.isNull(req.getScore())) {
            throw new BusinessException("评分不能为空");
        }
        if (Objects.isNull(req.getAttitudeScore())) {
            throw new BusinessException("服务态度评分不能为空");
        }
        if (Objects.isNull(req.getExecuteScore())) {
            throw new BusinessException("执行力评分不能为空");
        }
        if (Objects.isNull(req.getDisciplineScore())) {
            throw new BusinessException("纪律性分数不能为空");
        }

        if (Objects.isNull(req.getWorkScore())) {
            throw new BusinessException("工作质量分数不能为空");
        }

        String returnMessage = "";
        if(req.getId() != null && req.getId() > 0){
            //更新
            terminalAccountManagerScoreCommonService.updateTerminalAccountManagerScore(req);
            returnMessage = "编辑成功";
        }else{
            if (StringUtils.isBlank(req.getYearMonths())) {
                throw new BusinessException("月度不能为空");
            }
            if (Objects.isNull(req.getAccountManagerId())) {
                throw new BusinessException("业务员不能为空");
            }
            if (terminalAccountManagerScoreCommonService.count(new QueryWrapper<TerminalAccountManagerScoreCommonModel>()
                    .lambda().eq(TerminalAccountManagerScoreCommonModel::getAccountManagerId, req.getAccountManagerId())
                    .eq(TerminalAccountManagerScoreCommonModel::getYearMonths, req.getYearMonths())) > 0) {
                throw new BusinessException("同一业务员一个月只能评分一次");
            }
            terminalAccountManagerScoreCommonService.saveTerminalAccountManagerScore(req);
            returnMessage = "保存成功";
        }
        return RestResponse.success(returnMessage);



    }
}
