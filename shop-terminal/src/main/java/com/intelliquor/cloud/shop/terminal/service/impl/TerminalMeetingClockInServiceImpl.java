package com.intelliquor.cloud.shop.terminal.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.CustomException;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.UserInfo;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.terminal.dao.TerminalMeetingClockInDao;
import com.intelliquor.cloud.shop.terminal.model.TerminalActivityProjectConfigModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalMeetingClockInModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalTaskReceiveModel;
import com.intelliquor.cloud.shop.terminal.model.enums.TerminalActivityTypeEnum;
import com.intelliquor.cloud.shop.terminal.model.enums.TerminalTaskPackageStatusFlagEnum;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalMeetingClockInAdminReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalMeetingClockInReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalMeetingClockInResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalMeetingCountResp;
import com.intelliquor.cloud.shop.terminal.service.ITerminalActivityProjectConfigService;
import com.intelliquor.cloud.shop.terminal.service.ITerminalShopLevelService;
import com.intelliquor.cloud.shop.terminal.service.ITerminalTaskReceiveService;
import com.intelliquor.cloud.shop.terminal.service.TerminalMeetingClockInService;
import com.intelliquor.cloud.shop.terminal.util.DateTimeUtils;
import com.intelliquor.cloud.shop.terminal.util.TerminalDownloadCenterUtils;
import com.intelliquor.cloud.shop.terminal.util.enums.TerminalDownloadCenterEnum;
import com.intelliquor.cloud.shop.terminal.util.enums.TerminalMeetingTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 终端小程序会议打卡表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-17
 */
@Slf4j
@Service
public class TerminalMeetingClockInServiceImpl extends ServiceImpl<TerminalMeetingClockInDao, TerminalMeetingClockInModel> implements TerminalMeetingClockInService {

    @Autowired
    private TerminalMeetingClockInDao terminalMeetingClockInDao;

    @Autowired
    private TerminalDownloadCenterUtils terminalDownloadCenterUtils;
    @Autowired
    private UserContext userContext;

    @Autowired
    private ITerminalTaskReceiveService iTerminalTaskReceiveService;

    @Autowired
    private ITerminalActivityProjectConfigService terminalActivityProjectConfigService;

    @Autowired
    private ITerminalShopLevelService terminalShopLevelService;

    @Override
    public TerminalMeetingClockInModel signInClockIn(TerminalMeetingClockInModel terminalMeetingClockInModel) {
        //先判断签到打卡的必填参数是否为空
        judgeParamSignInClockIn(terminalMeetingClockInModel);

        //获取当前时间
        Date now = new Date();

        //创建时间
        terminalMeetingClockInModel.setCreateTime(now);
        //更新时间
        terminalMeetingClockInModel.setUpdateTime(now);
        //签到时间
        terminalMeetingClockInModel.setSignInTime(now);
        TerminalTaskReceiveModel terminalTaskReceiveModel = iTerminalTaskReceiveService.getOne(
                new QueryWrapper<TerminalTaskReceiveModel>().lambda()
                        .select(TerminalTaskReceiveModel::getTpId)
                        .eq(TerminalTaskReceiveModel::getBrokerId, terminalMeetingClockInModel.getCreateUser())
                        .eq(TerminalTaskReceiveModel::getDeleteFlag, false)
                        .eq(TerminalTaskReceiveModel::getStatus, TerminalTaskPackageStatusFlagEnum.VALID)
                        .last("limit 1"));
        if (!Objects.isNull(terminalTaskReceiveModel) && !Objects.isNull(terminalTaskReceiveModel.getTpId())) {
            terminalMeetingClockInModel.setTpId(terminalTaskReceiveModel.getTpId());
        }
        //必填参数不为空去添加
        if(terminalMeetingClockInDao.insert(terminalMeetingClockInModel) < 1){
            throw new BusinessException("500","添加签到打卡记录失败");
        }

        //添加成功后返回
        return terminalMeetingClockInModel;
    }

    @Override
    public List<TerminalMeetingClockInModel> selectMeetingClockInListAdmin(Integer page,Integer limit,TerminalMeetingClockInAdminReq terminalMeetingClockInAdminReq) {

        //有分页先分页
        if(null != page && null != limit){
            PageHelper.startPage(page,limit);
        }

        //直接去查询所有的会议打卡记录
        List<TerminalMeetingClockInModel> selectDataList = terminalMeetingClockInDao.selectMeetingClockInListAdmin(terminalMeetingClockInAdminReq);

        //遍历处理一下会议类型中文
        for(TerminalMeetingClockInModel selectData : selectDataList){
            //处理数据
            disposeMeetingTypeName(selectData);
        }

        //返回数据
        return selectDataList;
    }

    @Override
    public List<TerminalMeetingClockInModel> selectMeetingClockInList(Integer page, Integer limit,TerminalMeetingClockInReq terminalMeetingClockInReq) {
        //有分页先分页
        if(null != page && null != limit){
            PageHelper.startPage(page,limit,true,false,null);
        }

        //直接去查询所有的会议打卡记录
        List<TerminalMeetingClockInModel> selectDataList = terminalMeetingClockInDao.selectMeetingClockInList(terminalMeetingClockInReq);

        //遍历处理一下会议类型中文
        for(TerminalMeetingClockInModel selectData : selectDataList){
            //处理数据
            disposeMeetingTypeName(selectData);
        }

        //返回数据
        return selectDataList;
    }

    @Override
    public TerminalMeetingClockInModel selectMeetingClockInById(Integer id) {
        //查询数据
        TerminalMeetingClockInModel selectData = terminalMeetingClockInDao.selectOne(new QueryWrapper<TerminalMeetingClockInModel>().eq("id",id));

        //处理数据
        disposeMeetingTypeName(selectData);

        //返回
        return selectData;
    }

    @Override
    public void deleteMeetingByIdAdmin(Integer id) {
        //去执行假删
        TerminalMeetingClockInModel deleteModel = new TerminalMeetingClockInModel();
        //赋值
        deleteModel.setId(id);
        //假删
        deleteModel.setIsDelete(1);

        terminalMeetingClockInDao.updateById(deleteModel);
    }

    @Async
    @Override
    public void exportMeetingClockInListAdmin(TerminalMeetingClockInAdminReq terminalMeetingClockInAdminReq,
                                              UserInfo user) throws IOException {
        TerminalDownloadCenterEnum downloadEnum = TerminalDownloadCenterEnum.getTerminalDownloadCenterEnum(1);


        //异步导出 先根据导出创建一条记录
        Integer ExcelId = terminalDownloadCenterUtils.insertOrUpdateOrderDownloadCenterTerminal(user,terminalMeetingClockInAdminReq.getSelectCondition(),downloadEnum);

        ByteArrayOutputStream os = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel
                .write(os, TerminalMeetingClockInModel.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();

        WriteSheet writeSheet = EasyExcel.writerSheet("会议管理").build();

        //填入最大查询数量
        terminalMeetingClockInAdminReq.setMaxLimit(10000);

        while (true){
            //先去查询
            List<TerminalMeetingClockInModel> exportData = selectMeetingClockInListAdmin(null,null,terminalMeetingClockInAdminReq);

            //不为空才继续处理
            if(null == exportData || exportData.size() < 1){
                break;
            }

            //写入
            excelWriter.write(exportData, writeSheet);

            //之后把最小的id填入当遍历条件
            terminalMeetingClockInAdminReq.setStartId(exportData.get(exportData.size()-1).getId());
        }

        excelWriter.finish();

        String fileName = (new StringBuffer()).append("HYGL").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xlsx").toString();
        String fileAddress = terminalDownloadCenterUtils.getFileUrl(fileName, os.toByteArray());

        //生成 修改一下下载中心的状态
        terminalDownloadCenterUtils.updateOrderDownloadCenterTerminalById(ExcelId,fileAddress);
    }

    @Override
    public TerminalMeetingClockInModel signOutClockOut(TerminalMeetingClockInModel terminalMeetingClockInModel) {
        //判断
        judgeParamSignOutClockOut(terminalMeetingClockInModel);

        //赋值签退时间
        terminalMeetingClockInModel.setSignOutTime(new Date());

        //然后判断一下签退时间是不是今天
        //获取签退时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(terminalMeetingClockInModel.getSignOutTime());
        //获取签退时间的年月日
        int signOutYear = calendar.get(Calendar.YEAR);
        int signOutMonth = calendar.get(Calendar.MONTH);
        int signOutDay = calendar.get(Calendar.DAY_OF_MONTH);

        TerminalMeetingClockInModel selectData = terminalMeetingClockInDao.selectById(terminalMeetingClockInModel.getId());

        //获取签到时间
        Date signInTime = selectData.getSignInTime();
        calendar.setTime(signInTime);
        //获取签到时间的年月日
        int signInYear = calendar.get(Calendar.YEAR);
        int signInMonth = calendar.get(Calendar.MONTH);
        int signInDay = calendar.get(Calendar.DAY_OF_MONTH);

        //比较两个时间的年月日 如果不想同不允许签退
       /* if(signOutYear != signInYear || signOutMonth != signInMonth || signOutDay != signInDay){
            throw new BusinessException("400","超出当日时间 不允许签退");
        }*/

        //没问题去更新
        terminalMeetingClockInDao.updateById(terminalMeetingClockInModel);

        return terminalMeetingClockInModel;
    }

    @Override
    public TerminalMeetingClockInResp selectMeetingCount(Integer page, Integer limit, TerminalMeetingClockInReq terminalMeetingClockInReq) {

        //先处理参数
        disposeParam(terminalMeetingClockInReq);

        //创建返回集合
        TerminalMeetingClockInResp resultModel = new TerminalMeetingClockInResp();

        //先查询会议数据集合
        List<TerminalMeetingClockInModel> selectDataList = selectMeetingClockInList(page,limit,terminalMeetingClockInReq);

        //进行分页处理
        PageInfo<TerminalMeetingClockInModel> pageInfo = new PageInfo<>(selectDataList);

        //填入会议数据集合
        resultModel.setTerminalMeetingClockInModelList(pageInfo);

        //去查询数据统计
        List<TerminalMeetingCountResp> meetingCount = selectMeetingCount(terminalMeetingClockInReq);

        //填入会议统计
        resultModel.setTerminalMeetingCountResp(meetingCount);

        //返回
        return resultModel;
    }

    @Override
    public void handleSameDateData() throws Exception {
        //  1晨会 2周会 3月会
        List<String> paramStrList = terminalMeetingClockInDao.handleSameDateData();
        for (String paramStr : paramStrList) {
            String[] split = paramStr.split("=====");
            String _date = split[0];
            String meetingType = split[1];
            String createUser = split[2];
            LambdaQueryWrapper<TerminalMeetingClockInModel> lqw = Wrappers.lambdaQuery();
            lqw.apply(" DATE_FORMAT(sign_in_time,'%Y-%m-%d') = '" + _date +"' ");
            lqw.eq(TerminalMeetingClockInModel::getMeetingType, meetingType);
            lqw.eq(TerminalMeetingClockInModel::getCreateUser, createUser);
            lqw.orderByDesc(TerminalMeetingClockInModel::getSignInTime);
            List<TerminalMeetingClockInModel> inModelList = terminalMeetingClockInDao.selectList(lqw);
            for (int i = 1; i < inModelList.size(); i++) {
                TerminalMeetingClockInModel terminalMeetingClockInModel =  inModelList.get(i);
                terminalMeetingClockInModel.setRemark(terminalMeetingClockInModel.getRemark()+",数据重复"+(inModelList.size()-1)+"条手动执行"+ DateTimeUtils.getCurrentLongDate());
                terminalMeetingClockInModel.setIsDelete(1);
                terminalMeetingClockInDao.updateById(terminalMeetingClockInModel);
            }
        }
    }

    public void judgeParamSignInClockIn(TerminalMeetingClockInModel terminalMeetingClockInModel){
        //判断签到地址是否为空
        if(StringUtils.isBlank(terminalMeetingClockInModel.getSignInAddress())){
            throw new BusinessException("400","签到地址为空");
        }

        //判断会议类型是否为空
        if(null == terminalMeetingClockInModel.getMeetingType()){
            throw new BusinessException("400","会议类型为空");
        }

        //判断签到照片是否为空
        if(StringUtils.isBlank(terminalMeetingClockInModel.getSignInPicture())){
            throw new BusinessException("400","签到照片为空");
        }
    }

    public void judgeParamSignOutClockOut(TerminalMeetingClockInModel terminalMeetingClockInModel){
        //判断主键id是否为空
        if(null == terminalMeetingClockInModel.getId()){
            throw new BusinessException("400","必填的主键id为空");
        }

        //判断签退地址是否为空
        if(StringUtils.isBlank(terminalMeetingClockInModel.getSignOutAddress())){
            throw new BusinessException("400","签退地址为空");
        }

        //判断签退照片是否为空
        if(StringUtils.isBlank(terminalMeetingClockInModel.getSignOutPicture())){
            throw new BusinessException("400","签退照片为空");
        }
    }

    public void disposeMeetingTypeName(TerminalMeetingClockInModel selectData){
        //根据会议类型获取配置enum
        TerminalMeetingTypeEnum terminalMeetingTypeEnum = TerminalMeetingTypeEnum.hashMap.get(selectData.getMeetingType());
        //赋值会议类型中文
        selectData.setMeetingTypeName(terminalMeetingTypeEnum.getTypeName());
    }

    public List<TerminalMeetingCountResp> selectMeetingCount(TerminalMeetingClockInReq terminalMeetingClockInReq){
        //先创建一个返回数据
        List<TerminalMeetingCountResp> resultList = TerminalMeetingTypeEnum.arrayList;

        //查询数据统计 并且返回
        Map<Integer,TerminalMeetingCountResp> selectDataMap = terminalMeetingClockInDao.selectMeetingCount(terminalMeetingClockInReq);

        //遍历取值
        for(TerminalMeetingCountResp result : resultList){
            //根据会议类型获取
            TerminalMeetingCountResp selectData = selectDataMap.get(result.getMeetingType());
            //如果还没有这个类型的数据 默认赋值0
            if(null == selectData){
                result.setMeetingNumber(0);
            }else{
                //如果有 赋值统计出的数据
                result.setMeetingNumber(selectData.getMeetingNumber());
            }
        }

        return resultList;
    }

    public void disposeParam(TerminalMeetingClockInReq terminalMeetingClockInReq){
        //不为空才处理
        if(null != terminalMeetingClockInReq.getSelectData()){
            //先处理时间参数
            Calendar calendar = Calendar.getInstance();

            //赋值时间
            calendar.setTime(terminalMeetingClockInReq.getSelectData());
            //设置起始时间
            //时
            calendar.set(Calendar.HOUR_OF_DAY,0);
            //分
            calendar.set(Calendar.MINUTE,0);
            //秒
            calendar.set(Calendar.SECOND,0);
            //把起始时间填入
            terminalMeetingClockInReq.setStartTime(calendar.getTime());

            //获取结束时间
            //时
            calendar.set(Calendar.HOUR_OF_DAY,23);
            //分
            calendar.set(Calendar.MINUTE,59);
            //秒
            calendar.set(Calendar.SECOND,59);
            //把结束时间填入
            terminalMeetingClockInReq.setEndTime(calendar.getTime());
        }
    }

    public  Integer selectMeetingNum(String startOfDay, String endOfDay, Integer getActivityNameId, Integer companyId, Integer accountId){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startSignInTime = LocalDateTime.parse(startOfDay, formatter);
        LocalDateTime endSignInTime = LocalDateTime.parse(endOfDay, formatter);
        Integer selectMeetingNum = terminalMeetingClockInDao.selectCount(
                new QueryWrapper<TerminalMeetingClockInModel>()
                        .eq("meeting_type",getActivityNameId)
                        .ge(Objects.nonNull(startSignInTime), "sign_in_time", startSignInTime)
                        .le(Objects.nonNull(endSignInTime), "sign_in_time", endSignInTime)
                        .eq("is_delete",0)
                        .eq(Objects.nonNull("create_user"),"create_user",accountId)
        ).intValue();
        return selectMeetingNum;
    }
    @Override
    public Integer selectMeetingDayCount(TerminalActivityProjectConfigModel terminalActivityProjectConfigModel, Integer accountId){
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND,0); //这是将当天的【秒】设置为0
        calendar.set(Calendar.MINUTE,0); //这是将当天的【分】设置为0
        calendar.set(Calendar.HOUR_OF_DAY,0); //这是将当天的【时】设置为0
        String startOfDay = sdfYMD.format(calendar.getTime());

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        String endOfDay = sdfYMD.format(calendar.getTime());
        Integer companyId = terminalActivityProjectConfigModel.getCompanyId();
        return this.selectMeetingNum(startOfDay, endOfDay, terminalActivityProjectConfigModel.getActivityNameId(), companyId, accountId);
    }
    @Override
        public Integer selectMeetingWeekCount(TerminalActivityProjectConfigModel terminalActivityProjectConfigModel, Integer accountId){
                SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Calendar calendarMonday = Calendar.getInstance();
                calendarMonday.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                calendarMonday.set(Calendar.HOUR_OF_DAY, 0);
                calendarMonday.set(Calendar.MINUTE, 0);
                calendarMonday.set(Calendar.SECOND, 0);
                String startOfWeek =  sdfYMD.format(calendarMonday.getTime());

                Calendar calendarSunday = Calendar.getInstance();
                calendarSunday.set(Calendar.DAY_OF_WEEK, calendarSunday.getActualMaximum(Calendar.DAY_OF_WEEK));
                calendarSunday.add(Calendar.DAY_OF_WEEK, 1);
                calendarSunday.set(Calendar.HOUR_OF_DAY, 23);
                calendarSunday.set(Calendar.MINUTE, 59);
                calendarSunday.set(Calendar.SECOND, 59);
                String endOfWeek =  sdfYMD.format(calendarSunday.getTime());
                Integer companyId = terminalActivityProjectConfigModel.getCompanyId();
                return this.selectMeetingNum(startOfWeek, endOfWeek, terminalActivityProjectConfigModel.getActivityNameId(), companyId, accountId);
        }

    @Override
    public Integer selectMeetingMonthCount(TerminalActivityProjectConfigModel terminalActivityProjectConfigModel, Integer accountId){
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar startOfMonth = Calendar.getInstance();
        startOfMonth.set(Calendar.DAY_OF_MONTH, 1);
        startOfMonth.set(Calendar.HOUR_OF_DAY, 0);
        startOfMonth.set(Calendar.MINUTE, 0);
        startOfMonth.set(Calendar.SECOND, 0);
        String startMonth =  sdfYMD.format(startOfMonth.getTime());

        Calendar endOfMonth = Calendar.getInstance();
        endOfMonth.set(Calendar.DAY_OF_MONTH, endOfMonth.getActualMaximum(Calendar.DAY_OF_MONTH));
        endOfMonth.set(Calendar.HOUR_OF_DAY, 23);
        endOfMonth.set(Calendar.MINUTE, 59);
        endOfMonth.set(Calendar.SECOND, 59);
        String endMonth =  sdfYMD.format(endOfMonth.getTime());
        Integer companyId = terminalActivityProjectConfigModel.getCompanyId();
        return this.selectMeetingNum(startMonth, endMonth, terminalActivityProjectConfigModel.getActivityNameId(), companyId, accountId);
    }

    @Override
    public void signOutClockOutCheck(Integer id){
        TerminalMeetingClockInModel terminalMeetingClockInModel = terminalMeetingClockInDao.selectById(id);
        Date signInTime = terminalMeetingClockInModel.getSignInTime();
        Integer duration = Math.abs(DateUtils.diffMinute(signInTime, new Date()));
        // 根据会议类型校验时长
        if(terminalMeetingClockInModel.getMeetingType().equals(TerminalMeetingTypeEnum.morning_meeting.getType())) {
            // 获取配置
            TerminalActivityProjectConfigModel terminalActivityProjectConfigParams = new TerminalActivityProjectConfigModel();
            terminalActivityProjectConfigParams.setActivityTypeId(TerminalActivityTypeEnum.MEETING.getKey());
            terminalActivityProjectConfigParams.setActivityNameId(Integer.valueOf(terminalMeetingClockInModel.getMeetingType()));
            TerminalActivityProjectConfigModel terminalActivityProjectConfigModel = terminalActivityProjectConfigService.getTerminalActivityProjectConfig(terminalActivityProjectConfigParams);
            // 会议校验时长
            Integer morningMeetingDuration = terminalActivityProjectConfigModel.getDuration();
            if (Objects.isNull(terminalActivityProjectConfigModel)) {
                throw new BusinessException("未查询到配置信息");
            }
            if(duration < morningMeetingDuration) {
                throw new CustomException("会议时长未达到" + morningMeetingDuration + "分钟，提交将无法获得拜访量，是否确认要提交？");
            }
        } else {
            // 会议校验时长
            Integer meetingDuration = 60;
            if(duration < meetingDuration) {
                throw new CustomException("会议时长未超过1小时，提交将无法获得拜访量，是否确认要提交？");
            }
        }
    }
}
