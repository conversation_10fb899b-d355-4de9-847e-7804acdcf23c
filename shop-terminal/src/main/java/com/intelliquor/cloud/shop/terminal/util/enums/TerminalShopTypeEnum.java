package com.intelliquor.cloud.shop.terminal.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TerminalShopTypeEnum {

    channel_terminal(0,"烟酒终端"),
    catering_terminal(1,"餐饮终端"),
    together_shopping_terminal(2,"团购终端"),
    enterprise_terminal(3,"企业终端"),
    chain_terminal(4,"连锁终端"),
    CHAIN_CATERING_HOTEL_TERMINAL(10,"连锁型餐饮酒店"),
    FEATURED_CATERING_HOTEL_TERMINAL(11,"特色型餐饮酒店"),
    BUSINESS_CATERING_HOTEL_TERMINAL(12,"商务型餐饮酒店"),
    BANQUET_CATERING_HOTEL_TERMINAL(13,"宴席型餐饮酒店"),
    CHANNEL_CHAIN_TERMINAL(14,"渠道连锁终端"),
    ONLINE_TERMINAL(20,"线上终端"),
    SUPER_SHOP_TERMINAL(21,"商超连锁终端"),
    ;

    private Integer type;

    private String typeName;

    public static List<Map<String,Object>> arrayList = new ArrayList<>();

    public static Map<Integer, TerminalShopTypeEnum> hashMap = new HashMap<>();

    //处理两个集合
    static{
        for(TerminalShopTypeEnum terminalShopTypeEnum : TerminalShopTypeEnum.values()){
            //创建map填入List
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("postType",terminalShopTypeEnum.type);
            paramMap.put("postTypeName",terminalShopTypeEnum.typeName);
            arrayList.add(paramMap);

            //然后处理hashMap
            hashMap.put(terminalShopTypeEnum.type,terminalShopTypeEnum);
        }
    }
    public static String getNameByType(Integer type) {
        return hashMap.get(type).getTypeName();
    }
}
