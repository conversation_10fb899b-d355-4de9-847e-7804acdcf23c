package com.intelliquor.cloud.shop.terminal.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 终端拜访审核状态枚举
 *
 * <AUTHOR>
 * @since 2023/12/05 18:25
 */
@Getter
@AllArgsConstructor
public enum TerminalVisitStatusEnum {
    /**
     * 未提交
     */
    NOT_SUBMITTED(0, "未提交"),

    /**
     * 已提交
     */
    SUBMITTED(1, "已提交"),

    /**
     * 审核通过
     */
    APPROVED(2, "审核通过"),

    /**
     * 审核失败
     */
    FAILED(3, "审核失败");

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (TerminalVisitStatusEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

}
