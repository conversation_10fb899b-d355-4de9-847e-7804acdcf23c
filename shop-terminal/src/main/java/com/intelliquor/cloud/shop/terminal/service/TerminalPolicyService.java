package com.intelliquor.cloud.shop.terminal.service;

import com.intelliquor.cloud.shop.terminal.model.TerminalPolicyModel;

import java.util.List;
import java.util.Map;

public interface TerminalPolicyService{

    /**
     * 查询数据
     *
     * @return
     */
    List<TerminalPolicyModel> selectList(Map<String, Object> searchMap);

    /**
     * 查询数据
     *
     * @return
     */
    List<TerminalPolicyModel> selectList4Admin(Map<String, Object> searchMap);


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(TerminalPolicyModel model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(TerminalPolicyModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    TerminalPolicyModel getById(Integer id);
}
