package com.intelliquor.cloud.shop.terminal.model.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

@Data
public class GtAuditDataReq implements Serializable {

    private static final long serialVersionUID = 5881876511740355000L;

    /**
     * 终端id
     */
    @NotNull(message = "终端id不能为空")
    private Integer shopId;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    private Integer status;

    /**
     * 审核人姓名
     */
    @NotEmpty(message = "审核人姓名不能为空")
    private String  approvalUser;

    /**
     * 审核人电话
     */
    @NotEmpty(message = "审核人电话不能为空")
    private String  approvalPhone;

    /**
     * 终端协议id
     */
    private Integer terminalProtocolId;

    /**
     * 审核原因: JSON字符串
     */
    @NotEmpty(message = "审核原因不能为空")
    private String gtAuditResult;
}
