package com.intelliquor.cloud.shop.terminal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.enums.DeleteFlagEnum;

import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.req.TerminalProtocolActivityRelationReq;

import com.intelliquor.cloud.shop.terminal.dao.TerminalProductProtocolRelationChangeDao;
import com.intelliquor.cloud.shop.terminal.dao.TerminalProductProtocolRelationChangeDetailDao;
import com.intelliquor.cloud.shop.terminal.model.resp.*;
import com.intelliquor.cloud.shop.terminal.service.*;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;


@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TerminalProductProtocolRelationChangeDetailServiceImpl extends ServiceImpl<TerminalProductProtocolRelationChangeDetailDao, TerminalProductProtocolRelationChangeDetailModel> implements ITerminalProductProtocolRelationChangeDetailService {
    private final TerminalProductProtocolRelationChangeDetailDao terminalProductProtocolRelationChangeDetailDao;

    @Autowired
    private UserContext userContext;
    @Override
    public  TerminalProductProtocolRelationChangeDetailModel addProtocolRelationDisplayChangeDetail(DisplayProtocolResp displayProtocolResp, TerminalProtocolActivityRelationReq terminalProductProtocolRelationReq, Long changeId){
        Boolean isAddDisplayProtocol = displayProtocolResp.getIsAddDisplayProtocol();
        Boolean isUpdateDisplayProtocol = displayProtocolResp.getIsUpdateDisplayProtocol();
        Boolean isModifyDisplayProtocol = displayProtocolResp.getIsModifyDisplayProtocol();
        TerminalProtocolActivityRelationModel oldDisplayProtocolChangeDetail = displayProtocolResp.getOldDisplayProtocolChangeDetail();
        TerminalProtocolActivityRelationModel displayProductProtocolRelationModel = displayProtocolResp.getDisplayProductProtocolRelationModel();
        // 如果当前陈列协议签署标记是0，并且无当前生效协议，表示之前未签署过，变更后也没有新增该类型协议，所以不需要新增change_detail表记录，无需执行下面逻辑
        Boolean isOperateDisplayProtocolFlag = !(terminalProductProtocolRelationReq.getDisplayProtocolFlag() == 0
                && oldDisplayProtocolChangeDetail == null);
        if(isOperateDisplayProtocolFlag) {
            Boolean isDisplayProtocolFlag = Objects.nonNull(terminalProductProtocolRelationReq.getDisplayProtocolFlag());
            if (isDisplayProtocolFlag) {
                TerminalProductProtocolRelationChangeDetailModel terminalProductProtocolRelationChangeDetailModel = new TerminalProductProtocolRelationChangeDetailModel();
//              协议变更时，有可能存在只变了包量，陈列并未发生变化，所以isAddDisplayProtocol、isUpdateDisplayProtocol、isModifyDisplayProtocol都是false，需要以下前置赋值逻辑
                if (Objects.nonNull(oldDisplayProtocolChangeDetail)) {
                    BeanUtils.copyProperties(oldDisplayProtocolChangeDetail, terminalProductProtocolRelationChangeDetailModel);
                    terminalProductProtocolRelationChangeDetailModel.setNewId(oldDisplayProtocolChangeDetail.getId());
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(oldDisplayProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(oldDisplayProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldId(oldDisplayProtocolChangeDetail.getId());
                } else {
                    if (Objects.nonNull(displayProductProtocolRelationModel)) {
                        BeanUtils.copyProperties(displayProductProtocolRelationModel, terminalProductProtocolRelationChangeDetailModel);
                    }
                }
                if (isAddDisplayProtocol) {
                    terminalProductProtocolRelationChangeDetailModel.setNewId(displayProductProtocolRelationModel.getId());
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(displayProductProtocolRelationModel.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(null);
                    terminalProductProtocolRelationChangeDetailModel.setOldId(null);
                }
                if (isUpdateDisplayProtocol) {
                    terminalProductProtocolRelationChangeDetailModel.setNewId(displayProductProtocolRelationModel.getId());
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(displayProductProtocolRelationModel.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(oldDisplayProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldId(oldDisplayProtocolChangeDetail.getId());
                }
                if (isModifyDisplayProtocol) {
                    terminalProductProtocolRelationChangeDetailModel.setNewId(null);
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(null);
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(oldDisplayProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldId(oldDisplayProtocolChangeDetail.getId());
                }
                terminalProductProtocolRelationChangeDetailModel.setId(null);
                terminalProductProtocolRelationChangeDetailModel.setCreateTime(LocalDateTime.now());
                terminalProductProtocolRelationChangeDetailModel.setCreateUserId(userContext.getTerminalModel().getId());
                terminalProductProtocolRelationChangeDetailModel.setUpdateUserId(userContext.getTerminalModel().getId());
                terminalProductProtocolRelationChangeDetailModel.setUpdateTime(LocalDateTime.now());
                terminalProductProtocolRelationChangeDetailModel.setChangeId(changeId);
                terminalProductProtocolRelationChangeDetailModel.setIsDelete(DeleteFlagEnum.NOT_DELETE.getKey());
                terminalProductProtocolRelationChangeDetailModel.setCompanyId(terminalProductProtocolRelationReq.getCompanyId());
                terminalProductProtocolRelationChangeDetailDao.insert(terminalProductProtocolRelationChangeDetailModel);
                return terminalProductProtocolRelationChangeDetailModel;
            }
        }
        return null;
    }

    @Override
    public TerminalProductProtocolRelationChangeDetailModel addProtocolRelationPackageQuantityChangeDetail(PackageQuantityProtocolResp packageQuantityProtocolResp,TerminalProtocolActivityRelationReq terminalProductProtocolRelationReq, Long changeId) {
        Boolean isAddPackageQuantityProtocol = packageQuantityProtocolResp.getIsAddPackageQuantityProtocol();
        Boolean isUpdatePackageQuantityProtocol = packageQuantityProtocolResp.getIsUpdatePackageQuantityProtocol();
        Boolean isModifyPackageQuantityProtocol = packageQuantityProtocolResp.getIsModifyPackageQuantityProtocol();
        TerminalProtocolActivityRelationModel oldPackageQuantityProtocolChangeDetail = packageQuantityProtocolResp.getOldPackageQuantityProtocolChangeDetail();
        TerminalProtocolActivityRelationModel packageProductProtocolRelationModel = packageQuantityProtocolResp.getPackageProductProtocolRelationModel();
        //如果当前包量协议签署标记是0，并且无当前生效协议，表示之前未签署过，变更后也没有新增该类型协议,不需要新增change_detail表记录，无需执行下面逻辑
        Boolean isOperatePackageQuantityProtocolFlag = !(terminalProductProtocolRelationReq.getPackageQuantityProtocolFlag() == 0
                                                    && oldPackageQuantityProtocolChangeDetail == null);
        if(isOperatePackageQuantityProtocolFlag) {
            Boolean isPackageQuantityProtocolFlag = Objects.nonNull(terminalProductProtocolRelationReq.getPackageQuantityProtocolFlag());
            // 协议变更时，有可能存在只变了陈列，包量并未发生变化，所以isAddDisplayProtocol、isUpdateDisplayProtocol、isModifyPackageQuantityProtocol都是false，需要以下前置赋值逻辑
            if (isPackageQuantityProtocolFlag) {
                TerminalProductProtocolRelationChangeDetailModel terminalProductProtocolRelationChangeDetailModel = new TerminalProductProtocolRelationChangeDetailModel();
                if (oldPackageQuantityProtocolChangeDetail != null) {
                    BeanUtils.copyProperties(oldPackageQuantityProtocolChangeDetail, terminalProductProtocolRelationChangeDetailModel);
                    terminalProductProtocolRelationChangeDetailModel.setNewId(oldPackageQuantityProtocolChangeDetail.getId());
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(oldPackageQuantityProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(oldPackageQuantityProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldId(oldPackageQuantityProtocolChangeDetail.getId());
                } else {
                    if (packageProductProtocolRelationModel != null) {
                        BeanUtils.copyProperties(packageProductProtocolRelationModel, terminalProductProtocolRelationChangeDetailModel);
                    }
                }
                if (isAddPackageQuantityProtocol) {
                    terminalProductProtocolRelationChangeDetailModel.setNewId(packageProductProtocolRelationModel.getId());
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(packageProductProtocolRelationModel.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(null);
                    terminalProductProtocolRelationChangeDetailModel.setOldId(null);
                }
                if (isUpdatePackageQuantityProtocol) {
                    terminalProductProtocolRelationChangeDetailModel.setNewId(packageProductProtocolRelationModel.getId());
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(packageProductProtocolRelationModel.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(oldPackageQuantityProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldId(oldPackageQuantityProtocolChangeDetail.getId());
                }
                if (isModifyPackageQuantityProtocol) {
                    terminalProductProtocolRelationChangeDetailModel.setNewId(null);
                    terminalProductProtocolRelationChangeDetailModel.setNewProtocolCode(null);
                    terminalProductProtocolRelationChangeDetailModel.setOldProtocolCode(oldPackageQuantityProtocolChangeDetail.getActivityConfigId().toString());
                    terminalProductProtocolRelationChangeDetailModel.setOldId(oldPackageQuantityProtocolChangeDetail.getId());
                }
                terminalProductProtocolRelationChangeDetailModel.setId(null);
                terminalProductProtocolRelationChangeDetailModel.setCreateTime(LocalDateTime.now());
                terminalProductProtocolRelationChangeDetailModel.setCreateUserId(userContext.getTerminalModel().getId());
                terminalProductProtocolRelationChangeDetailModel.setUpdateTime(LocalDateTime.now());
                terminalProductProtocolRelationChangeDetailModel.setUpdateUserId(userContext.getTerminalModel().getId());
                terminalProductProtocolRelationChangeDetailModel.setChangeId(changeId);
                terminalProductProtocolRelationChangeDetailModel.setIsDelete(DeleteFlagEnum.NOT_DELETE.getKey());
                terminalProductProtocolRelationChangeDetailModel.setCompanyId(terminalProductProtocolRelationReq.getCompanyId());
                terminalProductProtocolRelationChangeDetailDao.insert(terminalProductProtocolRelationChangeDetailModel);
                return terminalProductProtocolRelationChangeDetailModel;
            }
        }
        return null;
    }

}
