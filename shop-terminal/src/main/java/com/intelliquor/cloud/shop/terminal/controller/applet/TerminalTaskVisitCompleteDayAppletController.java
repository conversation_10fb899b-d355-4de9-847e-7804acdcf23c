package com.intelliquor.cloud.shop.terminal.controller.applet;

import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalTaskVisitCompleteDayReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalTaskVisitCompleteScopeReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskVisitCompleteDayResp;
import com.intelliquor.cloud.shop.terminal.service.ITerminalTaskVisitCompleteDayService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @Auther: sunjianbu
 * @Date: 2023/4/25
 */
@Slf4j
@RestController
@RequestMapping("/terminal/visitCompleteDayApplet")
public class TerminalTaskVisitCompleteDayAppletController {

    private final ITerminalTaskVisitCompleteDayService terminalTaskVisitCompleteDayService;

    public TerminalTaskVisitCompleteDayAppletController(ITerminalTaskVisitCompleteDayService terminalTaskVisitCompleteDayService) {
        this.terminalTaskVisitCompleteDayService = terminalTaskVisitCompleteDayService;
    }

    @ApiOperation(value = "查询月度拜访列表", notes = "查询月度拜访列表", httpMethod = "GET")
    @RequestMapping(value = "/getAppMonthVisitList")
    public RestResponse<PageInfo<TerminalTaskVisitCompleteDayResp>> getAppMonthVisitList(TerminalTaskVisitCompleteDayReq filter) {
        try {
            log.info("业代任务拜访分页查询======开始, 查询条件为[{}]", Objects.nonNull(filter) ? new Gson().toJson(filter) : "");
            List<TerminalTaskVisitCompleteDayResp> list = terminalTaskVisitCompleteDayService.getMonthVisitList(filter, filter.getPageNum(), filter.getPageSize());
            PageInfo<TerminalTaskVisitCompleteDayResp> terminalTaskVisitCompleteDayRespPageInfo = new PageInfo<>(list);
            log.info("业代任务拜访分页查询======结束");
            return RestResponse.success(terminalTaskVisitCompleteDayRespPageInfo);
        } catch (Exception e) {
            log.error("业代任务拜访分页查询======失败, 查询条件为[{}]", Objects.nonNull(filter) ? new Gson().toJson(filter) : "", e);
            return RestResponse.error("业代任务拜访分页查询失败");
        }
    }

    @PostMapping("/addNewDataVisitedTheScopeBefore")
    public Response<String> addNewDataVisitedTheScopeBefore(@RequestBody TerminalTaskVisitCompleteScopeReq terminalTaskVisitCompleteScopeReq) {
        try {
            log.info("业代任务指定日期[{}]拜访完成详情======开始", terminalTaskVisitCompleteScopeReq.getStartTime(), terminalTaskVisitCompleteScopeReq.getEndTime());
            terminalTaskVisitCompleteDayService.addNewDataVisitedTheScopeDaily(terminalTaskVisitCompleteScopeReq);
            log.info("业代任务指定日期[{}]拜访完成详情======结束", terminalTaskVisitCompleteScopeReq.getStartTime(), terminalTaskVisitCompleteScopeReq.getEndTime());
            return Response.ok("业代任务指定日期拜访完成详情成功");
        } catch (Exception e) {
            log.error("业代任务指定日期[{}]拜访完成详情======失败", terminalTaskVisitCompleteScopeReq.getStartTime(), terminalTaskVisitCompleteScopeReq.getEndTime(), e);
            return Response.fail("业代任务指定日期拜访完成详情失败");
        }

    }
}
