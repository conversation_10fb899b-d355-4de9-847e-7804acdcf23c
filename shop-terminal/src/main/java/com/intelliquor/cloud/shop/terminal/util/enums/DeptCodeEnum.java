package com.intelliquor.cloud.shop.terminal.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DeptCodeEnum {

    one_code(0,"国标"),
    two_code(1,"高端酒");

    private Integer type;

    private String typeName;

    public static List<Map<String,Object>> arrayList = new ArrayList<>();

    public static Map<Integer, DeptCodeEnum> hashMap = new HashMap<>();

    //处理两个集合
    static{
        for(DeptCodeEnum terminalPostTypeEnum : DeptCodeEnum.values()){
            //创建map填入List
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("postType",terminalPostTypeEnum.type);
            paramMap.put("postTypeName",terminalPostTypeEnum.typeName);
            arrayList.add(paramMap);

            //然后处理hashMap
            hashMap.put(terminalPostTypeEnum.type,terminalPostTypeEnum);
        }
    }

}
