package com.intelliquor.cloud.shop.terminal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.terminal.model.TerminalMeetingRecordScoreModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalMeetingRecordScoreReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskMeetingCompleteScoreResp;

import java.math.BigDecimal;
import java.util.List;


/**
 * 终端小程序会议打卡得分表 服务类
 */
public interface TerminalMeetingRecordScoreService extends IService<TerminalMeetingRecordScoreModel> {

    TerminalMeetingRecordScoreModel signInMeetingRecordScore(TerminalMeetingRecordScoreModel terminalMeetingRecordScoreModel);
    TerminalMeetingRecordScoreModel signOutMeetingRecordScore(TerminalMeetingRecordScoreModel terminalMeetingRecordScoreModel);

    TerminalMeetingRecordScoreModel getTerminalMeetingRecordScoreByMcId(Integer id);

    void statisticsMeetingPoint(TerminalMeetingRecordScoreReq terminalMeetingRecordScoreReq);

    BigDecimal queryCompleteMeetingScore(int brokerId);

    TerminalTaskMeetingCompleteScoreResp getMonthMeetingScore(TerminalMeetingRecordScoreReq req);

    /**
     * 根据会议得分记录的业代id和年月，获取会议记录
     * @param brokerId
     * @param year
     * @param month
     * @return
     */
    List<TerminalMeetingRecordScoreModel> getTerminalMeetingRecordScoreByBrokerIdAndYearAndMonth(Integer brokerId, Integer year, Integer month);

    /**
     * 根据会议得分记录的业代id和年月，获取会议得分
     * @param brokerId
     * @param year
     * @param month
     * @return
     */
    BigDecimal getTerminalMeetingRecordScoreByBrokerIdAndYearAndMonthScore(Integer brokerId, Integer year, Integer month);
}
