package com.intelliquor.cloud.shop.terminal.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 任务领取状态枚举
 *
 * <AUTHOR>
 * @since 2023/11/24
 */
@Getter
@AllArgsConstructor
public enum TerminalTaskReceiveStatusEnum {

    /**
     * 未领取
     */
    NOT_RECEIVED(0, "未领取"),

    /**
     * 已领取
     */
    RECEIVED(1, "已领取"),
    ;

    private final Integer key;

    private final String value;

    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (TerminalTaskReceiveStatusEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

}
