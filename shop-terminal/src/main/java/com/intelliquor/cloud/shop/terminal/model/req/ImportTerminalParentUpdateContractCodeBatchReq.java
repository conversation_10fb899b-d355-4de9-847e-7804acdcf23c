package com.intelliquor.cloud.shop.terminal.model.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ImportTerminalParentUpdateContractCodeBatchReq {
    /**
     * 序号
     * */
    @ExcelProperty(index = 0)
    private Integer batchIndx;

    /**
     * 对象子编码（必填）
     * */
    @ExcelProperty(index = 1)
    private String deputyCode;

    /**
     * 变更上级编码（必填）
     */
    @ExcelProperty(index = 2)
    private String beforeParentCode;

    /**
     * 变更前合同编码（必填）
     */
    @ExcelProperty(index = 3)
    private String beforeContractCode;

    /**
     * 变更上级合同编码（必填）
     */
    @ExcelProperty(index = 4)
    private String afterContractCode;
}
