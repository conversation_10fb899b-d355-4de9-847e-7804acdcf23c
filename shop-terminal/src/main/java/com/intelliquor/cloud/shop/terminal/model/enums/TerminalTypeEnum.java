package com.intelliquor.cloud.shop.terminal.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2024/02/22 11:05
 */
@Getter
@AllArgsConstructor
public enum TerminalTypeEnum {

    /**
     * 核心终端
     */
    CORE_TERMINAL(1, "核心终端"),

    /**
     * 超级终端
     */
    SUPER_TERMINAL(2, "超级终端"),
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (TerminalTypeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

}
