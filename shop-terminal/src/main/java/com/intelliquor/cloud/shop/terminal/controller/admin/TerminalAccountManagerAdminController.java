package com.intelliquor.cloud.shop.terminal.controller.admin;


import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalAccountManagerAdminReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalAccountManagerReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalAccountManagerByManagerAdminResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalAccountManagerByStaffAdminResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalPostAdminResp;
import com.intelliquor.cloud.shop.terminal.service.TerminalAccountManagerService;
import com.intelliquor.cloud.shop.terminal.service.TerminalPostService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 终端小程序账户管理
 * @module 国台终端
 * */
@Slf4j
@RestController
@RequestMapping("/accountManagerAdmin")
public class TerminalAccountManagerAdminController {

    @Autowired
    private TerminalAccountManagerService terminalAccountManagerService;

    @Autowired
    private TerminalPostService terminalPostService;

    @Autowired
    private UserContext userContext;

    /**
     * 终端管理后台查询除客户经理外的所有账户
     * */
    @GetMapping("/selectTerminalAccountManagerByStaffAdmin")
    public RestResponse<PageInfo<TerminalAccountManagerByStaffAdminResp>> selectTerminalAccountManagerByStaffAdmin(TerminalAccountManagerAdminReq terminalAccountManagerAdminReq,
                                                                                                                   @RequestParam(value = "page",defaultValue = "1")Integer page,
                                                                                                                   @RequestParam(value = "limit",defaultValue = "10")Integer limit){
        try {
            //把公司参数填入
            terminalAccountManagerAdminReq.setCompanyId(userContext.getUserInfo().getIntCompanyId());
            //查询数据
            List<TerminalAccountManagerByStaffAdminResp> selectList = terminalAccountManagerService.selectTerminalAccountManagerByStaffAdmin(terminalAccountManagerAdminReq,page,limit);
            //分页处理
            PageInfo<TerminalAccountManagerByStaffAdminResp> pageInfo = new PageInfo<>(selectList);
            //返回数据
            return RestResponse.success("终端管理后台查询除客户经理外的所有账户成功",pageInfo);
        }catch (BusinessException e){
            log.error("终端管理后台查询除客户经理外的所有账户失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台查询除客户经理外的所有账户失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端管理后台根据id查询一个账户
     * */
    @GetMapping("/selectTerminalAccountManagerByStaffByIdAdmin")
    public RestResponse<TerminalAccountManagerByStaffAdminResp> selectTerminalAccountManagerByStaffByIdAdmin(@RequestParam("id")Integer id){
        try {

            //查询数据
            TerminalAccountManagerByStaffAdminResp selectData = terminalAccountManagerService.selectTerminalAccountManagerByStaffByIdAdmin(id);
            //返回数据
            return RestResponse.success("终端管理后台查询除客户经理外的所有账户成功",selectData);
        }catch (BusinessException e){
            log.error("终端管理后台查询除客户经理外的所有账户失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台查询除客户经理外的所有账户失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端管理后台修改一个账户的状态
     * */
    @GetMapping("/updateAccountStaffStatusByIdAdmin")
    public RestResponse<String> updateAccountStaffStatusByIdAdmin(@RequestParam("id")Integer id){
        try{
            //去查询
            terminalAccountManagerService.updateAccountStaffStatusById(id);
            return RestResponse.success("终端管理后台修改一个账户的状态成功");
        }catch (BusinessException e){
            log.error("终端管理后台修改一个账户的状态失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台修改一个账户的状态失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端管理后台创建账户
     * */
    @PostMapping("/insertAccountManagerAdmin")
    public RestResponse<String> insertAccountManagerAdmin(@RequestBody TerminalAccountManagerReq terminalAccountManagerReq){
        try{
            //把需要的参数获取并且填入
            //创建用户id
            terminalAccountManagerReq.setCreateUserId(userContext.getUserInfo().getUserId());
            //创建用户名称
            terminalAccountManagerReq.setCreateUserName(userContext.getUserInfo().getUsername());
            //公司id
            terminalAccountManagerReq.setCompanyId(userContext.getUserInfo().getIntCompanyId());
            //去添加
            terminalAccountManagerService.insertAccountManager(terminalAccountManagerReq);
            return RestResponse.success("终端管理后台添加一个账户成功");
        }catch (BusinessException e){
            log.error("终端管理后台添加一个账户失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台添加一个账户失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端管理后台主键id更新一个账户
     * */
    @PutMapping("/updateAccountManagerByIdAdmin")
    public RestResponse<String> updateAccountManagerByIdAdmin(@RequestBody TerminalAccountManagerReq terminalAccountManagerReq){
        try{
            //获取并且填入需要的账户信息
            //用户id
            terminalAccountManagerReq.setCreateUserId(userContext.getUserInfo().getUserId());
            //用户名称
            terminalAccountManagerReq.setCreateUserName(userContext.getUserInfo().getUsername());
            //去修改
            terminalAccountManagerService.updateAccountStaffById(terminalAccountManagerReq);
            return RestResponse.success("终端管理后台主键id更新一个账户成功");
        }catch (BusinessException e){
            log.error("终端管理后台主键id更新一个账户失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台主键id更新一个账户失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端管理后台查询所有客户经理
     * ********改成不分页
     * */
    @GetMapping("/selectTerminalAccountManagerByManagerAdmin")
    public RestResponse<List<TerminalAccountManagerByManagerAdminResp>> selectTerminalAccountManagerByManagerAdmin(@RequestParam(value = "nameOrPhone",required = false) String nameOrPhone){
        try{
            //去查询
            List<TerminalAccountManagerByManagerAdminResp> selectDataList = terminalAccountManagerService.selectTerminalAccountManagerByManagerAdmin(nameOrPhone);
            return RestResponse.success("终端管理后台查询所有客户经理成功",selectDataList);
        }catch (BusinessException e){
            log.error("终端管理后台查询所有客户经理失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台查询所有客户经理失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端管理后台查询所有可选择的岗位信息
     * */
    @GetMapping("/selectTerminalPostListAdmin")
    public RestResponse<PageInfo<TerminalPostAdminResp>> selectTerminalPostListAdmin(@RequestParam(value = "page",defaultValue = "1")Integer page,
                                                                                     @RequestParam(value = "limit",defaultValue = "10")Integer limit,
                                                                                     @RequestParam(value = "postNumberOrAddress",required = false) String postNumberOrAddress){
        try{
            //去查询
            List<TerminalPostAdminResp> selectDataList = terminalPostService.selectTerminalPostListNoSelectAdmin(page,limit,postNumberOrAddress);
            //分页
            PageInfo<TerminalPostAdminResp> pageInfo = new PageInfo<>(selectDataList);
            return RestResponse.success("终端管理后台查询所有可选择的岗位信息成功",pageInfo);
        }catch (BusinessException e){
            log.error("终端管理后台查询所有可选择的岗位信息失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台查询所有可选择的岗位信息失败",e);
            return RestResponse.error(e.getMessage());
        }
    }


    /**
     * 终端管理后台查询所有可选择的岗位信息【不分页】
     * */
    @GetMapping("/selectTerminalPostListAdminOne")
    public RestResponse<PageInfo<TerminalPostAdminResp>> selectTerminalPostListAdminOne(@RequestParam(value = "postNumberOrAddress",required = false) String postNumberOrAddress){
        try{
            //去查询
            List<TerminalPostAdminResp> selectDataList = terminalPostService.selectTerminalPostListAdminOne(postNumberOrAddress);
            //分页
            PageInfo<TerminalPostAdminResp> pageInfo = new PageInfo<>(selectDataList);
            return RestResponse.success("终端管理后台查询所有可选择的岗位信息成功",pageInfo);
        }catch (BusinessException e){
            log.error("终端管理后台查询所有可选择的岗位信息失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台查询所有可选择的岗位信息失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端小程序PC端账户账户管理导入
     * */
    @PostMapping("/importAccountManager")
    public RestResponse<String> importAccountManager(@RequestParam("file") MultipartFile multipartFile){
        try{
            //导入岗位管理
            terminalAccountManagerService.importAccountManager(multipartFile);
            //返回成功的信息
            return RestResponse.success("终端小程序PC端账户账户管理导入成功");
        }catch(BusinessException e){
            log.error("终端小程序PC端账户账户管理导入失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch(Exception e){
            log.error("终端小程序PC端账户账户管理导入失败",e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 终端管理后台查询所有客户经理
     * */
    @GetMapping("/selectAllTerminalAccountManagerByManager")
    public RestResponse<List<TerminalAccountManagerModel>> selectAllTerminalAccountManagerByManager(@RequestParam(value = "nameOrPhone",required = false) String nameOrPhone){
        try{
            //去查询
            List<TerminalAccountManagerModel> selectDataList = terminalAccountManagerService.selectAllTerminalAccountManagerByManager(nameOrPhone);
            return RestResponse.success("终端管理后台查询所有客户经理成功",selectDataList);
        }catch (BusinessException e){
            log.error("终端管理后台查询所有客户经理失败",e);
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch (Exception e){
            log.error("终端管理后台查询所有客户经理失败",e);
            return RestResponse.error(e.getMessage());
        }
    }
}
