package com.intelliquor.cloud.shop.terminal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopInfoScheduleModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopModel;
import com.intelliquor.cloud.shop.terminal.model.req.AddTerminalShopInfoScheduleReq;

import java.util.List;

/**
 * <p>
 * 终端信息附表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
public interface ITerminalShopInfoScheduleService extends IService<TerminalShopInfoScheduleModel> {


    void saveTerminalShopNotActive(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);

    void activateTerminal(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);

    void activateTerminalNewProtocol(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);

    // 酒店终端提交审核
    void activateTerminalNewProtocolByHotel(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);


    void updateTerminalShopBaseInfo(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);

    void updateTerminalShopBaseInfoNewProtocol(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);

    List<TerminalShopModel> checkExist(String contractCode, String licenseCode);

    void verifyLicense(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);

    void verifyLisenceAndContractType(AddTerminalShopInfoScheduleReq addTerminalShopInfoScheduleReq);

    String handlePhysicalCode(TerminalShopModel terminalShopModel);

    void setSamePhysicalCode(TerminalShopModel terminalShopModel, Integer isInsertOrUpdate);
}
