package com.intelliquor.cloud.shop.terminal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.TerminalScanDetailModel;
import com.intelliquor.cloud.shop.common.model.resp.TerminalScanDetailResp;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopContractProductConfigModel;
import com.intelliquor.cloud.shop.terminal.model.req.DataCountReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalLevelScanCountResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalScanBalanceAdminResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
/**
* 描述：终端扫码明细表 Dao接口
* <AUTHOR>
* @date 2022-08-17
*/
@Mapper
public interface TerminalScanDetailDao  extends BaseMapper<TerminalScanDetailModel> {

    //TODO 注意： 联盟和终端通用的方法写在 TerminalScanDetailCommonDao 中 !!!!!!!

    /**
     * 获取历史收货记录中的商品信息
     * @param param
     * @return
     */
    List<TerminalScanDetailResp> getScanHistoryGoodsList(Map<String, Object> param);

    List<TerminalLevelScanCountResp> selectTerminalScanCount(@Param("shopIdList")List<Integer> shopIdList,
                                                             @Param("productList")List<TerminalShopContractProductConfigModel> productList,
                                                             @Param("dataCountReq") DataCountReq dataCountReq);

    /**
     * 终端小程序后台列表
     * @return
     */
    List<TerminalScanBalanceAdminResp> selectListByBalanceIds(Map<String, Object> param);


    TerminalScanDetailModel selectOneByBalanceId(Integer balanceId);


}
