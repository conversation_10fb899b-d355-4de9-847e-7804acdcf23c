package com.intelliquor.cloud.shop.terminal.service;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.model.CloudDealerInfoModel;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalBanquetGoodsModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalBanquetModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalBanquetReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalBanquetFailReasonResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalBanquetOperatorResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalBanquetResp;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface TerminalBanquetService {

    /**
     * 查询数据
     *
     * @return
     */
    List<TerminalBanquetModel> selectList(Map<String, Object> searchMap);


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(TerminalBanquetModel model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(TerminalBanquetModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    TerminalBanquetModel getById(Integer id);

    /**
     * 查询数据
     *
     * @return
     */
    PageInfo<TerminalBanquetResp> selectList4App(TerminalBanquetReq req);

    /**
     * 获取经销商列表
     * @param req
     * @return
     */
    PageInfo<CloudDealerInfoModel> getDealerList(TerminalBanquetReq req);

     // 获取经1销商列表
    List<CloudDealerInfoModel> queryDealerList(TerminalBanquetReq req);

    /**
     * 获取经销商关联的终端列表
     * @param req
     * @return
     */
    PageInfo<ShopModel> getShopListByPage(TerminalBanquetReq req);

    /**
     * 重新提报
     * @param model
     */
    void againSave(TerminalBanquetModel model);

    /**
     * 获取宴席用酒
     * @return
     */
    List<TerminalBanquetGoodsModel> getGoodsList(Map<String, Object> param);

    void sendBalanceData(TerminalBanquetModel sendData);

    void updateBanquetStatus(String reqData,HttpServletRequest httpServletRequest);

    TerminalBanquetFailReasonResp selectFailReason(Integer banquetId);

    /**
     *  查询操作人信息
     * @param id
     * @return
     */
    TerminalBanquetOperatorResp getOperaterById(Integer id);
}
