package com.intelliquor.cloud.shop.terminal.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.model.AreaModel;
import com.intelliquor.cloud.shop.common.utils.BeanUtil;
import com.intelliquor.cloud.shop.common.valid.RegisterGroupValid;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class TerminalDealerInfoModelReq {

    private static final long serialVersionUID = 8329373453326634057L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 公司 ID
     */
    @NotNull(message = "商户id必须指定", groups = {RegisterGroupValid.class})
    private Integer companyId;

    /**
     * 用户微信openId
     */
    private String openId;

    /**
     * 客户类型 1-经销商 2-分销商
     */
    private Integer type;

    /**
     * 客户编码
     */
    private String dealerCode;

    /**
     * 客户名称
     */
    private String dealerName;

    /**
     * 经销商类型（客户类型为经销商时存在）
     */
    private Integer dealerType;

    /**
     * 经销商类型邀请码（客户类型为经销商时存在）
     */
    private String dealerInviteCode;

    /**
     * 终端店邀请码
     */
    private String shopInviteCode;

    /**
     * 上级客户 ID（当客户类型为分销商时存在）
     */
    private Integer superiorId;

    /**
     * 客户业务区域
     */
    private Integer dealerArea;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系电话
     */
    @NotBlank(message = "手机号必须指定", groups = {RegisterGroupValid.class})
    private String phone;

    /**
     * 地址
     */
    private String address;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 客户状态 0-停用 1-启用
     */
    private Integer status;

    /**
     * 删除状态 0-已删除 1-未删除
     */
    private Integer isDelete;

    /**
     * 业务区域名称
     */
    private String busAreaName;

    /**
     * 微信名称
     */
    private String weChartName;

    /**
     * 头像图片路径
     */
    private String headImgUrl;

    /**
     * 经销商微信群图片路径
     */
    private String dealerWeChartGroupImgUrl;

    /**
     * 终端店微信群图片路径
     */
    private String shopWeChartGroupImgUrl;

    /**
     * 省
     */
    private String provinces;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    @ApiModelProperty(value = "街道")
    private String street;

    /**
     * 经销商类型名称
     */
    private String dealerTypeName;

    /**
     * 状态名
     */
    private String statusName;

    /**
     * 业务区域路径集合
     */
    private String dealerAreaList;

    /**
     * 完整地址
     */
    private String fullAddress;

    /**
     * 账户余额
     */
    private BigDecimal accountBalance;

    @ApiModelProperty(value = "国台经销商数据id")
    private String gtDealerId;

    @ApiModelProperty(value = "国台经销商数据json")
    private String gtDealerData;

    /**
     * 账户积分
     */
    private Integer score;

    /**
     * 使用积分
     */
    private Integer useScore;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 人员id
     */
    private Integer staffId;

    /**
     * 分销商等级,经销商为0
     */
    private Integer level;

    private List<AreaModel> areaModelList;

    @ApiModelProperty(value = "门店图片")
    private String storesImg;

    @ApiModelProperty(value = "合同图片")
    private String contractImg;

    @ApiModelProperty(value = "营业执照图片")
    private String licenseImg;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 检查编号是否重复
     */
    private Integer checkExist=1;

    /**
     * 用于判断如何获取联盟的方式，0-用手机号获取联盟 1-用经销商编码获取
     */
    private Integer queryFlag = 0;

    private Integer accountType;

    private String licenseCode = "";

}