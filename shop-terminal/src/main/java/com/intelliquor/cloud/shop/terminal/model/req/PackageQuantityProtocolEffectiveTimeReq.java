package com.intelliquor.cloud.shop.terminal.model.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: mali
 * @CreateTime: 2024-02-22
 */
@Data

public class PackageQuantityProtocolEffectiveTimeReq {



    @NotNull(message = "终端id不能为空")
    private Long terminalShopId;


    private String protocolProductCode;

    private Integer marketType;

    private String protocolCode;

    private Integer companyId;

    private String levelCode;

    private Integer yearScanInNum;
}
