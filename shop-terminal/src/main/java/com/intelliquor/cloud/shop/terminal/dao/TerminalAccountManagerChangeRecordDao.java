package com.intelliquor.cloud.shop.terminal.dao;

import com.intelliquor.cloud.shop.terminal.model.TerminalAccountManagerChangeRecordModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalAccountManagerChangeRecordAdminReq;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 账户代表变更记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Repository
public interface TerminalAccountManagerChangeRecordDao extends BaseMapper<TerminalAccountManagerChangeRecordModel> {

    List<TerminalAccountManagerChangeRecordModel> selectChangeRecordListAdmin(TerminalAccountManagerChangeRecordAdminReq terminalAccountManagerChangeRecordAdminReq);

}
