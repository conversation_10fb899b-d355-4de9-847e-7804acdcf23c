package com.intelliquor.cloud.shop.terminal.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 拜访状态枚举
 */
@Getter
@AllArgsConstructor
public enum VisitStatusEnum {

    NOT_SUBMITTED(0, "未提交"),
    SUBMITTED(1, "已提交"),
    APPROVE_PASS(2, "审核通过"),
    APPROVE_FAIL(3, "审核失败");

    private final Integer value;
    private final String desc;

    static Map<Integer, VisitStatusEnum> enumMap = new HashMap<Integer, VisitStatusEnum>();

    static {
        for (VisitStatusEnum type : VisitStatusEnum.values()) {
            enumMap.put(type.getValue(), type);
        }
    }

    public static VisitStatusEnum getEnum(Integer code) {
        return enumMap.get(code);
    }
}
