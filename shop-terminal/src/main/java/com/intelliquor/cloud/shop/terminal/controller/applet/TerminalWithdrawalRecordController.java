package com.intelliquor.cloud.shop.terminal.controller.applet;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.TerminalWithdrawalRecordModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalShopPaymentReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalWithdrawalListReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalWithdrawalRecordReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopInfoResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalWithdrawalRecordResp;
import com.intelliquor.cloud.shop.terminal.service.TerminalWithdrawalExamineService;
import com.intelliquor.cloud.shop.common.service.TerminalWithdrawalRecordImgService;
import com.intelliquor.cloud.shop.terminal.service.TerminalWithdrawalRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 终端积分提现 小程序端 控制器类
 * @Date：2023-05-10 10:53
 * @author：Panys
 * @version：1.0
 */
@Slf4j
@RestController
@RequestMapping("/terminalWithdrawalRecord")
public class TerminalWithdrawalRecordController {

    @Autowired
    private UserContext userContext;

    @Autowired
    private TerminalWithdrawalExamineService terminalWithdrawalExamineService;

    @Autowired
    private TerminalWithdrawalRecordService terminalWithdrawalRecordService;

    @Autowired
    private TerminalWithdrawalRecordImgService terminalWithdrawalRecordImgService;

    /**
     * 根据shop_id查询终端是否可以提现
     * @param shopId 终端店铺id（t_member_shop的主键id）
     * @return 响应对象
     */
    @RequestMapping("/getTerminalWithdrawalStatusByShopId")
    public RestResponse getTerminalWithdrawalStatusByShopId(@RequestParam(value = "shopId")Integer shopId) {
        Integer result =  terminalWithdrawalExamineService.getTerminalWithdrawalStatusByShopId(shopId);
        return result == 1 ? RestResponse.success("该终端可提现！") : RestResponse.error(400, "该终端不可提现！");
    }

    /**
     * 根据shop_id查询收款信息、虚拟贷款信息
     * @param shopId shop_id
     * @return 响应对象
     */
    @RequestMapping("/getTerminalPaymentInfoByShopId")
    public RestResponse<TerminalShopInfoResp> getTerminalPaymentInfoByShopId(@RequestParam(value = "shopId")Integer shopId) {
        TerminalShopInfoResp resp = terminalWithdrawalExamineService.getTerminalPaymentInfoByShopId(shopId);
        return RestResponse.success(resp);
    }

    /**
     * 更新终端信息表（t_terminal_shop）中的收款信息
     * @param req 请求参数
     * @return 响应对象
     */
    @RequestMapping("/updateTerminalShopPayment")
    public RestResponse updateTerminalShopPayment(@RequestBody TerminalShopPaymentReq req){
        Integer result = terminalWithdrawalExamineService.updateTerminalShopPayment(req);
        return result > 0 ? RestResponse.success("更新成功！") : RestResponse.error(400, "更新失败！");
    }

    /**
     * 终端用户提现积分
     * @param req 请求参数封装
     * @return 响应结果
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public RestResponse<String> saveData(@RequestBody TerminalWithdrawalRecordReq req) {
        req.setUserId(userContext.getTerminalModel().getId()); // 获取当前登录用户id
        req.setLinkman(userContext.getTerminalModel().getName()); // 获取当前登录用户名
        terminalWithdrawalRecordService.saveData(req);
        return RestResponse.success("积分提现提交成功！");
    }

    /**
     * 国台终端小程序端，客户经理 终端提现列表 请求参数封装
     * @param req 请求参数
     * @return 响应结果
     */
    @RequestMapping("/getTerminalWithdrawalRecordList")
    public PageResponse<List<TerminalWithdrawalRecordModel>> getTerminalWithdrawalRecordList(@RequestBody TerminalWithdrawalListReq req) {
        req.setUserId(userContext.getTerminalModel().getId());
        PageInfo<TerminalWithdrawalRecordModel> pageInfo = terminalWithdrawalRecordService.getTerminalWithdrawalRecordList(req);
        return PageResponse.ok(pageInfo);
    }

    /**
     * 根据主键id查询积分提现详情信息
     * @param id 请求参数
     * @return 响应结果
     */
    @RequestMapping("/getTerminalWithdrawalRecordById")
    public Response<TerminalWithdrawalRecordResp> getTerminalWithdrawalRecordById(@RequestParam("id") Integer id) {
        TerminalWithdrawalRecordResp resp = terminalWithdrawalRecordService.getTerminalWithdrawalRecordById(id);
        return Response.ok(resp);
    }

    /**
     * 客户经理提交审核
     * @param req 请求参数
     * @return 响应结果
     */
    @RequestMapping("/accountManagerSave")
    public RestResponse<String> accountManagerSave(@RequestBody TerminalWithdrawalRecordReq req) {
        req.setAccountManagerId(userContext.getTerminalModel().getId());
        req.setAccountManagerName(userContext.getTerminalModel().getName());
        req.setAccountManagerPhone(userContext.getTerminalModel().getPhone());
        terminalWithdrawalRecordService.accountManagerExamine(req);
        return RestResponse.success("客户经理办理成功！");
    }

    /**
     * 根据主键id删除凭证图片
     * @param id 主键id
     * @return 响应结果
     */
    @RequestMapping("/deleteVoucherImgById")
    public RestResponse<String> deleteVoucherImgById(@RequestParam("id") Integer id) {
        log.info("用户，id：{}，名称：{}，删除凭证图片，主键id（t_terminal_withdrawal_record_img）为：{}", userContext.getTerminalModel().getId(), userContext.getTerminalModel().getName(), id);
        terminalWithdrawalRecordImgService.deleteVoucherImgById(id);
        return RestResponse.success("删除凭证图片成功！");
    }

    /**
     * 客户经理拒绝
     * @param req
     * @return
     */
    @PostMapping("rejectTerminalWithdrawalRecordModel")
    public RestResponse<String> rejectTerminalWithdrawalRecordModel(@RequestBody TerminalWithdrawalRecordReq req) {
        try {
            terminalWithdrawalRecordService.rejectTerminalWithdrawalRecordModel(req);
            return RestResponse.success("终端提现拒绝成功");
        } catch (Exception e) {
            return RestResponse.error(e.getMessage());
        }
    }
}
