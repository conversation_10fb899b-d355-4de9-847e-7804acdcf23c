package com.intelliquor.cloud.shop.terminal.model.req;

import lombok.Data;

@Data
public class TerminalParentUpdateReq {

    /**
     * 变更开始时间
     */
    private String startDate;
    /**
     * 变更结束时间
     */
    private String endDate;
    /**
     * 变更状态
     */
    private Integer updateResult;
    /**
     * 操作类型
     */
    private Integer operateType;
    /**
     * 操作员
     */
    private String createUserName;
    /**
     * 批次号
     */
    private String batchNo;
    private Long batchId;
    /**
     * 终端编码
     */
    private String deputyCode;
    /**
     * 经销商名称
     */
    private String dealerName;
    /**
     * 经销商编码
     */
    private String dealerCode;
    /**
     * 分销商名称
     */
    private String distributorName;
    /**
     * 分销商编码
     */
    private String distributorCode;
    /**
     * 终端名称
     */
    private String shopName;

    /**
     * 终端id
     */
    private Integer shopId;

    /**
     * 变更前分销商名称
     */
    private String beforeDistributorParentName;

    /**
     * 变更前分销商编码
     */
    private String beforeDistributorParentCode;

    /**
     * 变更前经销商名称
     */
    private String beforeDealerParentName;

    /**
     * 变更前经销商编码
     */
    private String beforeDealerParentCode;

    /**
     * 最大查询条数
     * */
    private Integer maxLimit;
    /**
     * 起始id
     * */
    private Integer startId;
}
