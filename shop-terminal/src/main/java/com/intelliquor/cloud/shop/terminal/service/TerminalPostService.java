package com.intelliquor.cloud.shop.terminal.service;

import com.intelliquor.cloud.shop.terminal.model.TerminalPostModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalPostReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalPostAdminResp;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 终端小程序岗位管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public interface TerminalPostService extends IService<TerminalPostModel> {

    void insertTerminalPostAdmin(TerminalPostModel terminalPostModel);

    List<TerminalPostModel> selectTerminalPostListAdmin(Integer page,Integer limit,TerminalPostReq terminalPostReq);

    void updateTerminalPostAdminById(TerminalPostModel terminalPostModel);

    List<TerminalPostAdminResp> selectTerminalPostListNoSelectAdmin(Integer page,Integer limit,String postNumberOrAddress);
    List<TerminalPostAdminResp> selectTerminalPostListAdminOne(String postNumberOrAddress);

    void importTerminalPost(MultipartFile multipartFile) throws IOException;
;
}
