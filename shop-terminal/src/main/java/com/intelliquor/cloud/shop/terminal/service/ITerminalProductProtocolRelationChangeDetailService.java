package com.intelliquor.cloud.shop.terminal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.TerminalProductProtocolRelationChangeDetailModel;
import com.intelliquor.cloud.shop.common.model.TerminalProductProtocolRelationChangeModel;
import com.intelliquor.cloud.shop.common.model.req.TerminalProtocolActivityRelationReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalProductProtocolRelationChangeReq;
import com.intelliquor.cloud.shop.terminal.model.resp.DisplayProtocolResp;
import com.intelliquor.cloud.shop.terminal.model.resp.PackageQuantityProtocolResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProductProtocolRelationChangeDetailResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProductProtocolRelationChangeResp;

import java.util.List;

public interface ITerminalProductProtocolRelationChangeDetailService extends IService<TerminalProductProtocolRelationChangeDetailModel> {
    TerminalProductProtocolRelationChangeDetailModel addProtocolRelationPackageQuantityChangeDetail(PackageQuantityProtocolResp packageQuantityProtocolResp, TerminalProtocolActivityRelationReq terminalProductProtocolRelationReq, Long changeId);

    TerminalProductProtocolRelationChangeDetailModel addProtocolRelationDisplayChangeDetail(DisplayProtocolResp displayProtocolResp, TerminalProtocolActivityRelationReq terminalProductProtocolRelationReq, Long changeId);
}
