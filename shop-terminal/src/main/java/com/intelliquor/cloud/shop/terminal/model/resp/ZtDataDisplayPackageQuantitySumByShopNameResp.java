package com.intelliquor.cloud.shop.terminal.model.resp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ZtDataDisplayPackageQuantitySumByShopNameResp {
    private static final long serialVersionUID = -3733933339764150733L;

    // id
    private Integer id;

    // 终端店铺ID
    private Integer terminalShopId;

    // 客户名称
    private String shopName;

    // 待入账
    private BigDecimal toBeAccountedAmount;

    // 已入账
    private BigDecimal accountedAmount;

    // 终端副编码
    private String deputyCode;

    // 上账状态
    private Integer accountStatus;

    // 发放状态名称
//    private String isScoreName;
}
