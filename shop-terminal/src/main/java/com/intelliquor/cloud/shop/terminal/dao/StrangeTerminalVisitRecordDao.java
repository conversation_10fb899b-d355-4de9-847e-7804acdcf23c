package com.intelliquor.cloud.shop.terminal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.terminal.model.StrangeTerminalVisitRecordModel;
import com.intelliquor.cloud.shop.terminal.model.dto.StrangeTerminalVisitRecordDto;
import com.intelliquor.cloud.shop.terminal.model.dto.StrangeTerminalVisitRecordSatisticsDto;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskStatisticsResp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Auther: sunjianbu
 * @Date: 2023/7/6
 */
@Repository
public interface StrangeTerminalVisitRecordDao extends BaseMapper<StrangeTerminalVisitRecordModel> {
    /**
     * 获取陌生终端拜访详情
     */
    StrangeTerminalVisitRecordModel getStrangeTerminalVisitRecordById(Integer id);

    /**
     * 统计范围时间内陌生终端点数
     * @param startDate 起始时间
     * @param endDate 截止时间
     * @return
     */
    List<StrangeTerminalVisitRecordDto> statisticsStrangeTerminalPoint(String startDate, String endDate);

    /**
     * 统计范围时间内陌生终端点数
     * @param id 记录id
     * @return
     */
    List<StrangeTerminalVisitRecordDto> statisticsStrangeTerminalPointById(Integer id);

    List<TerminalTaskStatisticsResp> getStrangeVisitStatistics(@Param("year")Integer year, @Param("brokerId")Integer brokerId);

    /**
     * 统计范围时间内陌生终端点数
     * @param tmId t_terminal_task_monthly表Id
     * @return
     */
    List<StrangeTerminalVisitRecordDto> statisticsStrangeTerminalPointByTmId(@Param("tmId")Integer tmId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 统计范围时间内业代的陌生终端拜访次数
     * @param userId 业代id
     * @param month 月份
     * @return
     */
    StrangeTerminalVisitRecordSatisticsDto statisticsStrangeTerminalVisitNum(@Param("userId")Integer userId, @Param("month")String month);
}
