package com.intelliquor.cloud.shop.terminal.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.model.TerminalProductProtocolConfigModel;
import com.intelliquor.cloud.shop.common.model.TerminalProtocolModel;
import com.intelliquor.cloud.shop.common.model.resp.TerminalProductProtocolConfigResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolActiveRelationResp;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 描述：终端拜访记录实体类
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Data
@TableName("t_terminal_visit_record")
public class TerminalVisitRecordModel {


    private Integer id;
    /**
     * 终端id t_member_shop表的id
     */
    private Integer shopId;

    /**
     * 签到省
     */
    private String signProvince;

    /**
     * 签到市
     */
    private String signCity;

    /**
     * 签到区
     */
    private String signDistinct;

    /**
     * 签到详细地址
     */
    private String signAddress;

    /**
     * 签到经度
     */
    private Double signLongitude;

    /**
     * 签到纬度
     */
    private Double signLatitude;

    /**
     * 现场拍照，多个逗号分割
     */
    private String signImg;

    /**
     * 店内陈列拍照，多个逗号分割
     */
    private String shopImg;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDelete;

    private Integer createUserId;

    /**
     * 创建人类型0-客户经理1-经销商人员2-终端人员 3-采集人员4-业务代表
     */
    private Integer createUserType;

    /**
     * 签到时间 年月日时分秒
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 签到日期 年月日
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createDate;

    private Integer companyId;

    private String shopName;


    private String isMember;

    /**
     * 签到门头照
     */
    private String signHeadImg;

    /**
     * 签退时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signOutTime;

    /**
     * 签退省
     */
    private String signOutProvince;

    /**
     * 签退市
     */
    private String signOutCity;

    /**
     * 签退区
     */
    private String signOutDistinct;

    /**
     * 签退详情地址
     */
    private String signOutAddress;

    /**
     * 签退经度
     */
    private String signOutLongitude;

    /**
     * 签退纬度
     */
    private String signOutLatitude;

    /**
     * 签退门头照
     */
    private String signOutHeadImg;

    /**
     * 拜访总结
     */
    private String summary;

    /**
     * 拜访时长（分），签退时间-签到时间
     */
    private Integer duration;

    /**
     * 拜访状态 0-未提交 1-已提交 2审核通过 3审核失败
     */
    private Integer status;

    /**
     * 陈列检查拍照
     */
    private String displayImg;

    /**
     * 政策海报照片
     */
    private String policyPosterImg;

    /**
     * 政策台卡照片
     */
    private String policyCardImg;

    /**
     * 政策宣讲照片
     */
    private String policyExplainImg;

    /**
     * 对账单照片
     */
    private String verifyAccountImg;

    /**
     * 竞品对策内容
     */
    private String competePolicy;

    /**
     * 数据填报-产品库存
     */
    List<TerminalVisitRecordDataModel> stockList;

    /**
     * 数据填报-竞品库存
     */
    List<TerminalVisitRecordDataModel> competeList;

    /**
     * 数据填报-宴席计划
     */
    List<TerminalVisitRecordDataModel> banquetList;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 拜访人名称
     */
    private String createUserName;
    /**
     * 拜访人手机号
     */
    private String createUserPhone;

    /**
     * 岗位名称
     */
    private Integer postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 客户经理Id
     */
    private Integer accountManagerId;

    /**
     * 终端信息
     */
    private TerminalShopModel shopModel;

    /**
     * 配置的主协议
     */
    private TerminalProtocolModel mainProtocol;

    /**
     * 配置2024年协议
     */
    private TerminalProductProtocolConfigModel productProtocolConfig;

    private TerminalProductProtocolConfigResp productProtocolConfigResp;

    /**
     * 陈列审核
     */
    private TerminalSkuCheck skuCheck;

    /**
     * 上传的照片列表
     */
    private List<GtAiGoodsShelfSKUModel> skuPhotoList;

    /**
     * 陈列申诉列表
     */
    private List<TerminalSkuCheckAppealModel> skuCheckAppealList;

    /**
     * 现场视频
     */
    private String liveVideo;

    /**
     * 进店后经度
     */
    private Double inStoreLongitude;

    /**
     * 进店后纬度
     */
    private Double inStoreLatitude;

    // 同步标识(0:未同步;1:已同步;2:同步失败)
    @TableField(value = "sys_state")
    private Integer sysState;

    //同步时间
    @TableField(value = "sys_date")
    private Date sysDate;

    //同步备注
    @TableField(value = "sys_remark")
    private String sysRmark;

    /**
     * 审核时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 审核人
     */
    private Long checkUser;

    /**
     * 审核意见
     */
    private String checkRemark;

    /**
     * 审核人姓名
     */
    @TableField(exist = false)
    private String checkUserName;

    /**
     * 审核人电话
     */
    @TableField(exist = false)
    private String checkUserPhone;

    /**
     * t_terminal_task_package主键
     */
    private Integer tpId;

    private Integer stepFlag;

    @TableField(exist = false)
    private Double displayDistance;


    /**
     * 签到时间 年月日时分秒
     * 2023/9/25 新增，处理签到时间问题
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date saveTime;

    @TableField(exist = false)
    private List<TerminalProtocolActiveRelationResp> terminalProtocolActiveRelationListResp;

    @TableField(exist = false)
    private Integer displaySurfaceTotal;
}
