package com.intelliquor.cloud.shop.terminal.controller.open;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.utils.GuotaiUtil;
import com.intelliquor.cloud.shop.terminal.model.req.ContractChangeReq;
import com.intelliquor.cloud.shop.terminal.service.TerminalShopService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @Author: MAX
 * @CreateTime: 2024-01-23  15:26
 */
@Slf4j
@RestController
@RequestMapping("/contract/dealer")
public class DealerContractChangeController {

    @Resource
    private TerminalShopService terminalShopService;

    /**
     * 经销商合同变更
     *
     * @param req 请求参数
     * @return RestResponse
     */
    @PostMapping("/change")
    public RestResponse dealerContractChange(@Valid @RequestBody ContractChangeReq req,
                                       HttpServletRequest httpServletRequest) {
        String token = httpServletRequest.getHeader("token");
        String timestampStr = httpServletRequest.getHeader("timestamp");
        //校验Token
        try {
            GuotaiUtil.verifyToken(token, timestampStr);
        } catch (BusinessException e) {
            return RestResponse.error(e.getMessage());
        }
        terminalShopService.dealerContractChange(req);
        return RestResponse.success("合同变更成功");
    }
}
