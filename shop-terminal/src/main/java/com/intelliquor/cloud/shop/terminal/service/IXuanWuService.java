package com.intelliquor.cloud.shop.terminal.service;

import com.intelliquor.cloud.shop.common.model.resp.QueryReceiveDataResp;
import com.intelliquor.cloud.shop.common.model.resp.QueryTerminalProtocolDataResp;
import com.intelliquor.cloud.shop.terminal.model.req.QueryReceiveInfoReq;
import com.intelliquor.cloud.shop.terminal.model.resp.QueryReceiveInfoResp;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IXuanWuService {

    /**
     * 查询收货详细
     * @param req
     * @return
     */
    QueryReceiveInfoResp queryReceiveInfo(QueryReceiveInfoReq req);

    /**
     * 该订单下是否存在赠酒订单
     * @param mainOrderCodeList
     * @return
     */
    Boolean queryGiftOrderByMainOrderCode(List<String> mainOrderCodeList);

    /**
     * 查询收货数据
     * @param terminalCode
     * @param goodsCode
     * @return
     */
    List<QueryReceiveDataResp> queryReceiveData(String terminalCode, String goodsCode);

    /**
     * 查询终端包量协议信息
     * @param terminalCode
     * @return
     */
    List<QueryTerminalProtocolDataResp> queryTerminalProtocolData(String terminalCode);
}
