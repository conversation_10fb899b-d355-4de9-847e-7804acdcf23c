package com.intelliquor.cloud.shop.terminal.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 未分配终端
 */
@Data
public class UnabsorbedDealerResp {

    /**
     * 主任务id
     */
    private Integer id;

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     *经销商名称
     */
    private String dealerName;

    /**
     * 终端id
     */
    private Integer memberShopId;

    /**
     * 终端等级
     */
    private String levelName;

    /**
     * 终端名称
     */
    private String name;

    /**
     * 终端负责人
     */
    private String linkman;

    /**
     * 终端地址
     */
    private String address;

    /**
     * terminalShop表id
     */
    private Integer terminalShopId;

    /**
     * 终端负责人电话
     */
    private String linkPhone;

    /**
     * 终端采集人id
     */
    private String collectId;

    /**
     * 终端采集人名称
     */
    private String collectName;

    /**
     * 终端采集人电话
     */
    private String collectPhone;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 任务编号
     */
    private String taskNum;

    /**
     * 任务创建人名称
     */
    private String taskCreateByName;

    /**
     * 任务创建人电话
     */
    private String taskCreateByPhone;
}
