package com.intelliquor.cloud.shop.terminal.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 描述：宴席开瓶扫瓶码数据实体类
 *
 * <AUTHOR>
 * @date 2022-08-11
 */
@Data
public class TerminalBanquetOpenScanModel {


    private Integer id;

    /**
     * 宴席id
     */
    private Integer banquetId;

    /**
     * 扫的原码
     */
    private String originalQrcode;

    /**
     * 解析后的码
     */
    private String qrcode;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    private Integer createUserId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Integer updateUserId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否删除0-未删除 1-已删除
     */
    private Integer isDelete;

    /**
     * 扫码状态0-正常 1-异常
     */
    private Integer scanStatus = 0;
    /**
     * 扫码描述
     */
    private String scanDesc;

    /**
     * 扫码异常原因
     */
    private String errMsg;

    /**
     * 箱码
     */
    private String codeXiang;

    /**
     * 登录人手机号
     */
    private String createUserPhone;
    /**
     * 码所属的经销商
     */
    private String dealerCode;
}