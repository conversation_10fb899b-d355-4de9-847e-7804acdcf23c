package com.intelliquor.cloud.shop.terminal.dao;

import com.intelliquor.cloud.shop.terminal.model.TerminalProtocolRelationModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolRelationResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_terminal_protocol_relation(终端关联的协议表)】的数据库操作Mapper
* @createDate 2023-02-17 10:32:36
* @Entity com.intelliquor.cloud.shop.terminal.model.TerminalProtocolRelation
*/
@Mapper
public interface TerminalProtocolRelationDao extends BaseMapper<TerminalProtocolRelationModel> {

    /**
     * 查询终端关联的协议列表
     * @param terminalId
     * @return
     */
    List<TerminalProtocolRelationResp> selectTerminalProductProtocolListByTerminalId(@Param("terminalId") Integer terminalId);
}




