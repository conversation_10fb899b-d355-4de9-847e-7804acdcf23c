package com.intelliquor.cloud.shop.terminal.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: MAX
 * @CreateTime: 2023-07-27  15:30
 */
@Data
public class VisitCheckWeekResp implements Serializable {
    private static final long serialVersionUID = 6889354532292271203L;

    /**
     * 本月周期数
     */
    private Integer weekNum;

    /**
     * 本周期开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 本周期结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 周期目标审核次数
     */
    private Integer requiredCheckNum;

    /**
     * 审核次数
     */
    private Integer checkNum;
}
