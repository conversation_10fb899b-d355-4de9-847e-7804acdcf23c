package com.intelliquor.cloud.shop.terminal.model.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2025-01-03 10:54
 */
@Data
public class ScanDetailReq {

    /**
     * 起始时间
     */
    @NotBlank(message = "起始时间不能为空")
    private String startTime;

    /**
     * 截止时间
     */
    @NotBlank(message = "截止时间不能为空")
    private String endTime;

    /**
     * 终端副编码
     */
    private String deputyCodes;

    /**
     * 产品skus
     */
    @NotBlank(message = "产品skus不能为空")
    private String skuCodes;
}
