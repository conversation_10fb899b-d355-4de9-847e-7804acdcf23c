package com.intelliquor.cloud.shop.terminal.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 描述：终端扫码批次表实体类
 *
 * <AUTHOR>
 * @date 2022-08-17
 */
@Data
public class TerminalScanBalanceModel {


    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "状态0-无效 1-有效，默认无效，只上传照片是无效，存在有效扫码就变为有效，查看进货记录时只查看有效的")
    private Integer status;

    @ApiModelProperty(value = "终端Id")
    private Integer shopId;

    @ApiModelProperty(value = "扫码收货类型 1-按单扫码收货 2-按码扫码收货")
    private Integer scanType;

    @ApiModelProperty(value = "现场照片")
    private String liveImg;

    @ApiModelProperty(value = "收货地址-省")
    private String province;

    @ApiModelProperty(value = "收货地址-市")
    private String city;

    @ApiModelProperty(value = "收货地址-区")
    private String district;

    @ApiModelProperty(value = "收货详细地址")
    private String address;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "latitude")
    private Double latitude;

    @ApiModelProperty(value = "扫码人id")
    private Integer createUserId;

    @ApiModelProperty(value = "扫码人类型0-客户经理 1-经销商人员 2-终端人员 3-联盟 4-业代")
    private Integer createUserType;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "商户Id")
    private Integer companyId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "本批次最后一次扫码操作时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;

    @ApiModelProperty(value = "是否删除0-未删除1-已删除")
    private Integer isDelete;

    /**
     * 批次
     */
    private String transaction;

    /**
     * 收支类型 1:进货奖励
     */
    private Integer type;

    /**
     * 虚拟货款
     */
    private BigDecimal virtualAmount = BigDecimal.ZERO;

    // 收货地点
    private Integer receivingLocation;

    // 距离(米)
    private BigDecimal distanceNum;
}