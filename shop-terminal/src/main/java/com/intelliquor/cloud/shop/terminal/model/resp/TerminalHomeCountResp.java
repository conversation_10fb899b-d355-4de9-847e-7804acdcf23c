package com.intelliquor.cloud.shop.terminal.model.resp;

import lombok.Data;

@Data
public class TerminalHomeCountResp {

    /**
     * 终端宴席报备
     * */
    private String terminalBanquetReportNum;

    /**
     * 终端宴席开展
     * */
    private String terminalBanquetStartNum;

    /**
     * 终端拜访数量
     * */
    private String terminalVisitNum;

    public String getTerminalBanquetReportNum(){
        if(null == this.terminalBanquetReportNum){
            this.terminalBanquetReportNum = "0/0";
        }
        return terminalBanquetReportNum;
    }

    public String getTerminalBanquetStartNum(){
        if(null == this.terminalBanquetStartNum){
            this.terminalBanquetStartNum = "0/0";
        }
        return terminalBanquetStartNum;
    }

    public String getTerminalVisitNum(){
        if(null == this.terminalVisitNum){
            this.terminalVisitNum = "0/0";
        }
        return terminalVisitNum;
    }

}
