package com.intelliquor.cloud.shop.terminal.controller.applet;

import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.FenceViolationOfAgreementAppealModel;
import com.intelliquor.cloud.shop.common.service.FenceViolationOfAgreementAppealService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 违约申诉 前端控制器
 * </p>
 *
 * <AUTHOR> XIN
 * @since 2024-03-07
 */
@RestController
@RequestMapping("/terminalApplet/fenceAppeal")
public class FenceViolationOfAgreementAppealController {

    @Resource
    private FenceViolationOfAgreementAppealService fenceViolationOfAgreementAppealService;

    /**
     * 查询异地开瓶申诉信息
     *
     * @param id
     * @return
     */
    @GetMapping("getByFenceId")
    public RestResponse<FenceViolationOfAgreementAppealModel> getByFenceId(Integer fenceId) {
        if (Objects.isNull(fenceId)) {
            return RestResponse.error("fenceId不能为空");
        }
        FenceViolationOfAgreementAppealModel model = fenceViolationOfAgreementAppealService.getByFenceId(fenceId);
        return RestResponse.success("查询成功", model);
    }
}
