package com.intelliquor.cloud.shop.terminal.controller.admin;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.terminal.model.enums.TerminalYearlyDetailTypeEnum;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalTaskScanCompleteDayReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalTaskYearlyDetailJobReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskScanCompleteDayResp;
import com.intelliquor.cloud.shop.terminal.service.ITerminalTaskScanCompleteDayService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业代任务每日收货完成详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Slf4j
@RestController
@RequestMapping("/terminal/scanCompleteDay")
public class TerminalTaskScanCompleteDayController {

    private final ITerminalTaskScanCompleteDayService terminalTaskScanCompleteDayService;

    public TerminalTaskScanCompleteDayController(ITerminalTaskScanCompleteDayService terminalTaskScanCompleteDayService) {
        this.terminalTaskScanCompleteDayService = terminalTaskScanCompleteDayService;
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getScanList")
    public Response<PageInfo<TerminalTaskScanCompleteDayResp>> getScanList(@RequestBody TerminalTaskScanCompleteDayReq req,
                                   @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                   @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        try {
            List<TerminalTaskScanCompleteDayResp> list = terminalTaskScanCompleteDayService.selectScanList(req, pageNum, pageSize);
            PageInfo<TerminalTaskScanCompleteDayResp> terminalTaskScanCompleteDayRespPageInfo = new PageInfo<>(list);
            return Response.ok(terminalTaskScanCompleteDayRespPageInfo);
        } catch (BusinessException e) {
            log.error("查询失败", e);
            return Response.fail(Integer.parseInt(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("查询失败", e);
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 手动触发业代任务指定日期收货完成详情
     *
     * @param dateStr 指定日期
     */
    @GetMapping("/addNewDataScannedTheDayBefore")
    public Response<String> addNewDataScannedTheDayBefore(@RequestParam("dateStr") String dateStr) {
        try {
            //转换时间格式
            Date date = DateUtil.parse(dateStr);
            log.info("业代任务指定日期[{}]收货完成详情======开始", new Gson().toJson(date));
            terminalTaskScanCompleteDayService.addNewDataScannedTheDayBeforeDaily(date);
            log.info("业代任务指定日期[{}]收货完成详情======结束", new Gson().toJson(date));
            return Response.ok("业代任务指定日期收货完成详情成功");
        } catch (Exception e) {
            log.error("业代任务指定日期[{}]收货完成详情======失败", new Gson().toJson(dateStr), e);
            return Response.fail("业代任务指定日期收货完成详情失败");
        }

    }

    @PostMapping("/page")
    public RestResponse<PageInfo<TerminalTaskScanCompleteDayResp>> page(@RequestBody TerminalTaskScanCompleteDayReq terminalTaskScanCompleteDayReq,
                             @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                             @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        try {
            log.info("业代任务收货完成分页查询======开始, 查询条件为[{}]", Objects.nonNull(terminalTaskScanCompleteDayReq) ? new Gson().toJson(terminalTaskScanCompleteDayReq) : "");
            PageInfo<TerminalTaskScanCompleteDayResp> terminalTaskScanCompleteDayRespPageInfo = terminalTaskScanCompleteDayService.page(terminalTaskScanCompleteDayReq, pageNum, pageSize);
            log.info("业代任务收货完成分页查询======结束");
            return RestResponse.success(terminalTaskScanCompleteDayRespPageInfo);
        } catch (Exception e) {
            log.error("业代任务收货完成分页查询======失败, 查询条件为[{}]", Objects.nonNull(terminalTaskScanCompleteDayReq) ? new Gson().toJson(terminalTaskScanCompleteDayReq) : "", e);
            return RestResponse.error("业代任务收货完成分页查询失败");
        }
    }


    /**
     * 手动触发
     *
     * 时间段内活跃终端统计
     * 并回写到 任务领取记录-月度收货任务完成度(t_terminal_task_receive_month)
     *
     * 统计时间范围：当月开始时间 到 当前时间-1天
     * 统计维度： 业务人员id,主任务id,任务领取记录-月度记录id
     *
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    @GetMapping("/livelyTerminalStatistics")
    public Response<String> livelyTerminalStatistics(@RequestParam("startDateStr") String startDateStr,
                                                     @RequestParam("endDateStr") String endDateStr) {
        try {
            //转换时间格式
            Date startTime = DateUtil.parse(startDateStr);
            Date endTime = DateUtil.parse(endDateStr);
            log.info("手动触发活跃终端统计======开始[{}]-[{}]", startTime,endTime);
            terminalTaskScanCompleteDayService.curMonthLivelyTerminalStatistics(startTime,endTime,null);
            log.info("手动触发活跃终端统计======结束[{}]-[{}]", startTime,endTime);
            return Response.ok("手动触发活跃终端统计成功");
        } catch (Exception e) {
            log.error("手动触发活跃终端统计======失败[{}]-[{}]", startDateStr,endDateStr, e);
            return Response.fail("手动触发活跃终端统计失败");
        }
    }


    /**
     * 手动触发
     *
     * 时间段内激活终端统计
     * 并回写到 任务领取记录-月度收货任务完成度(t_terminal_task_receive_month)
     *
     * 统计时间范围：当月开始时间 到 当前时间-1天
     * 统计维度： 业务人员id,主任务id,任务领取记录-月度记录id
     *
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    @GetMapping("/activateTerminalStatistics")
    public Response<String> activateTerminalStatistics(@RequestParam("startDateStr") String startDateStr,
                                                     @RequestParam("endDateStr") String endDateStr) {
        try {
            //转换时间格式
            DateTime startTime = DateUtil.beginOfMonth(DateUtil.parse(startDateStr));
            DateTime endTime = DateUtil.endOfDay(DateUtil.parse(endDateStr));
            log.info("手动触发激活终端统计======开始[{}]-[{}]", startTime,endTime);
            TerminalTaskYearlyDetailJobReq terminalTaskYearlyDetailJobReq = new TerminalTaskYearlyDetailJobReq();
            terminalTaskYearlyDetailJobReq.setStartTime(startTime);
            terminalTaskYearlyDetailJobReq.setEndTime(endTime);
            terminalTaskYearlyDetailJobReq.setTerminalType(TerminalYearlyDetailTypeEnum.COLLECTION.getKey());
            terminalTaskScanCompleteDayService.curActivateTerminalStatistics(terminalTaskYearlyDetailJobReq);
            log.info("手动触发激活终端统计======结束[{}]-[{}]", startTime,endTime);
            return Response.ok("手动触发激活终端统计成功");
        } catch (Exception e) {
            log.error("手动触发激活终端统计======失败[{}]-[{}]", startDateStr,endDateStr, e);
            return Response.fail("手动触发激活终端统计失败");
        }
    }
}
