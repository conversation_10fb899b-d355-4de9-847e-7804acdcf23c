package com.intelliquor.cloud.shop.terminal.controller.admin;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.terminal.service.BrokerTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 定时任务手动入口
 */
@Slf4j
@RestController
@RequestMapping("/taskTestController")
public class TaskTestController {

    @Autowired
    private BrokerTaskService brokerTaskService;


    /**
     * 终端扫码数据汇总
     * @param day
     * @param shopId
     * @return
     */
    @GetMapping(value = "/dealScanCollect")
    public Response<String> dealScanCollect(String day,Integer shopId){
        brokerTaskService.dealScanCollect(day,shopId);
        return Response.ok("处理成功");
    }

    /**
     * 业代任务完成量计算
     * @param brokerId 业代Id
     * @param taskPeriod 任务周期 具体年或者年-月
     * @return
     */
    @GetMapping(value = "/dealTaskCompaleteQuota")
    public Response<String> dealTaskCompaleteQuota(Integer brokerId,String taskPeriod,String dayStr){
        Date day = DateUtils.convert2Date(dayStr,"yyyy-MM-dd");
        brokerTaskService.dealTaskCompaleteQuota(brokerId,taskPeriod,day);
        return Response.ok("处理成功");
    }


    @GetMapping(value = "/test")
    public Response<String> test(){
        return Response.ok("处理成功");
    }
}
