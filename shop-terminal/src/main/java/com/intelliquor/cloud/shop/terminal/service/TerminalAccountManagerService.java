package com.intelliquor.cloud.shop.terminal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
import com.intelliquor.cloud.shop.common.model.UserInfo;
import com.intelliquor.cloud.shop.terminal.model.req.AccountManagerGetPromotionReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalAccountManagerAdminReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalAccountManagerReq;
import com.intelliquor.cloud.shop.terminal.model.resp.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 客户经理和人员账号表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
public interface TerminalAccountManagerService extends IService<TerminalAccountManagerModel> {

    public TerminalAccountManagerModel accountManagerLogin(String phone,
                                                           String verifyCode,
                                                           String weCharCode,
                                                           String appId);

    /**
     * 根据客户经理id获取登录模型
     *
     * @param accountManagerId 客户经理id
     * @return 登录模型
     */
    public TerminalAccountManagerModel getLoginModel(Integer accountManagerId);

    /**
     * 根据客户经理id获取登录模型
     *
     * @param promotionManagerId 客户经理id
     * @return 登录模型
     */
    public TerminalAccountManagerModel getLoginModelByPromotionManagerId(String promotionManagerId);

    public TerminalAccountManagerModel accountManagerLoginVerify(HttpServletRequest httpServletRequest);

    public TerminalAccountManagerResp selectStaffNumber(Integer id);

    public List<TerminalAccountManagerModel> selectAccountManagerByParentId(Integer id,
                                                                            Integer type,
                                                                            String nameOrPhone,
                                                                            Integer status,
                                                                            Integer page,
                                                                            Integer limit);

    public void insertAccountManager(TerminalAccountManagerReq terminalAccountManagerReq);

    public void deleteAccountStaffById(Integer id);

    public void updateAccountStaffStatusById(Integer id);

    public void updateAccountStaffById(TerminalAccountManagerReq terminalAccountManagerReq);

    public List<TerminalAccountManagerDealerResp> selectDealerByAccountManagerId(Integer id, String nameOrPhone);

    public List<TerminalAccountManagerDealerResp> selectShopByAccountManagerId(Integer id, String nameOrPhone);

    public void insertOrUpdateJoinAccountManager(String str, HttpServletRequest httpServletRequest);

    public void quitLogin(String authLocal);

    public List<TerminalAccountManagerByStaffAdminResp> selectTerminalAccountManagerByStaffAdmin(TerminalAccountManagerAdminReq terminalAccountManagerAdminReq,
                                                                                                 Integer page,
                                                                                                 Integer limit);

    public TerminalAccountManagerByStaffAdminResp selectTerminalAccountManagerByStaffByIdAdmin(Integer id);

    public List<TerminalAccountManagerByManagerAdminResp> selectTerminalAccountManagerByManagerAdmin(String nameOrPhone);

    public void exportAccountManagerByStaffAdmin(TerminalAccountManagerAdminReq terminalAccountManagerAdminReq, UserInfo user) throws IOException;

    void updateHeadPictureById(String headPicture, Integer id);

    void importAccountManager(MultipartFile multipartFile) throws IOException;

    /**
     * 查询任务下级负责人列表
     *
     * @param userId 用户id
     * @param searchText  搜索值
     * @return List<TerminalAccountManagerModel>
     */
    List<TerminalAccountManagerModel> selectSubTerminalManagerList(Integer userId, String searchText);

    // 根据手机号查询客户经理信息
    TerminalAccountManagerModel selectOnceAccountByPhone(String phone);

    List<TerminalAccountManagerModel> selectAllTerminalAccountManagerByManager(String nameOrPhone);

    PageInfo<AccountManagerGetPromotionResp> getMarketPromotionList(AccountManagerGetPromotionReq accountManagerGetPromotionReq);

    /**
    * 根据查询生效账号信息
     * @param id
    */
    TerminalAccountManagerModel selectAccountManagerById(Integer id);

    void asyncDisableAccount(String str, HttpServletRequest httpServletRequest);
}
