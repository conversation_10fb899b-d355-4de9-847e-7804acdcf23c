package com.intelliquor.cloud.shop.terminal.controller.applet;

import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.resp.TerminalProtocolActivityResp;
import com.intelliquor.cloud.shop.common.service.TerminalProtocolActivityService;
import com.intelliquor.cloud.shop.terminal.service.ITerminalProtocolActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 终端和产品协议 小程序端调用
 *
 * @module 国台终端
 */
@Slf4j
@RestController
@RequestMapping("/terminalProtocolActivity")
public class TerminalProtocolActivityController {

    @Autowired
    private TerminalProtocolActivityService terminalProtocolActivityService;

    @Autowired
    private ITerminalProtocolActivityService iTerminalProtocolActivityService;

    /**
     * 根据活动id获取活动配置详情
     */
    @GetMapping("/getTerminalProtocolActivityInfoByActivityId")
    public RestResponse<TerminalProtocolActivityResp> getTerminalProtocolActivityInfo(@Validated Long activityId) {
        try {
            TerminalProtocolActivityResp results = terminalProtocolActivityService.getTerminalProtocolActivityInfoByActivityId(activityId);
            // 返回成功的信息
            return RestResponse.success("终端小程序端获取活动配置成功", results);
        } catch (BusinessException e) {
            log.error("终端小程序端获取活动配置失败", e);
            return RestResponse.error(Integer.parseInt(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("终端小程序端获取终端活动配置失败", e);
            return RestResponse.error(e.getMessage());
        }
    }

    /**
     * 根据终端id查询活动列表
     */
    @GetMapping("/getTerminalProtocolActivityListByShopId")
    public RestResponse<List<TerminalProtocolActivityResp>> getTerminalProtocolActivityListByTerminalId(@Validated Integer shopId) {

        try {
            // 查询数据
            List<TerminalProtocolActivityResp> results = iTerminalProtocolActivityService.getTerminalProtocolActivityListByTerminalId(shopId);
            // 返回成功的信息
            return RestResponse.success("获取终端活动成功", results);
        } catch (BusinessException e) {
            log.error("获取终端活动失败", e);
            return RestResponse.error(Integer.parseInt(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("获取终端活动失败", e);
            return RestResponse.error(e.getMessage());
        }
    }

}
