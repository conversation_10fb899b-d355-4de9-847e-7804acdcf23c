package com.intelliquor.cloud.shop.terminal.model.req;

import com.baomidou.mybatisplus.annotation.TableField;
import com.intelliquor.cloud.shop.terminal.model.TerminalSalaryBasicInfoModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class TerminalSalaryBasicInfoReq  extends TerminalSalaryBasicInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "开始合作日期不能为空")
    @TableField(exist = false)
    private String startCooperationDateStr;

    @TableField(exist = false)
    private String terminationDateStr;
}
