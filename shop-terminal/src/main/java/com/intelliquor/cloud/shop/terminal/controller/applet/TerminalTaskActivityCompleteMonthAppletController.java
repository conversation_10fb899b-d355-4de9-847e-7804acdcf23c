package com.intelliquor.cloud.shop.terminal.controller.applet;

import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalTaskActivityCompleteMonthReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalTaskBanquetCompleteDayReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskBanquetAndMeetingCompleteMonthResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskBanquetCompleteScoreResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskMeetingCompleteScoreResp;
import com.intelliquor.cloud.shop.terminal.service.ITerminalTaskActivityCompleteMonthService;
import com.intelliquor.cloud.shop.terminal.service.ITerminalTaskBanquetCompleteDayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/17 09:12
 */
@Slf4j
@RestController
@RequestMapping("/terminal/activityCompleteMonthApplet")
@Api(tags = {"活动完成月报表接口"}, value = "活动完成月报表接口")
public class TerminalTaskActivityCompleteMonthAppletController {

    private final ITerminalTaskActivityCompleteMonthService terminalTaskActivityCompleteMonthService;
    private final UserContext userContext;

    public TerminalTaskActivityCompleteMonthAppletController(ITerminalTaskActivityCompleteMonthService terminalTaskActivityCompleteMonthService, UserContext userContext) {
        this.terminalTaskActivityCompleteMonthService = terminalTaskActivityCompleteMonthService;
        this.userContext = userContext;
    }



    @PostMapping("/getAppMonthMeetingScore")
    @ApiOperation(value = "获取会议得分", notes = "获取会议得分", httpMethod = "POST")
    RestResponse<TerminalTaskMeetingCompleteScoreResp> getAppBanquetScore(@RequestBody TerminalTaskActivityCompleteMonthReq req) {
        try {
            log.info("获取会议得分======开始");
            TerminalAccountManagerModel terminalModel = userContext.getTerminalModel();
            req.setBrokerId(terminalModel.getId());
            TerminalTaskMeetingCompleteScoreResp terminalTaskMeetingCompleteScoreResp = terminalTaskActivityCompleteMonthService.getMonthMeetingScore(req);
            log.info("获取会议得分======结束");
            return RestResponse.success(terminalTaskMeetingCompleteScoreResp);
        } catch (Exception e) {
            log.error("获取会议得分======失败", e);
            return RestResponse.error("获取会议得分失败");
        }
    }


    @ApiOperation(value = "查询月度活动（宴会会议）列表", notes = "查询月度活动（宴会会议)列表", httpMethod = "POST")
    @PostMapping(value = "/getAppMonthActivityList")
    RestResponse<PageInfo<TerminalTaskBanquetAndMeetingCompleteMonthResp>> getAppMonthActivityList(@RequestBody TerminalTaskActivityCompleteMonthReq req,
                                                                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            log.info("客户经理任务宴会会议分页查询======开始, 查询条件为[{}]", new Gson().toJson(req));
            TerminalAccountManagerModel terminalModel = userContext.getTerminalModel();
            req.setBrokerId(terminalModel.getId());
            PageInfo<TerminalTaskBanquetAndMeetingCompleteMonthResp> terminalTaskBanquetAndMeetingCompleteMonthRespPageInfo = terminalTaskActivityCompleteMonthService.pageMonthBanquetAndMeetingByAccountManager(req, pageNum, pageSize);
            log.info("客户经理任务宴会会议分页查询======结束");
            return RestResponse.success(terminalTaskBanquetAndMeetingCompleteMonthRespPageInfo);
        } catch (Exception e) {
            log.error("客户经理任务宴会会议分页查询======失败, 查询条件为[{}]", req, e);
            return RestResponse.error("客户经理任务宴会会议分页查询失败");
        }
    }
}
