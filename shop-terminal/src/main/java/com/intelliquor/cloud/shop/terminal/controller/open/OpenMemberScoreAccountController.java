package com.intelliquor.cloud.shop.terminal.controller.open;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intelliquor.cloud.shop.common.account.dto.MemberScoreAccountRespDTO;
import com.intelliquor.cloud.shop.common.account.service.IAccountRemoteService;
import com.intelliquor.cloud.shop.common.model.ShopDealerOrderPlus;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 会员积分账户开放接口
 */
@RestController
@RequestMapping("/open/member-score-account")
@Api(tags = "会员积分账户开放接口")
public class OpenMemberScoreAccountController {

    @Autowired
    private IAccountRemoteService accountRemoteService;

    /**
     * 查询会员积分账户详情
     * @param memberShopId 会员店铺ID
     * @return 会员积分账户详情
     */
    @GetMapping("/{memberShopId}/detail")
    @ApiOperation(value = "查询会员积分账户详情")
    public MemberScoreAccountRespDTO queryMemberScoreAccountDetail(
            @ApiParam(value = "会员店铺ID", required = true)
            @PathVariable("memberShopId") Long memberShopId) {
        return accountRemoteService.queryMemberScoreAccountDetail(memberShopId);
    }

    /**
     * 扣减会员积分(提现)
     * @param businessNo 业务编号
     * @param memberId 会员ID
     * @param points 积分数量
     * @return 扣减结果
     */
    @PostMapping("/withdrawal")
    @ApiOperation(value = "扣减会员积分(提现)")
    public Boolean deductPointsWithdrawal(
            @ApiParam(value = "业务编号", required = true)
            @RequestParam("businessNo") Integer businessNo,
            @ApiParam(value = "会员ID", required = true)
            @RequestParam("memberId") Integer memberId,
            @ApiParam(value = "子账户ID", required = true)
            @RequestParam("subAccountId") Long subAccountId,
            @ApiParam(value = "积分数量", required = true)
            @RequestParam("points") BigDecimal points) {
        return accountRemoteService.deductPointsWithdrawal(businessNo, memberId, points, subAccountId);
    }

    /**
     * 查询子账户积分
     * @param subAccountId 子账户ID
     * @return 子账户积分详情
     */
    @GetMapping("/sub-account/{subAccountId}")
    @ApiOperation(value = "查询子账户积分")
    public MemberScoreAccountRespDTO querySubAccountScore(
            @ApiParam(value = "子账户ID", required = true)
            @PathVariable("subAccountId") Long subAccountId) {
        return accountRemoteService.querySubAccountScore(subAccountId);
    }


    /**
     * 创建一个DTO来封装请求参数
     */
    @Data
    public class DeductPointsOrderRequest {
        private ShopDealerOrderPlus order;
        private ShopModel shopModel;
    }

    /**
     * 扣减会员积分(订单)
     * @param request 订单及店铺信息
     * @return 扣减结果
     */
    @PostMapping("/order/deduct")
    @ApiOperation(value = "扣减会员积分(订单)")
    public Boolean deductPointsOrder(
            @ApiParam(value = "订单及店铺信息", required = true)
            @RequestBody Map<String, Object> request) {
        ObjectMapper mapper = new ObjectMapper();
        ShopDealerOrderPlus order = mapper.convertValue(request.get("order"), ShopDealerOrderPlus.class);
        ShopModel shopModel = mapper.convertValue(request.get("shopModel"), ShopModel.class);
        return accountRemoteService.deductPointsOrder(order, shopModel);
    }

    /**
     * 根据SKU查询子账户积分
     * @param memberId 会员ID
     * @param sku SKU编码
     * @return 子账户积分详情
     */
    @GetMapping("/sku")
    @ApiOperation(value = "根据SKU查询子账户积分")
    public List<MemberScoreAccountRespDTO> querySubAccountScoreBySku(
            @ApiParam(value = "会员ID", required = true)
            @RequestParam("memberId") Long memberId,
            @ApiParam(value = "SKU编码", required = true)
            @RequestParam("sku") String sku) {
        return accountRemoteService.querySubAccountScoreBySku(memberId, sku);
    }
}
