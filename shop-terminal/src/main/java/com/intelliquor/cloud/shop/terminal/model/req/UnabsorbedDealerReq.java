package com.intelliquor.cloud.shop.terminal.model.req;


import com.intelliquor.cloud.shop.terminal.model.TerminalTaskPackageRegionModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "UnabsorbedDealerReq对象", description = "未分配终端列表入参")
public class UnabsorbedDealerReq {

    /**
     * 主任务id
     */
    @NotNull(message ="主任务id不能为空")
    @ApiModelProperty("主任务id")
    private Integer id;

    /**
     * 经销商编码
     */
    @ApiModelProperty("经销商编码")
    private String dealerCode;

    /**
     *经销商名称
     */
    @ApiModelProperty("经销商名称")
    private String dealerName;

    /**
     * 终端名称
     */
    @ApiModelProperty("终端名称")
    private String name;

    @ApiModelProperty("行政区域")
    private List<TerminalTaskPackageRegionModel> regionModelList;

}
