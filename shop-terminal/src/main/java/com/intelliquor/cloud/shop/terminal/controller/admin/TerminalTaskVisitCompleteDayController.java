package com.intelliquor.cloud.shop.terminal.controller.admin;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalTaskVisitCompleteDayReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalTaskVisitCompleteDayResp;
import com.intelliquor.cloud.shop.terminal.service.ITerminalTaskVisitCompleteDayService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业代任务每日拜访完成详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Slf4j
@RestController
@RequestMapping("/terminal/visitCompleteDay")
public class TerminalTaskVisitCompleteDayController {

    private final ITerminalTaskVisitCompleteDayService terminalTaskVisitCompleteDayService;

    public TerminalTaskVisitCompleteDayController(ITerminalTaskVisitCompleteDayService terminalTaskVisitCompleteDayService) {
        this.terminalTaskVisitCompleteDayService = terminalTaskVisitCompleteDayService;
    }


    /**
     * 手动触发业代任务指定日期拜访完成详情
     *
     * @param dateStr 指定日期
     */
    @GetMapping("/addNewDataVisitedTheDayBefore")
    public Response<String> addNewDataVisitedTheDayBefore(@RequestParam("dateStr") String dateStr) {
        try {
            //转换时间格式
            Date date = DateUtil.parse(dateStr);
            log.info("业代任务指定日期[{}]拜访完成详情======开始", new Gson().toJson(date));
            terminalTaskVisitCompleteDayService.addNewDataVisitedTheDayBeforeDaily(date);
            log.info("业代任务指定日期[{}]拜访完成详情======结束", new Gson().toJson(date));
            return Response.ok("业代任务指定日期拜访完成详情成功");
        } catch (Exception e) {
            log.error("业代任务指定日期[{}]拜访完成详情======失败", new Gson().toJson(dateStr), e);
            return Response.fail("业代任务指定日期拜访完成详情失败");
        }

    }

    @PostMapping("/page")
    public RestResponse<PageInfo<TerminalTaskVisitCompleteDayResp>> page(@RequestBody TerminalTaskVisitCompleteDayReq terminalTaskVisitCompleteDayReq,
                             @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                             @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        try {
            log.info("业代任务拜访分页查询======开始, 查询条件为[{}]", Objects.nonNull(terminalTaskVisitCompleteDayReq) ? new Gson().toJson(terminalTaskVisitCompleteDayReq) : "");
            PageInfo<TerminalTaskVisitCompleteDayResp> terminalTaskVisitCompleteDayRespPageInfo = terminalTaskVisitCompleteDayService.page(terminalTaskVisitCompleteDayReq, pageNum, pageSize);
            log.info("业代任务拜访分页查询======结束");
            return RestResponse.success(terminalTaskVisitCompleteDayRespPageInfo);
        } catch (Exception e) {
            log.error("业代任务拜访分页查询======失败, 查询条件为[{}]", Objects.nonNull(terminalTaskVisitCompleteDayReq) ? new Gson().toJson(terminalTaskVisitCompleteDayReq) : "", e);
            return RestResponse.error("业代任务拜访分页查询失败");
        }
    }

    @ApiOperation(value = "查询月度拜访列表", notes = "查询月度拜访列表", httpMethod = "GET")
    @RequestMapping(value = "/getMonthVisitList")
    public RestResponse<PageInfo<TerminalTaskVisitCompleteDayResp>> getMonthVisitList(TerminalTaskVisitCompleteDayReq filter) {
        try {
            log.info("业代任务拜访分页查询======开始, 查询条件为[{}]", Objects.nonNull(filter) ? new Gson().toJson(filter) : "");
            List<TerminalTaskVisitCompleteDayResp> list = terminalTaskVisitCompleteDayService.getMonthVisitList(filter, filter.getPageNum(), filter.getPageSize());
            PageInfo<TerminalTaskVisitCompleteDayResp> terminalTaskVisitCompleteDayRespPageInfo = new PageInfo<>(list);
            log.info("业代任务拜访分页查询======结束");
            return RestResponse.success(terminalTaskVisitCompleteDayRespPageInfo);
        } catch (Exception e) {
            log.error("业代任务拜访分页查询======失败, 查询条件为[{}]", Objects.nonNull(filter) ? new Gson().toJson(filter) : "", e);
            return RestResponse.error("业代任务拜访分页查询失败");
        }
    }

}
