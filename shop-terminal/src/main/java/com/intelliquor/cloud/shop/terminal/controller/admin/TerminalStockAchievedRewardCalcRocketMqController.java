package com.intelliquor.cloud.shop.terminal.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.intelliquor.cloud.shop.common.dao.DisplayResultCommonDao;
import com.intelliquor.cloud.shop.common.dao.TerminalProductProtocolRelationDao;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.DisplayResultModel;
import com.intelliquor.cloud.shop.common.service.*;
import com.intelliquor.cloud.shop.common.service.resp.ProtocoTimeParamResp;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalRewardCalcRocketMqReq;
import com.intelliquor.cloud.shop.terminal.rocketmq.MqSendService;
import com.intelliquor.cloud.shop.terminal.service.IDisplayResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/rocketmq")
public class TerminalStockAchievedRewardCalcRocketMqController {

    @Value(value = "${rocketmq.producer.topic.stock}")
    private String topic;

    @Value(value = "${rocketmq.producer.group}")
    private String group;

    @Autowired
    private TerminalProductProtocolV2Service terminalProductProtocolV2CommonService;
    @Autowired
    private TerminalRewardCalculatorService terminalRewardCalculatorService;

    @Autowired
    private TerminalRewardCalcDetailService terminalRewardCalcDetailService;

    @Autowired
    private TerminalRewardCalcBatchService terminalRewardCalcBatchService;

    @Autowired
    private TerminalProductProtocolRelationDao terminalProductProtocolRelationDao;

    @Autowired
    private DisplayResultCommonDao displayResultCommonDao;


    @Autowired
    private MqSendService mqSendService;

    @Autowired
    private IDisplayResultService displayResultService;

    @Autowired
    private ITerminalShopCommonService terminalShopCommonService;

    @GetMapping("/stockAchievedRewardCalc/sendMessage")
    public RestResponse<String> sendMessage(@RequestParam("password") String password) {
        log.info("统计需要计算进货达成奖励的终端======开始执行");
        if(!password.equals("18951002")){
            log.info("统计需要计算进货达成奖励密码：", password);
            return RestResponse.error("password error");
        }
        LocalDate currentDate = LocalDate.now();
        Month currentMonth = currentDate.getMonth();
        int currentDay = currentDate.getDayOfMonth();
        log.info("统计需要计算进货达成奖励的终端======月份：{}",currentMonth);

        // 当前时间是每个季度的第一个月的1号
//        if (currentDay == 1 && (currentMonth == Month.JANUARY || currentMonth == Month.APRIL ||
//                currentMonth == Month.JULY || currentMonth == Month.OCTOBER)) {
            log.info("统计需要计算进货达成奖励的终端======进入执行逻辑");

            String year = String.valueOf(currentDate.getYear());
            //  Month 值从 1 开始，因此我们需要 +1 来获得当前月份
            int month = currentDate.getMonthValue();
            int quarter = ((month - 1) / 3) + 1;
            if (quarter == 1) {
                year = String.valueOf((Integer.valueOf(year) - 1));
                quarter =  4;
            } else {
                quarter = quarter - 1;
            }
            log.info("统计需要计算进货达成奖励的终端======年份：{}，季度：{}",year, quarter);
            ProtocoTimeParamResp packageProtocoTimeParamResp = terminalProductProtocolV2CommonService.getPackageQuantityTimeByProtocolTypeAndYearAndQuarter(year, quarter);

            //遍历并核算奖励列表
            displayResultService.asyncCalculateStockAchievedReward(packageProtocoTimeParamResp.getStartDate(), packageProtocoTimeParamResp.getEndDate(), year, quarter);

            // 批量排查使用该方式
//            List<TerminalProductProtocolRelationModel> packageRewardTerminals = terminalProductProtocolV2CommonService.getProtocolTerminalIdsByProtocolType(ProtocolTypeEnum.PACKAGE_QUANTITY.getKey(),  packageProtocoTimeParamResp.getStartDate(), packageProtocoTimeParamResp.getEndDate());
//            //筛选：部分终端等级、签署包量协议并且产品是国标的终端
//            Set<String> validLevelCodes = new HashSet<>(Arrays.asList("13", "12", "1", "2", "3", "4", "10", "11"));
//            List<TerminalProductProtocolRelationModel> stockAchievedTerminals = new ArrayList<>();
//            for (TerminalProductProtocolRelationModel terminalProductProtocolRelationModel : packageRewardTerminals) {
//                if(validLevelCodes.contains(terminalProductProtocolRelationModel.getLevelCode()) && ProtocolProductCodeEnum.GUOBIAO.getKey().toString().equals(terminalProductProtocolRelationModel.getProtocolProductCode())){
//                    stockAchievedTerminals.add(terminalProductProtocolRelationModel);
//                }
//            }
//
//            // 单条排查使用该方式
////            Long relationId = 2446L;
////            List<TerminalProductProtocolRelationModel> stockAchievedTerminals = new ArrayList<>();
////            List<TerminalProductProtocolRelationModel> packageRewardTerminals1 = terminalProductProtocolV2CommonService.getProtocolTerminalIdsByProtocolType(ProtocolTypeEnum.PACKAGE_QUANTITY.getKey(),  packageProtocoTimeParamResp.getStartDate(),  packageProtocoTimeParamResp.getEndDate());
////            Set<String> validLevelCodes = new HashSet<>(Arrays.asList("13", "12", "1", "2", "3", "4", "10", "11"));
////            for (TerminalProductProtocolRelationModel item : packageRewardTerminals1) {
////                if( validLevelCodes.contains(item.getLevelCode()) && item.getId().longValue() == relationId.longValue() && ProtocolProductCodeEnum.GUOBIAO.getKey().toString().equals(item.getProtocolProductCode())){
////                    stockAchievedTerminals.add(item);
////                }
////            }
//            if(CollectionUtils.isNotEmpty(stockAchievedTerminals)){
//                log.info("统计需要计算进货达成奖励的终端======开始，终端奖励结果{}个", stockAchievedTerminals.size());
//
//                String batchNumber = terminalRewardCalculatorService.generateBatchNumber();
//
//                //遍历并核算奖励列表
//                displayResultService.asyncCalculateStockAchievedReward(stockAchievedTerminals, batchNumber, year, quarter, TerminalRewardOperationTypeEnum.INCENTIVE_ACCOUNTING.getKey());
//
//            }else{
//                log.info("统计需要计算进货达成奖励的终端======结束，无终端需要计算奖励");
//            }
//        }
        return RestResponse.success("send message success");
    }
    @PostMapping("/stockAchievedRewardSupplementCalc/sendMessage")
    public RestResponse<String> sendMessage(@RequestBody TerminalRewardCalcRocketMqReq terminalRewardCalcRocketMqReq) {
        log.info("统计需要补充计算进货达成奖励的终端======开始执行");
        String year = terminalRewardCalcRocketMqReq.getYear();
        Integer quarter = terminalRewardCalcRocketMqReq.getQuarter();
        log.info("统计需要计算进货达成奖励的终端======年份：{}，季度：{}",year,quarter);
        log.info("统计需要补充计算进货达成奖励的终端======进入执行逻辑");
//      查询终端该季度是否已经核算过奖励
        List<Long> terminalShopIds = terminalRewardCalcRocketMqReq.getTerminalShopIds();
        List<Long> terminalShopCalcIds = new ArrayList<>();
        for (Long terminalShopId : terminalShopIds) {
            List<DisplayResultModel> displayResultModelList = displayResultCommonDao.selectList(new LambdaQueryWrapper<DisplayResultModel>()
                .eq(DisplayResultModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                .eq(DisplayResultModel::getDisplayYear, year)
                .eq(DisplayResultModel::getDisplayQuarter, quarter)
                .eq(DisplayResultModel::getProtocolTypeNew, ProtocolTypeEnum.PACKAGE_QUANTITY.getKey())
                .eq(DisplayResultModel::getComputeType,ComputeTypeEnum.STOCK_ACHIEVED_REWARD.getKey())
                .eq(DisplayResultModel::getProtocolStandard, ProtocolStandardByYearEnum.STANDARD_24.getKey())
                .eq(DisplayResultModel::getCycleType, CycleTypeEnum.QUARTER.getKey())
                .eq(DisplayResultModel::getTerminalShopId, terminalShopId));
            if(CollectionUtils.isNotEmpty(displayResultModelList)){
                terminalShopCalcIds.add(terminalShopId);
            }
        }
        if(CollectionUtils.isNotEmpty(terminalShopCalcIds)) {
            log.info("统计需要补充计算进货达成奖励的终端======结束，之前核算过终端奖励的终端共{}个，具体有以下终端{}", terminalShopCalcIds.size(),terminalShopCalcIds);
            return RestResponse.error("统计需要补充计算进货达成奖励的终端======结束，之前核算过终端奖励的终端共" +terminalShopCalcIds.size()+"个,具体有以下终端:"+terminalShopCalcIds);
        }
//
        // 查询出来多少未激活，已合并的分店终端 terminalShopIds
        Map<String, List<Integer>> stringListMap = terminalShopCommonService.filterNotActiveMergedSubShopTerminal(terminalShopIds);
        List<Integer> notActiveShopIds = stringListMap.get("filterStatusEq0TerminalShopIds");
        List<Integer> subShopIds = stringListMap.get("filterSubShopIds");
        // 未激活、分店终端不做计算
        terminalShopIds.removeAll(notActiveShopIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList()));
        terminalShopIds.removeAll(subShopIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList()));

        ProtocoTimeParamResp packageProtocoTimeParamResp = terminalProductProtocolV2CommonService.getPackageQuantityTimeByProtocolTypeAndYearAndQuarter(year, quarter);
        //遍历并核算奖励列表
        displayResultService.asyncSupplementCalculateStockAchievedReward(packageProtocoTimeParamResp.getStartDate(), packageProtocoTimeParamResp.getEndDate(), terminalShopIds, year, quarter);

        if(CollectionUtils.isNotEmpty(notActiveShopIds)){
            log.info("统计进货达成未执行未激活的终端共{}个，具体有以下终端{}", notActiveShopIds.size(),notActiveShopIds);
            return RestResponse.success("统计进货达成未执行未激活的终端共"+notActiveShopIds.size()+"个，具体有以下终端"+notActiveShopIds);
        }
        if(CollectionUtils.isNotEmpty(subShopIds)){
            log.info("统计进货达成未执行已合并的分店终端共{}个，具体有以下终端{}", subShopIds.size(),subShopIds);
            return RestResponse.success("统计进货达成未执行已合并的分店终端共"+subShopIds.size()+"个，具体有以下终端"+subShopIds);
        }
        //List<TerminalProductProtocolRelationModel> packageRewardTerminals = new ArrayList<>();
        //
        //for (Long terminalShopId : terminalShopIds) {
        //    List<TerminalProductProtocolRelationModel> terminalProductProtocolRelationList = terminalProductProtocolV2CommonService.getProtocolTerminalIdsByProtocolTypeAndTerminalShopId(ProtocolTypeEnum.PACKAGE_QUANTITY.getKey(), packageProtocoTimeParamResp.getStartDate(), packageProtocoTimeParamResp.getEndDate(), terminalShopId);
        //    if (CollectionUtils.isNotEmpty(terminalProductProtocolRelationList)) {
        //        packageRewardTerminals.add(terminalProductProtocolRelationList.get(0));
        //    }
        //}
        ////筛选部分终端等级并且签署国标包量协议
        //Set<String> validLevelCodes = new HashSet<>(Arrays.asList("13", "12", "1", "2", "3", "4", "10", "11"));
        //List<TerminalProductProtocolRelationModel> stockAchievedTerminals = new ArrayList<>();
        //for (TerminalProductProtocolRelationModel terminalProductProtocolRelationModel : packageRewardTerminals) {
        //    if(validLevelCodes.contains(terminalProductProtocolRelationModel.getLevelCode()) && ProtocolProductCodeEnum.GUOBIAO.getKey().toString().equals(terminalProductProtocolRelationModel.getProtocolProductCode())){
        //        stockAchievedTerminals.add(terminalProductProtocolRelationModel);
        //    }
        //}
        //
        //if(CollectionUtils.isNotEmpty(stockAchievedTerminals)){
        //    log.info("统计需要补充计算进货达成奖励的终端======开始，终端奖励结果{}个,具体以下终端{}", stockAchievedTerminals.size(),stockAchievedTerminals);
        //    String batchNumber = terminalRewardCalculatorService.generateBatchNumber();
        //
        //    //遍历并核算奖励列表
        //    displayResultService.asyncCalculateStockAchievedReward(stockAchievedTerminals, batchNumber, year, quarter, TerminalRewardOperationTypeEnum.SUPPLEMENTARY_CALCULATION.getKey());
        //
        //    return RestResponse.success("统计需要补充进货达成奖励的终端======结束，批次号:"+batchNumber+"，终端奖励结果"+stockAchievedTerminals.size()+"个，具体有以下终端："+stockAchievedTerminals.stream().map(TerminalProductProtocolRelationModel::getTerminalShopId).collect(Collectors.toList()));
        //}else{
        //    log.info("统计需要补充进货达成奖励的终端======结束，无终端需要计算奖励");
        //    return RestResponse.success("统计需要补充进货达成奖励的终端结束，无终端需要计算奖励");
        //}
        return RestResponse.success("send message success");
//        }

    }


}
