package com.intelliquor.cloud.shop.terminal.model.enums;

import com.intelliquor.cloud.shop.terminal.model.resp.TerminalActivityTypeResp;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/6/27 15:41
 */
@Getter
@AllArgsConstructor
public enum TerminalActivityTypeEnum {

    /**
     * 会议
     */
    MEETING(1, "会议"),

    /**
     * 拜访
     */
    VISIT(2, "拜访"),
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (TerminalActivityTypeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

    /**
     * 根据key获取枚举
     */
    public static TerminalActivityTypeEnum getEnum(Integer key) {
        for (TerminalActivityTypeEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value;
            }
        }
        return null;
    }


    public static List<TerminalActivityTypeResp> arrayList = new ArrayList<>();

    public static Map<Integer, TerminalActivityTypeEnum> hashMap = new HashMap<>();

    //处理两个集合
    static{
        for(TerminalActivityTypeEnum terminalActivityTypeEnum : TerminalActivityTypeEnum.values()){
            //创建map填入List
            TerminalActivityTypeResp terminalActivityTypeResp = new TerminalActivityTypeResp();
            //会议类型
            terminalActivityTypeResp.setActivityTypeId(terminalActivityTypeEnum.key);
            //会议类型名称
            terminalActivityTypeResp.setActivityTypeName(terminalActivityTypeEnum.value);
            arrayList.add(terminalActivityTypeResp);

            //然后处理hashMap
            hashMap.put(terminalActivityTypeEnum.key, terminalActivityTypeEnum);
        }
    }

}
