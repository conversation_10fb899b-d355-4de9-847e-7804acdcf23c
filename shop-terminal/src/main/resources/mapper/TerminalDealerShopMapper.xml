<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalDealerShopDao">

    <!-- 新增云商用户信息 -->
    <insert id="insertCloudDealerInfo" parameterType="com.intelliquor.cloud.shop.terminal.model.req.TerminalDealerInfoModelReq"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_cloud_dealer_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="openId != null"> open_id, </if>
            <if test="type != null"> type, </if>
            <if test="dealerCode != null"> dealer_code, </if>
            <if test="dealerName != null"> dealer_name, </if>
            <if test="dealerType != null"> dealer_type, </if>
            <if test="dealerInviteCode != null"> dealer_invite_code, </if>
            <if test="shopInviteCode != null"> shop_invite_code, </if>
            <if test="superiorId != null"> superior_id, </if>
            <if test="dealerArea != null"> dealer_area, </if>
            <if test="linkman != null"> linkman, </if>
            <if test="phone != null"> phone, </if>
            <if test="address != null"> address, </if>
            <if test="companyId != null"> company_id, </if>
            <if test="createTime != null"> create_time, </if>
            <if test="status != null"> status, </if>
            <if test="isDelete != null"> is_delete, </if>
            <if test="weChartName != null"> we_chart_name, </if>
            <if test="headImgUrl != null"> head_img_url, </if>
            <if test="dealerWeChartGroupImgUrl != null"> dealer_we_chart_group_img_url, </if>
            <if test="shopWeChartGroupImgUrl != null"> shop_we_chart_group_img_url, </if>
            <if test="provinces != null"> provinces, </if>
            <if test="city != null"> city, </if>
            <if test="district != null"> district, </if>
            <if test="street != null"> street, </if>
            <if test="gtDealerId != null"> gt_dealer_id, </if>
            <if test="gtDealerData != null"> gt_dealer_data, </if>
            <if test="dealerAreaList != null"> dealer_area_list, </if>
            <if test="accountBalance != null"> account_balance, </if>
            <if test="longitude != null"> longitude, </if>
            <if test="latitude != null"> latitude, </if>
            <if test="storesImg != null"> stores_img, </if>
            <if test="contractImg != null"> contract_img, </if>
            <if test="licenseImg != null"> license_img, </if>
            <if test="remark != null"> remark, </if>
            <if test="accountType != null"> account_type, </if>
            <if test="level != null"> level, </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="openId != null"> #{openId}, </if>
            <if test="type != null"> #{type}, </if>
            <if test="dealerCode != null"> #{dealerCode}, </if>
            <if test="dealerName != null"> #{dealerName}, </if>
            <if test="dealerType != null"> #{dealerType}, </if>
            <if test="dealerInviteCode != null"> #{dealerInviteCode}, </if>
            <if test="shopInviteCode != null"> #{shopInviteCode}, </if>
            <if test="superiorId != null"> #{superiorId}, </if>
            <if test="dealerArea != null"> #{dealerArea}, </if>
            <if test="linkman != null"> #{linkman}, </if>
            <if test="phone != null"> #{phone}, </if>
            <if test="address != null"> #{address}, </if>
            <if test="companyId != null"> #{companyId}, </if>
            <if test="createTime != null"> #{createTime}, </if>
            <if test="status != null"> #{status}, </if>
            <if test="isDelete != null"> #{isDelete}, </if>
            <if test="weChartName != null"> #{weChartName}, </if>
            <if test="headImgUrl != null"> #{headImgUrl}, </if>
            <if test="dealerWeChartGroupImgUrl != null"> #{dealerWeChartGroupImgUrl}, </if>
            <if test="shopWeChartGroupImgUrl != null"> #{shopWeChartGroupImgUrl}, </if>
            <if test="provinces != null"> #{provinces}, </if>
            <if test="city != null"> #{city}, </if>
            <if test="district != null"> #{district}, </if>
            <if test="street != null"> #{street}, </if>
            <if test="gtDealerId != null"> #{gtDealerId}, </if>
            <if test="gtDealerData != null"> #{gtDealerData}, </if>
            <if test="dealerAreaList != null"> #{dealerAreaList}, </if>
            <if test="accountBalance != null"> #{accountBalance}, </if>
            <if test="longitude != null"> #{longitude}, </if>
            <if test="latitude != null"> #{latitude}, </if>
            <if test="storesImg != null"> #{storesImg}, </if>
            <if test="contractImg != null"> #{contractImg}, </if>
            <if test="licenseImg != null"> #{licenseImg}, </if>
            <if test="remark != null"> #{remark}, </if>
            <if test="accountType != null"> #{accountType}, </if>
            <if test="level != null"> #{level}, </if>
        </trim>
    </insert>

    <insert id="insertCloudDealerRelation">
        INSERT INTO t_cloud_dealer_relation
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="type != null ">
                type,
            </if>
            <if test="dealerId != null ">
                dealer_id,
            </if>
            <if test="parentId != null ">
                parent_id,
            </if>
            <if test="inviteCode != null ">
                invite_code,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="relationType != null ">
                relation_type,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="createRelationStaffId != null ">
                create_relation_staff_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="type != null ">
                #{type},
            </if>
            <if test="dealerId != null ">
                #{dealerId},
            </if>
            <if test="parentId != null ">
                #{parentId},
            </if>
            <if test="inviteCode != null ">
                #{inviteCode},
            </if>
            <if test="companyId != null ">
                #{companyId},
            </if>
            <if test="relationType != null ">
                #{relationType},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="createRelationStaffId != null ">
                #{createRelationStaffId}
            </if>
        </trim>
    </insert>

    <update id="updateCloudDealerInfo" parameterType="com.intelliquor.cloud.shop.terminal.model.req.TerminalDealerInfoModelReq">
        update t_cloud_dealer_info
        <set>
            <if test="openId != null"> open_id = #{openId}, </if>
            <if test="type != null"> type = #{type}, </if>
            <if test="dealerCode != null"> dealer_code = #{dealerCode}, </if>
            <if test="dealerName != null"> dealer_name = #{dealerName}, </if>
            <if test="dealerType != null"> dealer_type = #{dealerType}, </if>
            <if test="dealerInviteCode != null"> dealer_invite_code = #{dealerInviteCode}, </if>
            <if test="shopInviteCode != null"> shop_invite_code = #{shopInviteCode}, </if>
            <if test="superiorId != null"> superior_id = #{superiorId}, </if>
            <if test="dealerArea != null"> dealer_area = #{dealerArea}, </if>
            <if test="linkman != null"> linkman = #{linkman}, </if>
            <if test="phone != null"> phone = #{phone}, </if>
            <if test="address != null"> address = #{address}, </if>
            <if test="companyId != null"> company_id = #{companyId}, </if>
            <if test="createTime != null"> create_time = #{createTime}, </if>
            <if test="status != null"> status = #{status}, </if>
            <if test="isDelete != null"> is_delete = #{isDelete}, </if>
            <if test="weChartName != null"> we_chart_name = #{weChartName}, </if>
            <if test="headImgUrl != null"> head_img_url = #{headImgUrl}, </if>
            <if test="dealerWeChartGroupImgUrl != null"> dealer_we_chart_group_img_url = #{dealerWeChartGroupImgUrl}, </if>
            <if test="shopWeChartGroupImgUrl != null"> shop_we_chart_group_img_url = #{shopWeChartGroupImgUrl}, </if>
            <if test="provinces != null"> provinces = #{provinces}, </if>
            <if test="city != null"> city = #{city}, </if>
            <if test="district != null"> district = #{district}, </if>
            <if test="street != null"> street = #{street}, </if>
            <if test="gtDealerId != null"> gt_dealer_id = #{gtDealerId}, </if>
            <if test="gtDealerData != null"> gt_dealer_data = #{gtDealerData}, </if>
            <if test="dealerAreaList != null"> dealer_area_list = #{dealerAreaList}, </if>
            <if test="accountBalance != null"> account_balance = #{accountBalance}, </if>
            <if test="longitude != null"> longitude = #{longitude}, </if>
            <if test="latitude != null"> latitude = #{latitude}, </if>
            <if test="storesImg != null"> stores_img = #{storesImg}, </if>
            <if test="contractImg != null"> contract_img = #{contractImg}, </if>
            <if test="licenseImg != null"> license_img = #{licenseImg}, </if>
            <if test="remark != null"> remark = #{remark}, </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteCloudDealerRelationByDealerId">
        delete
        from
        t_cloud_dealer_relation
        where
        dealer_id = #{dealerId}
    </delete>

    <select id="selectDealerIdByDealerCode" resultType="com.intelliquor.cloud.shop.terminal.model.req.TerminalDealerInfoModelReq">
        select
        id,
        dealer_code as dealerCode,
        dealer_name as dealerName,
        phone,
        account_type
        from
        t_cloud_dealer_info
        where
        dealer_code = #{dealerCode}
    </select>

    <insert id="insertCloudDealerGtTerminalApply" parameterType="com.intelliquor.cloud.shop.terminal.model.req.TerminalDealerGtTerminalReq">
        INSERT INTO t_cloud_dealer_gt_terminal_apply
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="zhongtaiId != null ">
                zhongtai_id,
            </if>
            <if test="contractCode != null ">
                contract_code,
            </if>
            <if test="channelCode != null ">
                channel_code,
            </if>
            <if test="channelName != null ">
                channel_name,
            </if>
            <if test="terminalCode != null ">
                terminal_code,
            </if>
            <if test="deputyCode != null ">
                deputy_code,
            </if>
            <if test="terminalName != null ">
                terminal_name,
            </if>
            <if test="licenseCode != null ">
                license_code,
            </if>
            <if test="licenseImg != null ">
                license_img,
            </if>
            <if test="phone != null ">
                phone,
            </if>
            <if test="linkman != null ">
                linkman,
            </if>
            <if test="storesImg != null ">
                stores_img,
            </if>
            <if test="province != null ">
                province,
            </if>
            <if test="city != null ">
                city,
            </if>
            <if test="district != null ">
                district,
            </if>
            <if test="address != null ">
                address,
            </if>
            <if test="longitude != null ">
                longitude,
            </if>
            <if test="latitude != null ">
                latitude,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="isDelete != null ">
                is_delete
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="zhongtaiId != null ">
                #{zhongtaiId},
            </if>
            <if test="contractCode != null ">
                #{contractCode},
            </if>
            <if test="channelCode != null ">
                #{channelCode},
            </if>
            <if test="channelName != null ">
                #{channelName},
            </if>
            <if test="terminalCode != null ">
                #{terminalCode},
            </if>
            <if test="deputyCode != null ">
                #{deputyCode},
            </if>
            <if test="terminalName != null ">
                #{terminalName},
            </if>
            <if test="licenseCode != null ">
                #{licenseCode},
            </if>
            <if test="licenseImg != null ">
                #{licenseImg},
            </if>
            <if test="phone != null ">
                #{phone},
            </if>
            <if test="linkman != null ">
                #{linkman},
            </if>
            <if test="storesImg != null ">
                #{storesImg},
            </if>
            <if test="province != null ">
                #{province},
            </if>
            <if test="city != null ">
                #{city},
            </if>
            <if test="district != null ">
                #{district},
            </if>
            <if test="address != null ">
                #{address},
            </if>
            <if test="longitude != null ">
                #{longitude},
            </if>
            <if test="latitude != null ">
                #{latitude},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateTime != null ">
                #{updateTime},
            </if>
            <if test="companyId != null ">
                #{companyId},
            </if>
            <if test="isDelete != null ">
                #{isDelete}
            </if>
        </trim>
    </insert>
    <select id="getRelationNum" resultType="java.lang.Integer">
        select count(*) from t_cloud_dealer_relation where dealer_id = #{dealerId} and parent_id = #{parentId}
    </select>

    <select id="selectDealerInfoById" resultType="com.intelliquor.cloud.shop.terminal.model.req.TerminalDealerInfoModelReq">
        select
        id,
        dealer_code as dealerCode,
        dealer_name as dealerName,
        phone
        from
        t_cloud_dealer_info
        where
        id = #{id}
    </select>
</mapper>