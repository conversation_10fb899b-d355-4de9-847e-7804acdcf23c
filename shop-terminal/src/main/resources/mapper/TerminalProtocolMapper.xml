<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalProtocolDao">
    <delete id="deleteByShopId">
        delete
            t_terminal_protocol,
            t_terminal_protocol_product
        from t_terminal_protocol
                 left join
             t_terminal_protocol_product
             on t_terminal_protocol.id = t_terminal_protocol_product.protocol_id
        where t_terminal_protocol.terminal_shop_id = #{shopId}
    </delete>
    <select id="selectProtocolByTerminalShopId"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalProtocolModel">
        select * from t_terminal_protocol  where protocol_type = 0 and terminal_shop_id = #{terminalShopId} limit 1
    </select>

    <update id="updateStatus">
        update t_terminal_protocol
        set check_status = #{status}
        where id = #{id}
    </update>

    <select id="getLevelNameByLevelCode" resultType="java.lang.String">
        select level_name from t_terminal_shop_level where id = #{levelCode}
    </select>
    <!--临时使用-->
    <select id="selectProtocolList"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalProtocolModel">
        SELECT t.* FROM t_terminal_protocol t where t.protocol_type=0 and t.protocol_property=0 and t.check_status=1 and end_status=2
                                                and terminal_shop_id in(SELECT terminal_shop_id FROM t_display_result WHERE id in (
                SELECT display_result_id FROM t_display_result_detail GROUP BY display_result_id))  order by create_time
    </select>

    <update id="deleteProtocolById">
        UPDATE t_terminal_protocol SET delete_status = 1 WHERE id = #{id}
    </update>
    <select id="selectProtocolListByTerminalShopId"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalProtocolModel">
        select a.id as id,
            a.protocol_type as protocolType,
            a.protocol_property as protocolProperty,
            a.product_type as productType,
            a.level_code as levelCode,
            a.dealer_code as dealerCode,
            a.protocol_image as protocolImage,
            a.member_shop_id as memberShopId,
            a.terminal_shop_id as terminalShopId,
            a.product_protocol_config_id as productProtocolConfigId,
            a.company_id as companyId,
            a.package_quantity_name as packageQuantityName,
            a.package_quantity_display_surface as packageQuantityDisplaySurface,
            a.package_quantity_display_cost as packageQuantityDisplayCost,
            a.package_quantity_replenish_stock_quantity as packageQuantityReplenishStockQuantity,
            a.display_name as displayName,
            a.display_display_surface as displayDisplaySurface,
            a.display_display_cost as displayDisplayCost,
            a.display_replenish_stock_quantity as displayReplenishStockQuantity,
            b.display_surface as displaySurface,
            b.month_scan_in_num as monthScanInNum,
            b.year_scan_in_num as yearScanInNum,
            b.display_amount as displayAmount,
            b.package_amount as packageAmount,
            a.check_status as checkStatus,
            a.end_status as endStatus,
            a.delete_status as deleteStatus,
            a.create_time as createTime,
            a.create_user as createUser,
            a.update_time as updateTime,
            a.update_user as updateUser,
            a.effective_time as effectiveTime,
            a.finish_status as finishStatus,
            c.level_name as levelName
        from t_terminal_protocol a
        left join t_terminal_product_protocol b on a.product_protocol_config_id = b.id
        left join t_terminal_shop_level c on a.level_code = c.id
        where a.delete_status = 0 and a.terminal_shop_id = #{terminalShopId};
    </select>
    <select id="selectProtocolListByTerminalShopId2"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolModelResp">
        select a.id as id,
            a.protocol_type as protocolType,
            a.protocol_property as protocolProperty,
            a.product_type as productType,
            a.level_code as levelCode,
            a.dealer_code as dealerCode,
            a.protocol_image as protocolImage,
            a.member_shop_id as memberShopId,
            a.terminal_shop_id as terminalShopId,
            a.product_protocol_config_id as productProtocolConfigId,
            a.company_id as companyId,
            a.package_quantity_name as packageQuantityName,
            a.package_quantity_display_surface as packageQuantityDisplaySurface,
            a.package_quantity_display_cost as packageQuantityDisplayCost,
            a.package_quantity_replenish_stock_quantity as packageQuantityReplenishStockQuantity,
            a.display_name as displayName,
            a.display_display_surface as displayDisplaySurface,
            a.display_display_cost as displayDisplayCost,
            a.display_replenish_stock_quantity as displayReplenishStockQuantity,
            b.display_surface as displaySurface,
            b.month_scan_in_num as monthScanInNum,
            b.year_scan_in_num as yearScanInNum,
            b.display_amount as displayAmount,
            b.package_amount as packageAmount,
            a.check_status as checkStatus,
            a.end_status as endStatus,
            a.delete_status as deleteStatus,
            a.create_time as createTime,
            a.create_user as createUser,
            a.update_time as updateTime,
            a.update_user as updateUser,
            a.effective_time as effectiveTime,
            a.finish_status as finishStatus,
            c.level_name as levelName
        from t_terminal_protocol a
        left join t_terminal_product_protocol b on a.product_protocol_config_id = b.id
        left join t_terminal_shop_level c on a.level_code = c.id
        where a.delete_status = 0 and a.terminal_shop_id = #{terminalShopId};
    </select>
    <select id="selectProtocolListByTerminalShopId3"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolModelResp">
        select a.id as id,
            a.protocol_type as protocolType,
            a.protocol_property as protocolProperty,
            a.product_type as productType,
            a.level_code as levelCode,
            a.dealer_code as dealerCode,
            a.protocol_image as protocolImage,
            a.member_shop_id as memberShopId,
            a.terminal_shop_id as terminalShopId,
            a.product_protocol_config_id as productProtocolConfigId,
            a.company_id as companyId,
            a.package_quantity_name as packageQuantityName,
            a.package_quantity_display_surface as packageQuantityDisplaySurface,
            a.package_quantity_display_cost as packageQuantityDisplayCost,
            a.package_quantity_replenish_stock_quantity as packageQuantityReplenishStockQuantity,
            a.display_name as displayName,
            a.display_display_surface as displayDisplaySurface,
            a.display_display_cost as displayDisplayCost,
            a.display_replenish_stock_quantity as displayReplenishStockQuantity,
            b.display_surface as displaySurface,
            b.month_scan_in_num as monthScanInNum,
            b.year_scan_in_num as yearScanInNum,
            b.display_amount as displayAmount,
            b.package_amount as packageAmount,
            a.check_status as checkStatus,
            a.end_status as endStatus,
            a.delete_status as deleteStatus,
            a.create_time as createTime,
            a.create_user as createUser,
            a.update_time as updateTime,
            a.update_user as updateUser,
            a.effective_time as effectiveTime,
            a.finish_status as finishStatus,
            c.level_name as levelName
        from t_terminal_protocol a
        left join t_terminal_product_protocol b on a.product_protocol_config_id = b.id
        left join t_terminal_shop_level c on a.level_code = c.id
        where a.delete_status = 0 and a.protocol_type != 2 and a.terminal_shop_id = #{terminalShopId};
    </select>
    <select id="getProtocolById"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolModelResp">
        select a.id as id,
            a.protocol_type as protocolType,
            a.protocol_property as protocolProperty,
            a.product_type as productType,
            a.level_code as levelCode,
            a.dealer_code as dealerCode,
            a.protocol_image as protocolImage,
            a.member_shop_id as memberShopId,
            a.terminal_shop_id as terminalShopId,
            a.product_protocol_config_id as productProtocolConfigId,
            a.company_id as companyId,
            a.package_quantity_name as packageQuantityName,
            a.package_quantity_display_surface as packageQuantityDisplaySurface,
            a.package_quantity_display_cost as packageQuantityDisplayCost,
            a.package_quantity_replenish_stock_quantity as packageQuantityReplenishStockQuantity,
            a.display_name as displayName,
            a.display_display_surface as displayDisplaySurface,
            a.display_display_cost as displayDisplayCost,
            a.display_replenish_stock_quantity as displayReplenishStockQuantity,
            b.display_surface as displaySurface,
            b.month_scan_in_num as monthScanInNum,
            b.year_scan_in_num as yearScanInNum,
            b.display_amount as displayAmount,
            b.package_amount as packageAmount,
            a.check_status as checkStatus,
            a.end_status as endStatus,
            a.delete_status as deleteStatus,
            a.create_time as createTime,
            a.create_user as createUser,
            a.update_time as updateTime,
            a.update_user as updateUser,
            a.effective_time as effectiveTime,
            a.finish_status as finishStatus,
            c.level_name as levelName
        from t_terminal_protocol a
        left join t_terminal_product_protocol b on a.product_protocol_config_id = b.id
        left join t_terminal_shop_level c on a.level_code = c.id
        where a.id = #{id};
    </select>
    <select id="getTerminalProtocolInfoList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolStockResp">
        SELECT
	        a.id AS id,
	        a.level_code AS levelCode,
	        a.protocol_type AS protocolType,
	        a.member_shop_id AS memberShopId,
	        a.effective_time AS effectiveTime,
	        b.year_scan_in_num AS yearScanInNum
        FROM
	        t_terminal_protocol a
	    LEFT JOIN t_terminal_product_protocol b ON a.product_protocol_config_id = b.id
        WHERE
	        a.finish_status = 1
	        AND a.check_status = 1
	        AND a.delete_status = 0
	        AND a.product_protocol_config_id > 0
	        AND a.effective_time IS NOT NULL
	        AND b.year_scan_in_num > 0
    </select>
    <select id="getStockNumberByShopIdAndTime" resultType="integer">
        SELECT IFNULL(sum( b.shop_received_qyt ) ,0)
        FROM t_shop_dealer_order a
	    LEFT JOIN t_shop_dealer_order_detail b ON a.order_code = b.order_code
        WHERE
	        a.shop_id = #{shopId}
            AND a.order_status in (2,3)
	        AND a.create_time <![CDATA[ >= ]]> #{beginTime} and a.create_time <![CDATA[ <= ]]> #{endTime}
        GROUP BY a.shop_id
    </select>
    <select id="getYearScanInNumByShopIdAndYear" resultType="integer">
        SELECT
	        IFNULL(sum( b.year_scan_in_num ) ,0)
        FROM
	        t_terminal_protocol a
	        LEFT JOIN t_terminal_product_protocol b ON a.product_protocol_config_id = b.id
        WHERE
            a.member_shop_id = #{memberShopId}
	        AND a.finish_status = 1
	        AND a.check_status = 1
	        AND a.delete_status = 0
	        AND a.product_protocol_config_id > 0
	        AND DATE_FORMAT( a.effective_time, '%Y' ) = #{year}
	        AND b.year_scan_in_num > 0
    </select>
    <update id="updateFinishStatusByIds">
        update t_terminal_protocol set finish_status = 2
        where id in
        <foreach collection="idList" item="item" index="index"  open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>




    <select id="todoProtocol" resultType="com.intelliquor.cloud.shop.common.model.vo.TerminalProtocolModelVo">
        SELECT
            b.shop_name,
            b.leader_name,
            b.leader_phone,
            a.*
        FROM
            t_terminal_protocol a,
            t_terminal_shop b
        WHERE
            a.terminal_shop_id = b.id
            AND a.protocol_type = 0
            AND a.delete_status = 0
            AND a.level_code != ''
            AND a.effective_time LIKE '2023-03-13%'
            AND b.level_code != ''
            AND a.check_status IN ( 3, 5 )
            AND b.create_user IN
            <foreach collection="userIds" item="userId"  open="(" separator="," close=")">
                #{userId}
            </foreach>
            AND a.id NOT IN ( SELECT protocol_id FROM t_terminal_shop_node )
        GROUP BY
            a.terminal_shop_id
    </select>
    <select id="getProtocolTotalDisplaySurface"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalProtocolModel">
        select mm.product_type as productType, sum(nn.display_surface) as displaySurface
        from t_terminal_protocol mm
        left join t_terminal_product_protocol nn on mm.product_protocol_config_id = nn.id
        where mm.terminal_shop_id = #{id}
            and mm.check_status = 1
            and mm.delete_status = 0
            and mm.effective_time is not null
        GROUP BY mm.product_type
    </select>
    <select id="getMaxLevelProtocolList"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.MaxLevelProtocalResp">
        select
            t.*,
            tl.id AS "levelId",
            tl.level_name AS "levelName"
        from
            t_terminal_protocol t,
            t_terminal_shop_level tl
        where
            t.level_code = tl.id
            and t.check_status=1
            and t.create_time &lt;=  #{req.queryDate}
            and t.member_shop_id = #{req.shopId}
        order by
            t.create_time desc,
            t.level_code desc
    </select>

    <select id="getProtocolByShopIds"
            resultType="com.intelliquor.cloud.shop.common.model.TerminalProtocolModel">
        select member_shop_id, min(level_code) as level_code
                from t_terminal_protocol
        --         与智赢沟通，不需要判断协议是否审核通过
        where protocol_type = 0 and delete_status = 0
            and member_shop_id in
            <foreach item="item" index="index" collection="shopIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        --         where protocol_type = 0 and delete_status = 0 and check_status = 1 and effective_time is not null
        group by member_shop_id
    </select>
</mapper>
