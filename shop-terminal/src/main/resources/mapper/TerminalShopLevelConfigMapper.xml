<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalShopLevelConfigDao">

    <select id="selectTerminalShopLevelConfig" resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalLevelCountResp">
        select
        level_code as levelCode,
        level_name as levelName,
        level_short_name as levelShortName,
        box_min_number as boxMinNumber,
        box_max_number as boxMaxNumber
        from
        t_terminal_shop_level_config
        order by sort asc
    </select>
</mapper>
