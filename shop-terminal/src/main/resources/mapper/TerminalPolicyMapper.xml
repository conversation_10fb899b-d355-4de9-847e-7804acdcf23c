<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalPolicyDao">

    <sql id="baseColumn">
        id,
        title,
        sub_title,
        type,
        img,
        content,
        start_time,
        end_time,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        company_id,
        is_delete,
        account_type
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.terminal.model.TerminalPolicyModel">
            <id property="id" column="id"/>
            <result property="title" column="title"/>
            <result property="subTitle" column="sub_title"/>
            <result property="type" column="type"/>
            <result property="img" column="img"/>
            <result property="content" column="content"/>
            <result property="startTime" column="start_time"/>
            <result property="endTime" column="end_time"/>
            <result property="createTime" column="create_time"/>
            <result property="createUserId" column="create_user_id"/>
            <result property="createUserName" column="create_user_name"/>
            <result property="updateTime" column="update_time"/>
            <result property="updateUserId" column="update_user_id"/>
            <result property="updateUserName" column="update_user_name"/>
            <result property="companyId" column="company_id"/>
            <result property="isDelete" column="is_delete"/>
        <result property="accountType" column="account_type"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            is_delete = 0
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="startTime != null">
                and ( start_time is null or start_time &lt;= #{startTime})
            </if>
            <if test="endTime != null">
                and (end_time is null or end_time >= #{endTime})
            </if>
            <if test="accountType != null">
                and account_type = #{accountType}
            </if>
        </where>
    </sql>


    <select id="selectList" resultType="com.intelliquor.cloud.shop.terminal.model.TerminalPolicyModel">
        select * from t_terminal_policy
        <include refid="selectiveWhere"/>
        ORDER BY id desc
    </select>

    <select id="selectList4Admin" resultMap="baseResultMap">
        SELECT
        p.*
        FROM t_terminal_policy p
        <where>
            p.is_delete = 0
            <if test="companyId != null">
                and p.company_id = #{companyId}
            </if>
            <if test="type != null">
                and p.type = #{type}
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and p.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and p.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="month != null and month != ''">
                and date_format(p.create_time, '%Y-%m') = #{month}
            </if>
            <if test="id != null">
                and p.id = #{id}
            </if>
        </where>
        order by p.id desc
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.terminal.model.TerminalPolicyModel">
        INSERT INTO t_terminal_policy
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="title != null ">
            title,
            </if>
            <if test="subTitle != null ">
            sub_title,
            </if>
            <if test="type != null ">
            type,
            </if>
            <if test="img != null ">
            img,
            </if>
            <if test="content != null ">
            content,
            </if>
            <if test="startTime != null ">
            start_time,
            </if>
            <if test="endTime != null ">
            end_time,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="createUserId != null ">
            create_user_id,
            </if>
            <if test="createUserName != null ">
            create_user_name,
            </if>
            <if test="updateTime != null ">
            update_time,
            </if>
            <if test="updateUserId != null ">
            update_user_id,
            </if>
            <if test="updateUserName != null ">
            update_user_name,
            </if>
            <if test="companyId != null ">
            company_id,
            </if>
            <if test="isDelete != null ">
            is_delete,
            </if>
        <if test="accountType != null ">
            account_type
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="title != null ">
            #{title},
            </if>
            <if test="subTitle != null ">
            #{subTitle},
            </if>
            <if test="type != null ">
            #{type},
            </if>
            <if test="img != null ">
            #{img},
            </if>
            <if test="content != null ">
            #{content},
            </if>
            <if test="startTime != null ">
            #{startTime},
            </if>
            <if test="endTime != null ">
            #{endTime},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="createUserId != null ">
            #{createUserId},
            </if>
            <if test="createUserName != null ">
            #{createUserName},
            </if>
            <if test="updateTime != null ">
            #{updateTime},
            </if>
            <if test="updateUserId != null ">
            #{updateUserId},
            </if>
            <if test="updateUserName != null ">
            #{updateUserName},
            </if>
            <if test="companyId != null ">
            #{companyId},
            </if>
            <if test="isDelete != null ">
            #{isDelete},
            </if>
        <if test="accountType != null ">
            #{accountType}
        </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.terminal.model.TerminalPolicyModel">
        UPDATE t_terminal_policy
        <set>
            <if test="title != null and title != ''">
            title = #{title},
            </if>
            <if test="subTitle != null and subTitle != ''">
            sub_title = #{subTitle},
            </if>
            <if test="type != null ">
            type = #{type},
            </if>
            <if test="img != null and img != ''">
            img = #{img},
            </if>
            <if test="content != null and content !=''">
            content = #{content},
            </if>
            <if test="startTime != null ">
            start_time = #{startTime},
            </if>
            <if test="endTime != null ">
            end_time = #{endTime},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
            <if test="createUserId != null ">
            create_user_id = #{createUserId},
            </if>
            <if test="createUserName != null and createUserName != ''">
            create_user_name = #{createUserName},
            </if>
            <if test="updateTime != null ">
            update_time = #{updateTime},
            </if>
            <if test="updateUserId != null ">
            update_user_id = #{updateUserId},
            </if>
            <if test="updateUserName != null and updateUserName !=''">
            update_user_name = #{updateUserName},
            </if>
            <if test="companyId != null ">
            company_id = #{companyId},
            </if>
            <if test="isDelete != null ">
            is_delete = #{isDelete},
            </if>
            <if test="accountType != null ">
                account_type = #{accountType}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_terminal_policy
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_terminal_policy
        WHERE id = #{id}
    </select>

</mapper>