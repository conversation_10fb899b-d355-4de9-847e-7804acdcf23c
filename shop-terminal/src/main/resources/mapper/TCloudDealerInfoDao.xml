<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TCloudDealerInfoDao">
    <select id="getList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.CloudDealerInfoResp">
        select i.id, i.open_id, i.type, i.dealer_code, i.dealer_name, i.dealer_type, i.dealer_invite_code,
        i.shop_invite_code, i.superior_id, i.dealer_area, i.linkman, i.phone, i.address, i.company_id, i.create_time,
        i.update_time, i.status, i.is_delete, i.we_chart_name, i.head_img_url, i.dealer_we_chart_group_img_url,
        i.shop_we_chart_group_img_url, i.provinces, i.city, i.district, i.street, i.gt_dealer_id, i.gt_dealer_data,
        i.dealer_area_list, i.account_balance, i.longitude, i.latitude, i.stores_img, i.contract_img, i.license_img,
        i.remark, i.level, i.score, i.union_id, i.account_type, i.virtual_amount, i.create_user_id, i.store_provinces,
        i.store_city, i.store_district, i.store_street, i.store_address, i.contract_code,
        CASE WHEN i.account_type = 3 THEN d.contract_type WHEN i.account_type = 4 THEN i.contract_type ELSE i.contract_type END AS contract_type,
        i.contract_name, d.contract_no, parent_dealer_code
        from t_cloud_dealer_info i
        left join t_cloud_dealer_info_detail d on i.id = d.dealer_id
        <where>
            i.status=1 and i.is_delete=1
            <if test="dealerCode != null and dealerCode != ''">
                and i.dealer_code = #{dealerCode}
            </if>
            <if test="dealerName != null and dealerName != ''">
                and i.dealer_name like CONCAT('%', #{dealerName}, '%')
            </if>
            <if test="accountType != null">
                and i.account_type = #{accountType}
            </if>
            <if test="superiorId != null">
                and i.superior_id = #{superiorId}
            </if>
        </where>
    </select>

    <select id="selectCheckList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributorResp">
        select tcdi.id                   AS dealerId,
               tcdi.dealer_name,
               tcdi.linkman,
               tcdi.phone,
               tcdii.dealer_name         AS parentDealerName,
               IF(tcdi.status = 1, 3, 0) AS checkStatus
        from t_cloud_dealer_info tcdi
                 left join t_cloud_dealer_info tcdii on (tcdi.superior_id = tcdii.id and tcdii.account_type = 1)
        where tcdi.is_delete = 1
          and tcdi.status = #{checkStatus}
          and tcdi.account_type = 3
        <if test="createUserId != null">
            and tcdi.create_user_id = #{createUserId}
        </if>
        order by tcdi.create_time DESC
    </select>

    <select id="selectDetailSchedule" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributorResp">
        select tcdi.dealer_info_id        AS dealer_id,
               tcdi.id                    AS scheduleId,
               tcdi.superior_id,
               tcdii.dealer_code          AS parentDealerCode,
               tcdii.dealer_name          AS parentDealerName,
               tcdid.dealer_business_area AS dealerBusinessArea,
               tcdi.dealer_name,
               tcdi.dealer_code,
               tcdid.contract_name,
               tcdid.contract_no,
               tcdid.contract_type,
               tcdid.parent_dealer_code,
               tcdi.dealer_area_list,
               tcdi.linkman,
               tcdi.phone,
               tcdi.check_status          AS checkStatus,
               tcdi.address,
               tcdi.provinces,
               tcdi.city,
               tcdi.district,
               tcdi.license_img,
               tcdi.stores_img,
               tcdid.legal_name,
               tcdid.office_env_image,
               tcdid.stock_env_image,
               tcdid.high_dealer_status,
               tcdid.goods_codes,
               tcdid.cooper_status,
               tcdid.business_goods,
               tcdid.business_start_time,
               tcdid.business_end_time,
               tcdid.last_order_time,
               tcdid.direct_terminal_status,
               tcdid.old_terminal_name,
               tcdid.old_terminal_code,
               tcdid.old_goods_type,
               tcdid.old_dealer_name,
               tcdid.old_dealer_code,
               tcdid.team_status,
               tcdid.team_num,
               tcdid.wine_env_info,
               tcdid.main_price_range,
               tcdid.legal_social_rel,
               tcdid.leader_social_rel,
               tcdid.legal_leader_rel,
               tcdid.license_code
        from t_cloud_dealer_info_schedule tcdi
                 left join t_cloud_dealer_info tcdii on (tcdi.superior_id = tcdii.id and tcdii.account_type = 1)
                 left join t_cloud_dealer_info_detail_schedule tcdid on tcdi.id = tcdid.schedule_id
        where tcdi.dealer_info_id = #{dealerId}
          AND tcdi.is_delete = 1
          and tcdi.account_type = 3
    </select>


    <select id="selectDetail" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributorResp">
        select tcdi.id                                               AS dealer_id,
               tcdis.id                                              AS scheduleId,
               tcdi.superior_id,
               tcdii.dealer_code                                     AS parentDealerCode,
               tcdii.dealer_name                                     AS parentDealerName,
               tcdid.dealer_business_area                            AS dealerBusinessArea,
               tcdi.dealer_name,
               tcdi.dealer_code,
               tcdid.contract_name,
               tcdid.contract_no,
               tcdid.contract_type,
               tcdid.parent_dealer_code,
               tcdi.dealer_area_list,
               tcdi.linkman,
               tcdi.phone,
               IFNULL(tcdis.check_status, IF(tcdi.status = 1, 3, 0)) AS checkStatus,
               tcdi.address,
               tcdi.provinces,
               tcdi.city,
               tcdi.district,
               tcdi.license_img,
               tcdi.stores_img,
               tcdid.legal_name,
               tcdid.office_env_image,
               tcdid.stock_env_image,
               tcdid.high_dealer_status,
               tcdid.goods_codes,
               tcdid.cooper_status,
               tcdid.business_goods,
               tcdid.business_start_time,
               tcdid.business_end_time,
               tcdid.last_order_time,
               tcdid.direct_terminal_status,
               tcdid.old_terminal_name,
               tcdid.old_terminal_code,
               tcdid.old_goods_type,
               tcdid.old_dealer_name,
               tcdid.old_dealer_code,
               tcdid.team_status,
               tcdid.team_num,
               tcdid.wine_env_info,
               tcdid.main_price_range,
               tcdid.legal_social_rel,
               tcdid.leader_social_rel,
               tcdid.legal_leader_rel,
               tcdid.license_code
        from t_cloud_dealer_info tcdi
                 left join t_cloud_dealer_info tcdii on (tcdi.superior_id = tcdii.id and tcdii.account_type = 1)
                 left join t_cloud_dealer_info_schedule tcdis on tcdis.dealer_info_id = tcdi.id
                 left join t_cloud_dealer_info_detail tcdid on tcdi.id = tcdid.dealer_id
        where tcdi.id = #{dealerId}
          AND tcdi.is_delete = 1
          and tcdi.account_type = 3
    </select>



    <select id="selectHhrDetail" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributorResp">
        select tcdi.id                                               AS dealer_id,
               tcdis.id                                              AS scheduleId,
               tcdi.superior_id,
               tcdii.dealer_code                                     AS parentDealerCode,
               tcdii.dealer_name                                     AS parentDealerName,
               tcdi.dealer_area                                      AS dealerBusinessArea,
               tcdi.dealer_name,
               tcdi.dealer_code,
               tcdi.contract_name,
               tcdi.contract_code as contractNo,
               tcdi.contract_type,
               tcdi.dealer_area_list,
               tcdi.linkman,
               tcdi.phone,
               IFNULL(tcdis.check_status, IF(tcdi.status = 1, 3, 0)) AS checkStatus,
               tcdi.address,
               tcdi.provinces,
               tcdi.city,
               tcdi.district,
               tcdi.license_img,
               tcdi.stores_img
        from t_cloud_dealer_info tcdi
                 left join t_cloud_dealer_info tcdii on (tcdi.superior_id = tcdii.id)
                 left join t_cloud_dealer_info_schedule tcdis on tcdis.dealer_info_id = tcdi.id
        where tcdi.id = #{dealerId}
    </select>

    <select id="selectAllList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributorResp">
        select *
        from (select tcdi.id                   AS dealerId,
                     tcdi.dealer_name,
                     tcdi.linkman,
                     tcdi.phone,
                     tcdii.dealer_name         AS parentDealerName,
                     IF(tcdi.status = 1, 3, 0) AS checkStatus,
                     tcdi.create_time
              from t_cloud_dealer_info tcdi
                       left join t_cloud_dealer_info tcdii on (tcdi.superior_id = tcdii.id and tcdii.account_type = 1)
                       LEFT JOIN t_cloud_dealer_info_schedule tcdis ON tcdis.dealer_info_id = tcdi.id
        where tcdi.is_delete = 1
          and tcdi.account_type = 3
          AND tcdis.id is null
        <if test="createUserId != null">
            and tcdi.create_user_id = #{createUserId}
        </if>
        UNION ALL
        select tcdi.dealer_info_id AS dealerId,
               tcdi.dealer_name,
               tcdi.linkman,
               tcdi.phone,
               tcdii.dealer_name   AS parentDealerName,
               tcdi.check_status   AS checkStatus,
               tcdi.create_time
        from t_cloud_dealer_info_schedule tcdi
                 left join t_cloud_dealer_info tcdii
                           on (tcdi.superior_id = tcdii.id and tcdii.account_type = 1)
        where tcdi.is_delete = 1
          and tcdi.account_type = 3
        <if test="createUserId != null">
            and tcdi.create_user_id = #{createUserId}
        </if>
        ) t
        order by t.create_time DESC
    </select>

    <select id="distributorSelectList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributorResp">
        select tcdi.id AS dealerId,
                tcdi.dealer_name,
                tcdi.dealer_area_list
        from t_cloud_dealer_info tcdi
                 left join t_cloud_dealer_info_detail tcdid on tcdi.id = tcdid.dealer_id
        where tcdi.status = 1
          and tcdi.is_delete = 1
          and account_type = 3
          and tcdid.contract_no = #{contractCode}
    </select>

    <select id="selectAreaInfoByContractNo" resultType="java.lang.String">
        select IFNULL(tcdi.dealer_area_list, '') AS dealerAreaList
        from t_cloud_dealer_info tcdi
                 left join t_cloud_dealer_info_detail tcdid on tcdi.id = tcdid.dealer_id
        where account_type = 3
          and tcdid.contract_no = #{contractNo}
        <if test="dealerId != null">
            and tcdi.id != #{dealerId}
        </if>
    </select>

    <select id="getByDealerCode" resultType="com.intelliquor.cloud.shop.terminal.model.TCloudDealerInfoModel">
        SELECT id, open_id, type, dealer_code, dealer_name, dealer_type, dealer_invite_code, shop_invite_code,
        superior_id, dealer_area, linkman, phone, address, company_id, create_time, update_time, status, is_delete,
        we_chart_name, head_img_url, dealer_we_chart_group_img_url, shop_we_chart_group_img_url, provinces, city,
        district, street, gt_dealer_id, gt_dealer_data, dealer_area_list, account_balance, longitude, latitude,
        stores_img, contract_img, license_img, remark, level, score, union_id, account_type, virtual_amount,
        create_user_id, store_provinces, store_city, store_district, store_street, store_address, contract_code,
        contract_type, contract_name FROM t_cloud_dealer_info WHERE dealer_code = #{dealerCode} AND is_delete = 1
    </select>

    <select id="getCopartnerList" resultType="com.intelliquor.cloud.shop.terminal.model.TCloudDealerInfoModel">
        SELECT
        id, open_id, type, dealer_code, dealer_name, dealer_type, dealer_invite_code, shop_invite_code, superior_id,
        dealer_area, linkman, phone, address, company_id, create_time, update_time, status, is_delete, we_chart_name,
        head_img_url, dealer_we_chart_group_img_url, shop_we_chart_group_img_url, provinces, city, district, street,
        gt_dealer_id, gt_dealer_data, dealer_area_list, account_balance, longitude, latitude, stores_img, contract_img,
        license_img, remark, level, score, union_id, account_type, virtual_amount, create_user_id, store_provinces,
        store_city, store_district, store_street, store_address, contract_code, contract_type, contract_name
        FROM
        t_cloud_dealer_info
        WHERE
        id IN (
        SELECT
        dealer_id
        FROM
        t_cloud_dealer_relation
        WHERE
        parent_id = #{dealerId})
        and contract_code = #{contractCode}
        and is_delete = 1
    </select>

    <select id="getById" resultType="com.intelliquor.cloud.shop.terminal.model.resp.CloudDealerInfoResp">
        select * from t_cloud_dealer_info where id = #{id}
    </select>

    <select id="getCloudDealerInfoByCode"
            resultType="com.intelliquor.cloud.shop.terminal.model.TCloudDealerInfoModel">
        select tcdi.* from t_terminal_shop_contract ttsc
                               left join t_cloud_dealer_info tcdi on tcdi.dealer_code = ttsc.dealer_code and tcdi.is_delete = 1
        where ttsc.member_shop_id = #{msId}
    </select>

    <select id="selectTerminalModifyShopList"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.DealerModifyShopResp">
        SELECT ms.id            AS shopId,
               ms.`name`        AS shopName,
               ms.main_code,
               ts.leader_name,
               ts.leader_phone,
               ts.address,
               tsc.dealer_code  AS parentDealerCode,
               cdii.dealer_name AS parentDealerName,
               ct.contract_name,
               tsc.contract_code,
               ct.contract_type
        FROM t_member_shop ms
                 LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
                 LEFT JOIN t_cloud_dealer_relation tcdi ON cdi.id = tcdi.dealer_id
                 LEFT JOIN t_terminal_shop ts ON ms.id = ts.member_shop_id
                 LEFT JOIN t_terminal_shop_contract tsc ON ts.id = tsc.terminal_shop_id
                 LEFT JOIN t_cloud_dealer_info cdii ON cdii.id = tcdi.parent_id
                 LEFT JOIN t_contract_type ct ON tsc.contract_type = ct.contract_type
                 LEFT JOIN (SELECT * FROM t_terminal_shop_merge WHERE `status` = 0) merge ON ts.id = merge.from_id
        WHERE tcdi.parent_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tcdi.company_id = #{shopModify.companyId}
        AND cdi.account_type = 5
        AND cdi.is_delete = 1
        <if test="shopModify.province != null and shopModify.province != ''">
            AND cdi.provinces = #{shopModify.province}
        </if>
        <if test="shopModify.city != null and shopModify.city != ''">
            AND cdi.city = #{shopModify.city}
        </if>
        <if test="shopModify.district != null and shopModify.district != ''">
            AND cdi.district = #{shopModify.district}
        </if>
        <if test="shopModify.shopName != null and shopModify.shopName != ''">
            AND ts.shop_name LIKE concat('%', #{shopModify.shopName}, '%')
        </if>
        <if test="shopModify.accountType != null">
            <if test="shopModify.accountType == 1">
                AND ms.is_member = 0
            </if>
            <if test="shopModify.accountType == 4">
                AND ms.is_member = 1
            </if>
        </if>
        AND ts.is_delete = 0
        AND ts.STATUS = 1
        AND ts.company_id = #{shopModify.companyId}
        AND cdi.id IS NOT NULL
        AND ts.merge_type IN (0, 1)
        AND merge.from_id IS NULL
        AND ms.status = 0
        ORDER BY ts.create_time DESC
    </select>

    <select id="selectDistributorsModifyShopList"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.DealerModifyShopResp">
        SELECT ms.id            AS shopId,
               ms.`name`        AS shopName,
               cdi.linkman      AS leader_name,
               cdi.phone        AS leader_phone,
               cdi.address,
               cdii.dealer_code AS parentDealerCode,
               cdii.dealer_name AS parentDealerName,
               ct.contract_name,
               cdid.contract_no AS contract_code,
               ct.contract_type,
               (select count(*)
                from t_member_shop ims
                         LEFT JOIN t_cloud_dealer_info icdi ON icdi.dealer_code = ims.dealer_code
                         LEFT JOIN t_cloud_dealer_relation itcdi ON icdi.id = itcdi.dealer_id
                where itcdi.parent_id = cdi.id  AND itcdi.company_id = #{shopModify.companyId} AND icdi.account_type = 5 AND icdi.is_delete = 1) AS terminalShopNum
        FROM t_member_shop ms
                 LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
                 LEFT JOIN t_cloud_dealer_info_detail cdid ON cdi.id = cdid.dealer_id
                 LEFT JOIN t_cloud_dealer_relation tcdi ON cdi.id = tcdi.dealer_id
                 LEFT JOIN t_cloud_dealer_info cdii ON cdii.id = tcdi.parent_id
                 LEFT JOIN t_contract_type ct ON cdid.contract_type = ct.contract_type
        WHERE tcdi.parent_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tcdi.company_id = #{shopModify.companyId}
        AND cdi.account_type = 3
        AND cdi.is_delete = 1
        <if test="shopModify.province != null and shopModify.province != ''">
            AND cdi.provinces = #{shopModify.province}
        </if>
        <if test="shopModify.city != null and shopModify.city != ''">
            AND cdi.city = #{shopModify.city}
        </if>
        <if test="shopModify.district != null and shopModify.district != ''">
            AND cdi.district = #{shopModify.district}
        </if>
        <if test="shopModify.shopName != null and shopModify.shopName != ''">
            AND ms.`name` LIKE concat('%', #{shopModify.shopName}, '%')
        </if>
        AND cdi.id IS NOT NULL
        AND ms.status = 0
        ORDER BY cdi.create_time DESC
    </select>

    <select id="selectCopartnerModifyShopList"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.DealerModifyShopResp">
        SELECT ms.id            AS shopId,
        ms.`name`        AS shopName,
        cdi.linkman      AS leader_name,
        cdi.phone        AS leader_phone,
        cdi.address,
        cdii.dealer_code AS parentDealerCode,
        cdii.dealer_name AS parentDealerName,
        ct.contract_name,
        cdi.contract_code AS contract_code,
        ct.contract_type,
        (select count(*)
        from t_member_shop ims
        LEFT JOIN t_cloud_dealer_info icdi ON icdi.dealer_code = ims.dealer_code
        LEFT JOIN t_cloud_dealer_relation itcdi ON icdi.id = itcdi.dealer_id
        where itcdi.parent_id = cdi.id  AND itcdi.company_id = #{shopModify.companyId} AND icdi.account_type = 5 AND icdi.is_delete = 1) AS terminalShopNum
        FROM t_member_shop ms
        LEFT JOIN t_cloud_dealer_info cdi ON cdi.dealer_code = ms.dealer_code
        LEFT JOIN t_cloud_dealer_relation tcdi ON cdi.id = tcdi.dealer_id
        LEFT JOIN t_cloud_dealer_info cdii ON cdii.id = tcdi.parent_id
        LEFT JOIN t_contract_type ct ON cdi.contract_type = ct.contract_type
        WHERE tcdi.parent_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tcdi.company_id = #{shopModify.companyId}
        AND cdi.account_type = 4
        AND cdi.is_delete = 1
        <if test="shopModify.province != null and shopModify.province != ''">
            AND cdi.provinces = #{shopModify.province}
        </if>
        <if test="shopModify.city != null and shopModify.city != ''">
            AND cdi.city = #{shopModify.city}
        </if>
        <if test="shopModify.district != null and shopModify.district != ''">
            AND cdi.district = #{shopModify.district}
        </if>
        <if test="shopModify.shopName != null and shopModify.shopName != ''">
            AND ms.`name` LIKE concat('%', #{shopModify.shopName}, '%')
        </if>
        AND cdi.id IS NOT NULL
        AND ms.status = 0
        ORDER BY cdi.create_time DESC
    </select>

    <select id="distributorCheckPageList" resultType="com.intelliquor.cloud.shop.terminal.model.resp.DistributorCheckResp">
        SELECT
            distributor.id as dealerId,
            distributor.dealer_code as dealerCode,
            distributor.dealer_name as dealerName,
            dealer.dealer_code as parentDealerCode,
            dealer.dealer_name as parentDealerName,
            distributor.dealer_area_list as dealerAreaList,
            distributor.linkman as linkman,
            distributor.phone as phone,
            ttam.name AS createUserName,
            tcdicd.check_type as checkType,
            tcdicb.check_status as checkStatus,
            tcdicd.create_time as createTime,
            tcdicd.check_user_name as checkUserName,
            distributor.status as status
        FROM
            t_cloud_dealer_info distributor
                LEFT JOIN
            t_cloud_dealer_info dealer
            ON
                dealer.id = distributor.superior_id
                LEFT JOIN
            (
                SELECT icd.* FROM t_cloud_dealer_info_check_detail icd WHERE icd.id IN (SELECT MAX(cd.id) FROM t_cloud_dealer_info_check_detail cd GROUP BY cd.dealer_id)
            ) tcdicd
            ON
                tcdicd.dealer_id = distributor.id
                LEFT JOIN
            t_cloud_dealer_info_check_balance tcdicb
            ON
                tcdicd.balance_id = tcdicb.id
                LEFT JOIN
            t_terminal_account_manager ttam
            ON
                ttam.id = tcdicb.create_user_id
        WHERE
            distributor.account_type = 3
          AND distributor.is_delete = 1
         <if test="distributorName != null and distributorName != ''">
            AND distributor.dealer_name like concat('%',#{distributorName},'%')
         </if>
        <if test="distributorCode != null and distributorCode != ''">
            AND distributor.dealer_code like concat('%',#{distributorCode},'%')
        </if>
        <if test="dealerName != null and dealerName != ''">
            AND dealer.dealer_name like concat('%',#{dealerName},'%')
        </if>
        <if test="dealerCode != null and dealerCode != ''">
            AND dealer.dealer_code like concat('%',#{dealerCode},'%')
        </if>
        <if test="status != null and status != ''">
            AND distributor.status = #{status}
        </if>
    </select>
</mapper>
