<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalSuperRegionConfigDao">
    <insert id="saveRegion" parameterType="com.intelliquor.cloud.shop.terminal.model.TerminalSuperRegionConfigModel"
            useGeneratedKeys="true" keyProperty="id">
        insert into t_terminal_super_region_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="province != null and province != ''">
                province,
            </if>
            <if test="city != null and city != ''">
                city,
            </if>
            <if test="district != null and district != ''">
                district,
            </if>
            <if test="scanInNum != null">
                scan_in_num,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createUserName != null and createUserName != ''">
                create_user_name,
            </if>
            <if test="createUserPhone != null and createUserPhone != ''">
                create_user_phone,
            </if>
            <if test="isComplete != null">
                is_complete,
            </if>
        </trim>
        select
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="province != null and province != ''">
                    #{province},
                </if>
                <if test="city != null and city != ''">
                    #{city},
                </if>
                <if test="district != null and district != ''">
                    #{district},
                </if>
                <if test="scanInNum != null">
                    #{scanInNum},
                </if>
                <if test="createUserId != null">
                    #{createUserId},
                </if>
                <if test="createUserName != null and createUserName != ''">
                    #{createUserName},
                </if>
                <if test="createUserPhone != null and createUserPhone != ''">
                    #{createUserPhone},
                </if>
                <if test="isComplete != null">
                    #{isComplete},
                </if>
            </trim>
        FROM DUAL
        WHERE NOT EXISTS(SELECT city FROM t_terminal_super_region_config
        <where>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="district != null and district != ''">
                and district = #{district}
            </if>
        </where>
        )
    </insert>
</mapper>
