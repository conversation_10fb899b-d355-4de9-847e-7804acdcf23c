<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.VisitTerminalConfigDao">

    <resultMap type="com.intelliquor.cloud.shop.terminal.model.VisitTerminalConfigModel" id="VisitTerminalConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="levelId" column="level_id" jdbcType="INTEGER"/>
        <result property="cycleCode" column="cycle_code" jdbcType="VARCHAR"/>
        <result property="checkNum" column="check_num" jdbcType="INTEGER"/>
        <result property="isStatus" column="is_status" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="delTime" column="del_time" jdbcType="TIMESTAMP"/>
        <result property="delUser" column="del_user" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectListPage" resultType="com.intelliquor.cloud.shop.terminal.model.resp.VisitTerminalConfigResp">
        select
            t1.id,
            t1.level_id as levelId,
            t2.level_name as levelName,
            t1.cycle_code as cycleCode,
            t3.code_name as cycleName,
            t1.check_num as checkNum,
            t1.is_status as isStatus
        from t_visit_terminal_config t1 left join  t_terminal_shop_level t2 on t2.id = t1.level_id
        left join sys_code_base t3 on t3.code = t1.cycle_code and t3.state='A' AND t3.code_type='CYCLE_CODE'
        where t1.is_delete = 0
    </select>

    <select id="getWeekConfig" resultMap="VisitTerminalConfigMap">
        select id,
               level_id,
               cycle_code,
               check_num,
               is_status,
               is_delete,
               create_time,
               create_user,
               create_user_name,
               update_time,
               update_user,
               del_time,
               del_user,
               remark
        from t_visit_terminal_config
        where is_delete = 0
          and level_id = 0
          and cycle_code = 0
          and is_status = 1
        limit 1
    </select>
</mapper>
