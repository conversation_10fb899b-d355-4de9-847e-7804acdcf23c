<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.TerminalShopInfoScheduleDao">

    <select id="selectTerminalShopInfoScheduleById"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopInfoScheduleResp">
        select
            ts.id,
            ts.terminal_shop_id,
            ts.shop_name,
            ts.leader_name,
            ts.leader_phone,
            ts.tag,
            ts.head_img,
            ts.remark,
            ts.province,
            ts.city,
            ts.district,
            ts.address,
            ts.longitude,
            ts.latitude,
            ts.is_image,
            ts.image_head_picture,
            ts.keeper_name,
            ts.keeper_phone,
            ts.shop_area,
            ts.whether_license,
            ts.license_img,
            ts.license_code,
            ts.status,
            ts.is_delete,
            ts.create_time,
            ts.create_user,
            ts.update_time,
            ts.company_id,
            ts.member_shop_id,
            ts.shop_type,
            ts.enterprise_name,
            ts.food_business_license,
            ts.whether_proprietary_trading,
            ts.receiving_warehouse_province,
            ts.receiving_warehouse_city,
            ts.receiving_warehouse_district,
            ts.receiving_warehouse_address,
            ts.level_code,
            ts.contact_name,
            ts.contact_level,
            ts.contact_phone,
            ts.receiving_payment_type,
            ts.receiving_payment_name,
            ts.receiving_payment_account,
            ts.receiving_payment_bank,
            ts.receiving_payment_account_picture,
            ts.audit_user_id,
            ts.is_high_member,
            ts.audit_result,
            ts.company_person,
            ts.company_name,
            ts.license_message
        from  t_terminal_shop_info_schedule ts   where   ts.id = #{id}  and  ts.is_delete = 0
    </select>

    <select id="getTerminalShopScheduleIdByTerminalShopId" resultType="java.lang.Integer">
        select id from t_terminal_shop_info_schedule where terminal_shop_id = #{terminalShopId}
    </select>

    <select id="selectTerminalShopInfoScheduleByTerminalShopId"
            resultType="com.intelliquor.cloud.shop.terminal.model.resp.TerminalShopInfoScheduleResp">
        select
            ts.id,
            ts.terminal_shop_id,
            ts.shop_name,
            ts.leader_name,
            ts.leader_phone,
            ts.tag,
            ts.head_img,
            ts.remark,
            ts.province,
            ts.city,
            ts.district,
            ts.address,
            ts.longitude,
            ts.latitude,
            ts.is_image,
            ts.image_head_picture,
            ts.keeper_name,
            ts.keeper_phone,
            ts.shop_area,
            ts.whether_license,
            ts.license_img,
            ts.license_code,
            ts.status,
            ts.is_delete,
            ts.create_time,
            ts.create_user,
            ts.update_time,
            ts.company_id,
            ts.member_shop_id,
            ts.shop_type,
            ts.enterprise_name,
            ts.food_business_license,
            ts.whether_proprietary_trading,
            ts.receiving_warehouse_province,
            ts.receiving_warehouse_city,
            ts.receiving_warehouse_district,
            ts.receiving_warehouse_address,
            ts.level_code,
            ts.contact_name,
            ts.contact_level,
            ts.contact_phone,
            ts.receiving_payment_type,
            ts.receiving_payment_name,
            ts.receiving_payment_account,
            ts.receiving_payment_bank,
            ts.receiving_payment_account_picture,
            ts.audit_user_id,
            ts.is_high_member,
            ts.audit_result
        from  t_terminal_shop_info_schedule ts   where   ts.terminal_shop_id = #{terminalShopId}  and  ts.is_delete = 0
    </select>

    <update id="batchUpdateParentDistributorId">
        update t_terminal_shop_info_schedule
        set distributor_id = #{distributorId},remark= #{remark}
        where deputy_code in
        <foreach collection="deputyCodeList" separator="," open="(" close=")" item="code">
            #{code}
        </foreach>
    </update>

    <update id="batchUpdateParentCopartnerId">
        update t_terminal_shop_info_schedule
        set copartner_id = #{copartnerId},remark= #{remark}
        where deputy_code in
        <foreach collection="deputyCodeList" separator="," open="(" close=")" item="code">
            #{code}
        </foreach>
    </update>


    <update id="updateRelation">
        update t_cloud_dealer_relation tcdr set tcdr.parent_id =#{parentId}
        where dealer_id in(SELECT tcdi.id FROM t_cloud_dealer_info tcdi where tcdi.dealer_code in
        <foreach collection="dealerCodeList" separator="," open="(" close=")" item="code">
            #{code}
        </foreach>
        )
    </update>
    <update id="updateDistributorIdById">
        update t_terminal_shop_info_schedule  set distributor_id = #{distributorId} where id= #{id}
    </update>
</mapper>
