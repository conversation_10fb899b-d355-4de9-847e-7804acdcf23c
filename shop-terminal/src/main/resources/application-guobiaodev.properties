#å¬å±æ¨¡åéç½®
spring.profiles.include=gbstest

server.port=8886
server.servlet.context-path=/guotai-terminal

#æ°æ®åºéç½®
spring.datasource.url=**************************************************************************************************************************************************************************************************************************************
spring.datasource.username=cloud_shop
spring.datasource.password=cloud_shop
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver

drink.datasource.url=******************************************************************************************************************************************************
drink.datasource.username=drink-advisory
drink.datasource.password=JiYouAi@)21
drink.datasource.driver=com.mysql.cj.jdbc.Driver

club.datasource.url=********************************************************************************************************************************************
club.datasource.username=drink_club
club.datasource.password=drink_club
club.datasource.driver=com.mysql.jdbc.Driver

#druidè¿æ¥æ± 
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

#è¿æ¥æ± éç½®
spring.datasource.initialSize=50
spring.datasource.minIdle=5
spring.datasource.maxActive=1000
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.filters=stat,slf4j
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.mvc.throw-exception-if-no-handler-found=true
spring.resources.add-mappings=false
logging.level.com.intelliquor.cloud.shop=info
logging.path=/home/<USER>/guotai-terminal

#pagehelper
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql
pagehelper.page-size-zero=true
spring.mvc.favicon.enabled=false
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=1000MB
swagger.title=\uFFFD\u01B5\uFFFD
swagger.version=0.0.1
swagger.contact.name=\uFFFD\u01B5\uFFFD
swagger.base-package=com.intelliquor.cloud.shop.terminal
swagger.base-path=/**

# å¼åå¹³å°å¾®ä¿¡éç½®
wechat.open.componentAppId=wxae44190677cf893c
wechat.open.componentSecret=34fe77ee49e3a9c022968b0641751c2b
wechat.open.componentToken=intelliquorcom
wechat.open.componentAesKey=abcdefghijkluvwxmnopqrstyz0123456789ABCDEFG
wechat.redis.host=************
wechat.redis.password=guotai2020
wechat.redirect.url=http://testgbsszhqd.guotaijiu.com/cloudshop/base-setting/wechat-mini
wechat.referer.url=testgbsszhqdapi.guotaijiu.com/guotai-shop

#å°ç¨åº
APP_ID_NEW=wxc496d270e7c090ba
APP_SECRET_NEW=6c28f7fb942295f6dc6d4bbef8ec4fcc
SESSION_KEY_URL=https://api.weixin.qq.com/sns/jscode2session

#oss
aliyunoss.folder=shopTerminal
aliyunoss.folder.order=cloud-shop-orderExcel
aliyunoss.key=LTAI5tDCGdkQ3TZbH183Knpu
aliyunoss.secret=******************************
aliyunoss.bucket=guofenclubnew
aliyunoss.domain=oss-cn-beijing.aliyuncs.com
local.order.folder=orderFolder
# osséç½®ä¿¡æ¯
aliyun.oss.accessId=LTAI5tDCGdkQ3TZbH183Knpu
aliyun.oss.accessKey=******************************
aliyun.oss.endpoint=oss-cn-beijing.aliyuncs.com
aliyun.oss.bucket=guofenclubnew
aliyun.oss.host=guofenclubnew.oss-cn-beijing.aliyuncs.com
aliyun.oss.expireTime=30
aliyun.oss.min=0
aliyun.oss.max=10485760
aliyun.oss.dir=shopTerminal
aliyun.oss.calbackUrl=http://testszhqdapi.guotaijiu.com/guotai-terminal/oss/callback


#\uFFFD\u00BC\uFFFD\uFFFD\uFFFD\u05B7
data.center.url=http://manage-api.baijiuyun.com/gt/data-api/event/w/add
data.center.token=1121711460351737946
data.center.modelId=216
data.center.userId=14
data.center.sourceSystem=jz
data.center.baseurl=http://manage-api.baijiuyun.com/gt
data.center.businessId=14

# Redis
spring.redis.database=0
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=guotai2020
spring.redis.timeout=10000

#jest setting
#spring.elasticsearch.jest.uris=http://************:9200,http://************:9200,http://************:9200
spring.elasticsearch.jest.uris=http://es-cn-mp9194thu000fuusi.elasticsearch.aliyuncs.com:9200
spring.elasticsearch.jest.read-timeout=10s
spring.elasticsearch.jest.connection-timeout=10s
spring.elasticsearch.jest.multi-threaded=true
spring.elasticsearch.jest.username=elastic
spring.elasticsearch.jest.password=JiYouAi@)19

#kafka
spring.kafka.bootstrap-servers=************:9092
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.consumer.group-id=cloud-shop-prod
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.listener.ack-mode=MANUAL_IMMEDIATE
kafka.system.order.topic=systemOrderTopic

#æºçæ°æ®åºéç½®
dmp.datasource.url=*************************************************************************************************************************
dmp.datasource.username=market
dmp.datasource.password=market_2019_802
dmp.datasource.driver=com.mysql.jdbc.Driver



#ä¼å£«éæéé
finance.channel=1003
finance.queryInformation=http://*************:19080/mbx/rest/resource/fy/queryInformation/0/v1
finance.queryYcInformation=http://*************:19080/mbx/rest/resource/yc/listUserInfo/0/v1
finance.getVirtualCards=http://*************:19080/mbx/rest/resource/virtualCard/getVirtualCards/0/v1
finance.payurl=http://*************:7000/suning/order/pay
finance.listVirtualCardUrl = http://*************:19080/mbx/rest/resource/yc/listVirtualCard/0/v1
finance.payCallBackUrl=http://cloudshopapi.baijiuyun.com/wechat/customerPaymentRecord/payCallBack
finance.youshiNotifyOrderUrl = https://mbx.1532mall.net/mbx/rest/resource/retailer/consume/0/v1
finance.youshiInvoiceAuditUrl = https://mbx.1532mall.net/mbx/rest/resource/dealer/consume/0/v1

#é®ç®±éç½®
spring.mail.host = smtp.exmail.qq.com
spring.mail.username = <EMAIL>
spring.mail.password = d4ypUCvHtdJe6bWb
spring.mail.properties.mail.smtp.port = 465
spring.mail.sender = <EMAIL>
spring.mail.receivers = <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
spring.mail.properties.mail.smtp.starttls.enable = true
spring.mail.properties.mail.smtp.starttls.required = true
spring.mail.properties.mail.smtp.ssl.enable = true
spring.mail.default-encoding = utf-8

encryptJWTKey: U0JBUElKV1RkV2FuZzkyNjQ1NA==
# å¤©è¾°MD5Key
notice.tianchen.secret.key=E96BA47AE577A25BE8724C868016E1FE
# éç¥å¤©è¾°ä¿®æ¹ç 
notice.tianchen.url = http://***************:9103/t/api/warehouse/updateDealer

#å¤è´æ¥ä½¿ç¨codeè¯·æ±
code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfoByType

#æ¥è¯¢ç®±ä¸é¢çç¶ç 
box_code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfoMap/

#æ¥è¯¢çº¢æç®±ç ä¸çç¶ç æ°æ®
hx_box_code.url=http://manage-api.baijiuyun.com/gt/data-api/code/r/codeInfoMapV2

#é»è®¤å½å°companyId
gt_company_id=50
#éæ²companyId
jinsha_company_id=52

#å½å°åæ­¥éç½®
gtsyn.gtDealerUrl=http://xzt.jscssui.com:9527/jeecg-boot/web/api/analyze/customer/pageList?pageNo={pageNo}&pageSize={pageSize}
gtsyn.gtEsSynUrl=http://api.dm.intelliquor.com/data/data-api/v2/model/w/save
gtsyn.gtEsSynUserId=219
gtsyn.gtEsSynModelId=697
gtsyn.gtEsSynToken=1343736924237463614

#è°ç¨å½å°çè´¦å·ä¿¡æ¯
gtsyn.accessKey=GZDCs8ko
gtsyn.accessSecret=i5qqr7OyTXwsB83CINtDc7xUfw6H67pv

#æ ¹æ®ææºå·è·åç»éå/åéåä¿¡æ¯
#gtsyn.checkPhoneUrl=https://gtsytest.tasly.com/enterprisewx/auth/zytrace/queryDistributionByConnectPhone
gtsyn.checkPhoneUrl=https://gtsytest.tasly.com/enterprisewx/auth/zytrace/queryDistributionByConnectPhone
#è·åtoken
gtsyn.accessTokenUrl=https://gtsytest.tasly.com/enterprisewx/auth/zytrace/accessToken
# å½å°è®¢åå°å
gtsyn.synOrderUrl = https://gtsytest.tasly.com/enterprisewx/auth/zytrace/syncOrderList


#æºç è·åä¸å¡åºå
zy.api.getBusinessAreaUrl=http://************:7002/v2/zy/company/getBusinessArea
#æ¥è¯¢ååæ¥å£
product_bottle_ratio = http://manage-api.baijiuyun.com/product-api/findOneByCode

#å¥åº·é¥®éä¿±ä¹é¨
drink_club_manage_api.url=http://10.32.176.20:7111/drink_club_manage_api
#å¥åº·é¥®éé¡¾é®
drink_advisory_mgr_api.url=http://10.32.176.20:7110/drink_advisory_mgr_api
drink_advisory_mgr_api_test.url=http://api.baijiuyun.com/drink_advisory_mgr_api_dev
#å¥åº·é¥®éä¿±ä¹é¨ è®¢åä¿¡æ¯
gt-drink-club-manage-api-order-info-url=${drink_club_manage_api.url}/data-api/drinkClubData/getOrderInfo
#å¥åº·é¥®éä¿±ä¹é¨ ååä¸ä¸æ¶åè¡¨
gt-drink-advisory-mgr-api-goods-list-url=${drink_advisory_mgr_api.url}/data-api/leaderDealer/goods/getListByPage
#å¥åº·é¥®éä¿±ä¹é¨ é¡¾é®åäº«è®¢åçæå¥å±
gt-drink-advisory-save-balance-url = ${drink_advisory_mgr_api.url}/data-api/shopMeltBalanceRecord/saveBalanceRecord
#å¥åº·é¥®éä¿±ä¹é¨ å é¤è®¢åå¥å±
gt-drink-advisory-delete-balance-url = ${drink_advisory_mgr_api.url}/data-api/shopMeltBalanceRecord/delBalanceRecord
#å¥åº·é¥®éä¿±ä¹é¨ æ¥è¯¢ååè¯¦æ
gt-drink-advisory-goods-detail-url = ${drink_advisory_mgr_api.url}/system/wineClub/getGoodsInfo

#ç¨äºå°ç¨åºåçï¼æ¹ææºå·å¯ä»¥å¡«åä»»æéªè¯ç 
special_phone = 17663702099

#å½ç²æ°æ®åº
drinkClub.datasource.url=*********************************************************************************************************************
drinkClub.datasource.username=drink_club
drinkClub.datasource.password=drink_club
drinkClub.datasource.driver=com.mysql.jdbc.Driver

#ççå°companyId
lyt_company_id=83

#è·åçæ­¦ä¿¡æ¯
#xuanwu.token.url = http://59.80.40.170:7000/api/auth/openlogin
#å½æ æ¾åç¬éç½®ï¼è¦çcommonä¸­çéç½®
xuanwu.accountinfocode = 1733052964442083328
xuanwu.opentypecode = guobiaoshi-data-server
xuanwu.opentypesecret = gbs8888
xuanwu.clienttypecode = 1
#xuanwu.sendFxsApplyUrl = http://59.80.40.170:7000/api/teapi/dy-biz/1404623815841026146/1406218921853456476
#xuanwu.getContractListFromXwUrl = http://59.80.40.170:7000/api/teapi/dy-biz/1404623815841026146/1412217234285269071
xuanwu.sendFxsApplyUrl = http://**************:7000/api/teapi/dy-biz/1404623815841026146/1406218921853456476
xuanwu.sendGwApplyUrl = http://**************:7000/api/teapi/dy-biz/1404623815841026146/1495952773764747356
xuanwu.getContractListFromXwUrl = http://**************:7000/api/teapi/dy-biz/1404623815841026146/1412217234285269071
xuanwu.sendSpecialUrl =http://**************:7000/api/teapi/dy-biz/1404623815841026146/1495952773764747363
xuanwu.getDealerListByPhoneUrl = http://**************:7000/api/teapi/dy-biz/1508478852203155529/1542767614978297946

#ç¨äºå°ç¨åºåçï¼æ¹ææºå·å¯ä»¥å¡«åä»»æéªè¯ç 
special_code = 222222

#å½å°ä¸å¡åºåid
gt_area_id = 178

#è¿éçwxAppIdåagentIdæ¯æº¯æºåç«¯ç¨ç
#https://www.yuque.com/docs/share/1eae9f92-eca2-4e2f-88a2-8a0e83c8983b å¯ç lg0c
guotai.suyuan.wxAppId=ww923d07042ff2a178
guotai.suyuan.agentId=1000002


#ååè·åè§æ ¼
goods.queryByCode.url = http://manage-api.baijiuyun.com/product-api/findOneByCode

#æºç è·åä¸å¡åºå
zy.api.businessAreaListUrl=http://api.baijiuyun.com/v2/businessArea/list4Leader


instudio.client_id = Wr5duXakFl64SPxUlLT5Y1y35PHIzXsO

instudio.secret_id = HyOrycFgbN02f4Y3WHVpDlTnxFgFllD5

instudio.customer.url = https://gtdjktyzxtest.insuite.cn/studio/api_special/insuite/mdm_customer/create

instudio.order.url = https://gtdjktyzxtest.insuite.cn/studio/api_special/insuite/sal_order/create

#ç»ç«¯å¯ç å å¯å¯é¥
terminal_sign_key=**********

#è¯å«è¥ä¸æ§ç§çæ¥å£
check_license_host=https://bizlicense.market.alicloudapi.com
check_license_path=/rest/160601/ocr/ocr_business_license.json
#è¥ä¸æ§ç§code
check_license_appCode=db247fd6c51347439b09b183a78ffc59

check_food_license_host=https://spxk1.market.alicloudapi.com
check_food_license_path=/ocrservice/foodBlicense

#linuxä¸ä¼ æä»¶å°å
uploadImageLinux=/var/www/img/guotaiimg/images
uploadVideoLinux=/var/www/img/guotaiimg/videos
#linuxåæ¾æä»¶å°å
showImageLinux=/guotaiimg/images
showVideoLinux=/guotaiimg/videos
#windowsä¸ä¼ æä»¶å°å
uploadImageWindows=/C://images
uploadVideoWindows=/C://videos
#windowsåæ¾æä»¶å°å
showImageWindows=/C://images
showVideoWindows=/C://videos

#ä»æº¯æºè·åç ä¿¡æ¯
xuanwu.getCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/
#ä»æº¯æºè·åè¿è´§åä¸çææç ä¿¡æ¯
xuanwu.getOrderCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/receive/
#æåæ¶è´§åæ­¥è³æº¯æº
xuanwu.sendOrderCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/confirm-receive
#æç æ¶è´§åæ­¥è³æº¯æº
#xuanwu.sendCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/confirm-delivery
xuanwu.sendCodeInfo.url = https://gtsytest.tasly.com/enterprisewx/zy/facade/scan-receive

##çæåè´§å¯¹è±¡æèæ¿æ´»ç»ç«¯çæ¶ååæ­¥ä¸­å°çæ¥å£
xuanwu.sendTerminalData.url = http://**************:7000/api/teapi/dy-biz/1508478852203155529/1559909743152205923
#å®´å¸­å¤æ ¸æåååæ­¥ä¸­å°
xuanwu.sendBanquetData.url = http://**************:7000/api/teapi/dy-biz/1551809561512513631/1566985019447185494
#æ¥è¯¢å¤æ ¸å®¡æ¹æµç¨æè§ æµè¯
xuanwu.selectTerminalBanquetFail.url = http://oa.guotaijiu.com:8099/api/km-review/kmReviewRestService/queryApproveProcess
#æ¨éå¢è´­å®¢æ·
xuanwu.sendTogtherClient.url = http://**************:7000/api/teapi/dy-biz/1404623815841026146/1406218921853456476


##NFCæ«ç åæ­¥-ä¸­å°
xuanwu.sendNfcInfo.url = http://**************:7000/api/teapi/dy-biz/1403256332726964315/1552854596869820515
##NFCæ«ç åæ­¥æ¸é¤-ä¸­å°
xuanwu.sendNfcInfoDel.url = http://**************:7000/api/teapi/dy-biz/1403256332726964315/1562738966199930977

#éåè®°å½åæ­¥
xuanwu.terminalDisplay.url =http://**************:7000/api/teapi/dy-biz/1551809561512513631/1574192642818969699

#çæåè´§å¯¹è±¡æèæ¿æ´»ç»ç«¯çæ¶ååæ­¥ä¸­å°çæ¥å£
#xuanwu.sendTerminalData.url = http://59.80.40.171:7000/api/teapi/dy-biz/1508478852203155529/1559909743152205923
#NFCæ«ç åæ­¥-ä¸­å°
#xuanwu.sendNfcInfo.url = http://59.80.40.171:7000/api/teapi/dy-biz/1403256332726964315/1552854596869820515
#NFCæ«ç åæ­¥æ¸é¤-ä¸­å°
#xuanwu.sendNfcInfoDel.url = http://59.80.40.171:7000/api/teapi/dy-biz/1403256332726964315/1562738966199930977

#éç½®ä¸ä¸ªåè´¦æ·ç»è¾è®¯ç¨
tencent.phone = 17663702099

#é¦åæ´»å¨æ¥æ å¹´-æ-æ¥ æ¶:å:ç§
first_order_date = 2022-10-19 00:00:00


#æè®¿è®°å½åæ­¥ç»ä¸­å°
terminal_visit_url = http://**************:7000/api/teapi/dy-biz/1399909626723569761/1569519693713248355

#20231227ç¼ç æ²éä¼è®®è¦ç¹ï¼
#1ãå½æ æ¾ç»ä¸ä½¿ç¨3å¼å¤´9ä½æ°çç¼ç ã
#2ã3111å¼å¤´çç¼ç ç±çæ­¦ç³»ç»ä½¿ç¨ï¼ä¾å¦ï¼311100000  ãä¸­å°ä½¿ç¨ã
#3ã32å¼å¤´çç¼ç ç±æºçç³»ç»ä½¿ç¨ï¼å·ä½çç±»åå¦åéåãç»ç«¯ç­ç¨å¦å¤çå­æ®µåºåãä¾å¦ï¼320000000   ãæºçä½¿ç¨ã
#å½å°ç»ç«¯çæç¼ç åç¼
gt.terminal.code = 321
#å½å°å¢è´­å®¢æ·çæç¼ç åç¼
gt.team.shopping.code = 322
#å½å°åéåçæç¼ç åç¼
gt.distributor.code = 323
#å½å°åä¼äººçæç¼ç åç¼
gt.partnership.code = 324
#å½æ æ¾çæ è¯
gt.type = gbs

send.zt.rewardrecordurl = http://**************:7000/api/teapi/dy-biz/1404623815841026146/1642011261766078560

#å½å°aiè¯å«ç»å½è´¦æ·
gt.ai.username = 10145
#å½å°aiè¯å«ç»å½å¯ç 
gt.ai.password = knwV7aXrHFbhaOVc
#å½å°aiè¯å«ç»å½æ¥å£
gt.ai.login = https://hwaiapi.xtion.net/api/ai/auth/login
#å½å°aiæ¥è¯¢æ¯å¦ç¿»æçæ¥å£
gt.ai.re.shoot = https://hwaiapi.xtion.net/api/ai/imagequality/fakephoto
#å½å°aiè¯å«æ¥è¯¢éåäº§åä¿¡æ¯æ¥å£
gt.ai.select.display.goods = https://hwaiapi.xtion.net/api/ai/shelf/syncdetect
send.oa.orderurl = http://oa.guotaijiu.com:8099/api/km-review/kmReviewRestService/addReview1
send.oa.orderapprovalurl = http://oa.guotaijiu.com:8099/api/km-review/kmReviewRestService/queryApproveProcess

xuanwu.sendYbcjTerminalData.url = http://**************:7000/api/teapi/dy-biz/1508478852203155529/1656205591451930723

guotai.promotionManagerListUrl=http://**************:7000/api/teapi/dy-biz/1404623815841026146/1670959693071257699

#rocketmq.name-server=10.32.175.161:9876
#rocketmq.producer.group=ZY-GUOTAI-TERMINAL-GROUP-TO1
#rocketmq.producer.topic.display=ZY-GUOTAI-TERMINAL-PROTOCOL-DISPLAY-REWARD-CACL-T01
#rocketmq.producer.topic.package=ZY-GUOTAI-TERMINAL-PROTOCOL-PACKAGE-REWARD-CACL-T01
#rocketmq.producer.topic.openBottleIncentiveBonus=ZY-GUOTAI-TERMINAL-OPEN-BOTTLE-INCENTIVE-BONUS-T01
#rocketmq.producer.topic.openBottleIncentiveReward=ZY-GUOTAI-TERMINAL-OPEN-BOTTLE-INCENTIVE-REWARD-T01
#rocketmq.producer.topic.banquet=ZY-GUOTAI-TERMINAL-BANQUET-REWARD-T01
#rocketmq.consume.group.display=ZY-GUOTAI-TERMINAL-GROUP-DISPLAY-TO1
#rocketmq.consume.group.package=ZY-GUOTAI-TERMINAL-GROUP-PACKAGE-TO1
#rocketmq.consume.group.openBottleIncentiveBonus=ZY-GUOTAI-TERMINAL-GROUP-OPEN-BOTTLE-INCENTIVE-BONUS-T01
#rocketmq.consume.group.openBottleIncentiveReward=ZY-GUOTAI-TERMINAL-GROUP-OPEN-BOTTLE-INCENTIVE-REWARD-T01
#rocketmq.consume.group.banquet=ZY-GUOTAI-TERMINAL-GROUP-BANQUET-REWARD-T01
#rocketmq.consume.topic.display=ZY-GUOTAI-TERMINAL-PROTOCOL-DISPLAY-REWARD-CACL-T01
#rocketmq.consume.topic.package=ZY-GUOTAI-TERMINAL-PROTOCOL-PACKAGE-REWARD-CACL-T01
#rocketmq.consume.topic.openBottleIncentiveBonus=ZY-GUOTAI-TERMINAL-OPEN-BOTTLE-INCENTIVE-BONUS-T01
#rocketmq.consume.topic.openBottleIncentiveReward=ZY-GUOTAI-TERMINAL-OPEN-BOTTLE-INCENTIVE-REWARD-T01
#rocketmq.consume.topic.banquet=ZY-GUOTAI-TERMINAL-BANQUET-REWARD-T01
#
#rocketmq.acountProducer.group= REWARD-ACCOUNT-SUBMITGROUP-TEST
#rocketmq.acountProducer.topic=REWARD-ACCOUNT-SUBMIT-TEST
#rocketmq.acountConsume.group=GTZD-REWARD-ACCOUNT-CALLBACKGROUP-TEST
#rocketmq.acountConsume.topic=GTZD-REWARD-ACCOUNT-CALLBACK-TEST

xuanwu.account.sourceSystem = GTZD
xuanwu.account.banquetSourceSystem = yx
xuanwu.account.packageRelCode = YSPZ-JF-002
xuanwu.account.packageType = 2
xuanwu.account.displayRelCode = YSPZ-DSFYJ-001
xuanwu.account.displayType = 18

xuanwu.updateImage = http://**************:7000/api/teapi/dy-biz/1631543050440413205/1780792831770759267

xuanwu.account.purchaseAchievedRelCode = YSPZ-DSFYJ-019
xuanwu.account.purchaseAchievedType = 19
xuanwu.account.terminalPurchaseRewardType=21
xuanwu.account.terminalPurchaseRewardRelCode=YSPZ-DSFYJ-022
xuanwu.account.incentiveGiftRelCode = YSPZ-DSFYJ-024
xuanwu.account.incentiveGiftType = 22
xuanwu.account.incentivePointsRelCode = YSPZ-JF-038
xuanwu.account.incentivePointsType = 26

oaAccountID = admin
oaAccountPassword = 123456

