package com.intelliquor.cloud.shop.terminal;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.dao.TerminalRewardRecordDao;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
import com.intelliquor.cloud.shop.common.model.TerminalCodeOutTimeModel;
import com.intelliquor.cloud.shop.common.model.TerminalRewardRecordModel;
import com.intelliquor.cloud.shop.common.model.req.OrderReq;
import com.intelliquor.cloud.shop.common.model.resp.CloudDealerOutBalanceResp;
import com.intelliquor.cloud.shop.common.service.ICloudDealerOutBalanceService;
import com.intelliquor.cloud.shop.common.service.ITerminalCodeOutTimeService;
import com.intelliquor.cloud.shop.common.service.ScanCommonService;
import com.intelliquor.cloud.shop.terminal.dao.MemberShopDao;
import com.intelliquor.cloud.shop.terminal.dao.TerminalAccountManagerDao;
import com.intelliquor.cloud.shop.terminal.model.TCloudDealerInfoModel;
import com.intelliquor.cloud.shop.terminal.model.req.DisplayResultReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalAccountManagerReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalVisitRecordReq;
import com.intelliquor.cloud.shop.terminal.model.resp.CloudDealerInfoResp;
import com.intelliquor.cloud.shop.terminal.model.resp.OrderNodeResp;
import com.intelliquor.cloud.shop.terminal.service.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestClass {
    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ITerminalCodeOutTimeService terminalCodeOutTimeService;
    @Autowired
    private ScanCommonService scanCommonService;

    @Autowired
    private CloudDealerInfoService cloudDealerInfoService;

    @Resource
    private DistributorService distributorService;

    @Resource
    private CallXWService callXWService;

    @Resource
    private ITerminalVisitTotalService terminalVisitTotalService;


    /*@Resource
    private IntegralSyncService integralSyncService;*/

    @Autowired
    private TerminalMeetingClockInService terminalMeetingClockInService;

    @Autowired
    private TerminalAccountManagerDao terminalAccountManagerDao;



    @Test
    public void td3() {
        //System.out.println(RandomUtils.getTerminalCode());

    }

    @Test
    public void td2() {
        // 分页处理
        PageHelper.startPage(1, 10);
        TCloudDealerInfoModel cloudDealerInfoModel = new TCloudDealerInfoModel();
        cloudDealerInfoModel.setAccountType(3);
        // cloudDealerInfoModel.setDealerName("小吴");
        // 查询
        List<CloudDealerInfoResp> selectList = cloudDealerInfoService.getList(cloudDealerInfoModel);

        //分页
        PageInfo<CloudDealerInfoResp> pageInfo = new PageInfo<>(selectList);

    }

    @Autowired
    private TerminalRewardRecordDao terminalRewardRecordDao;

    @Autowired
    private MemberShopDao memberShopDao;

    @Test
    public void td() {
        //setIfAbsent 就是 setnx
        Boolean absent = redisTemplate.opsForValue().setIfAbsent("name", "xiaoming");
        System.out.println("absent = " + absent);

        String name = (String) redisTemplate.opsForValue().get("name");
        System.out.println("name = " + name);

        stringRedisTemplate.opsForValue().set("strname", "strname");
        System.out.println(stringRedisTemplate.opsForValue().get("strname"));

    }

    /**
     * 营业执照识别示例
     */
    @Test
    public void testBizlicense() {
        // 这些配置要放到配置文件中
        String host = "https://bizlicense.market.alicloudapi.com";
        String path = "/rest/160601/ocr/ocr_business_license.json";
        // 这个也要放到配置文件中，这个是测试的凭证，发版时要换成客户的
        String appcode = "228e6f8d71004f63840f6f51b8a0a57f";


        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 接口调用凭证
            headers.set("Authorization", "APPCODE " + appcode);


            JSONObject req = new JSONObject();
            // 这里是要识别的图片地址
            req.put("image", "https://dealer-saas.oss-cn-beijing.aliyuncs.com/cloud-shop/1660118128736.jpg");
            HttpEntity<JSONObject> entity = new HttpEntity<>(req, headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(host + path, HttpMethod.POST, entity, String.class);
            System.out.println(responseEntity.getBody());
            //获取response的body
            //System.out.println(EntityUtils.toString(response.getEntity()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void test1() {

    }

    @Test
    public void test2() {
        TerminalCodeOutTimeModel model = new TerminalCodeOutTimeModel();
        List<TerminalCodeOutTimeModel> list = terminalCodeOutTimeService.selectPageList(model, 1, 10);
        System.out.println(list.size());
    }

    @Test
    public void test4() {
        List<TerminalRewardRecordModel> recordModelList = terminalRewardRecordDao.selectListByRemark();
        Map<Integer, List<TerminalRewardRecordModel>> map = recordModelList.stream().collect(Collectors.groupingBy(TerminalRewardRecordModel::getShopId));
        String st = "";
        for (Integer shopId : map.keySet()) {
            st = st + "," + shopId;
            TerminalRewardRecordModel terminalRewardRecordModel = terminalRewardRecordDao.selectTerminalRewardRecordModelByShopId(shopId);
            BigDecimal afterChangeVirtualAmount = terminalRewardRecordModel.getAfterChangeVirtualAmount();
            BigDecimal score = BigDecimal.ZERO;
            List<TerminalRewardRecordModel> rewardRecordModelList = map.get(shopId);
            log.info("终端ID为：{}开始计算积分", shopId);
            for (TerminalRewardRecordModel rewardRecordModel : rewardRecordModelList) {
                score = score.add(rewardRecordModel.getAmount());
               //  terminalRewardRecordDao.delete(rewardRecordModel.getId());
            }
            memberShopDao.subtractVirtualAmount(shopId, score.negate());
            log.info("终端ID为：{}结束计算积分,共{}条记录，加{}积分", shopId, rewardRecordModelList.size(), score.negate());
        }
        log.info(st);
    }

    @Test
    public void test3() {
        List<TerminalRewardRecordModel> recordModelList = terminalRewardRecordDao.selectListByIds();
        Map<Integer, List<TerminalRewardRecordModel>> map = new HashMap<>();

        recordModelList.forEach(e -> {
            Integer shopId = e.getShopId();
            if (map.containsKey(shopId)) {
                List<TerminalRewardRecordModel> list = map.get(shopId);
                e.setRemark("扣除合同外产品奖励-" + e.getId());
                e.setSysState(99);
                e.setIsDelete(0);
                e.setSysDate(null);
                e.setSysMsg("");
                e.setAmount(e.getAmount().negate());
                list.add(e);
                map.put(shopId, list);
            } else {
                List<TerminalRewardRecordModel> list = new ArrayList<>();
                e.setRemark("扣除合同外产品奖励-" + e.getId());
                e.setSysState(99);
                e.setIsDelete(0);
                e.setSysDate(null);
                e.setSysMsg("");
                e.setAmount(e.getAmount().negate());
                list.add(e);
                map.put(shopId, list);
            }
        });
        //for (Integer shopId : map.keySet()) {
        TerminalRewardRecordModel terminalRewardRecordModel = terminalRewardRecordDao.selectTerminalRewardRecordModelByShopId(134535);
        BigDecimal afterChangeVirtualAmount = terminalRewardRecordModel.getAfterChangeVirtualAmount();
        BigDecimal score = BigDecimal.ZERO;
        List<TerminalRewardRecordModel> rewardRecordModelList = map.get(134535);
        log.info("终端ID为：{}开始计算积分", 134535);

        for (int i = 0; i < rewardRecordModelList.size(); i++) {
            TerminalRewardRecordModel rewardRecordModel = rewardRecordModelList.get(i);
            score = score.add(rewardRecordModel.getAmount());
            rewardRecordModel.setBeforeChangeVirtualAmount(afterChangeVirtualAmount);
            //rewardRecordModel.setAfterChangeVirtualAmount(afterChangeVirtualAmount.add(score));
            rewardRecordModel.setAfterChangeVirtualAmount(afterChangeVirtualAmount.add(rewardRecordModel.getAmount()));
            rewardRecordModel.setCreateTime(new Date());
            rewardRecordModel.setType(2);
            rewardRecordModel.setSysState(99);
            log.info(JSONObject.toJSONString(rewardRecordModel));
            //if(i == rewardRecordModelList.size()-1){
            //terminalRewardRecordDao.insert(rewardRecordModel);
            //}
        }
        // memberShopDao.subtractVirtualAmount(134535,score);
        log.info("终端ID为：{}结束计算积分,共{}条记录，加{}积分", 134535, rewardRecordModelList.size(), score);
        //}
    }

    @Autowired
    private TerminalAccountManagerService terminalAccountManagerService;

    @Autowired
    private TerminalShopService terminalShopService;

    @Autowired
    TerminalVisitRecordService terminalVisitRecordService;

    @Test
    public void test6() {

        TerminalAccountManagerReq req = new TerminalAccountManagerReq();
        req.setBelongTo("35517");
        req.setAgentCode("1234");
        req.setName("测试发展");
        req.setParentId(27);
        req.setPhone("***********");
        req.setPostId(96);
        req.setCompanyId(50);
        req.setDeptCode(1);
        req.setId(315);
        req.setType(2);
        /*req.setId(286);*/
        // terminalAccountManagerService.insertAccountManager(req);
        //  terminalAccountManagerService.updateAccountStaffById(req);
       /* TerminalAccountManagerAdminReq adminReq = new TerminalAccountManagerAdminReq();
        adminReq.setCompanyId(50);
        List<TerminalAccountManagerByStaffAdminResp> selectList = terminalAccountManagerService.selectTerminalAccountManagerByStaffAdmin(adminReq,1,10);
*/
        /*TerminalAccountManagerByStaffAdminResp adminResp = terminalAccountManagerService.selectTerminalAccountManagerByStaffByIdAdmin(293);
        log.info(JSONObject.toJSONString(adminResp));*/


        terminalAccountManagerService.updateAccountStaffById(req);
        // terminalShopService.bindTerminalShopAndAccountExecutive(44512,283);

        /*  terminalVisitRecordService.sysDisplayImgToZt(null);*/

        /* sysTerminalScoreTask.compareTerminalScoreRecord();*/
    }

    @Autowired
    private ICloudDealerOutBalanceService cloudDealerOutBalanceService;

    @Autowired
    private IDisplayResultService displayResultService;

    @Test
    public void test8() {
        OrderReq req = new OrderReq();
        req.setAccountManagerId(27);
        req.setIsMember(1);
        req.setReceivingStatus(0);
        PageInfo<CloudDealerOutBalanceResp> listByPage = cloudDealerOutBalanceService.getListByPage(req, 1, 10);
        log.info(JSONObject.toJSONString(listByPage));
    }

    @Test
    public void test9() throws IOException {
       /* CloudDealerOutBalanceDetailResp resp = cloudDealerOutBalanceService.getCloudDealerOutBalanceDetailById(1234l);
        log.info(JSONObject.toJSONString(resp));*/
        File file = new File("C:\\Users\\<USER>\\Desktop\\导入账户.xlsx");
        MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), null, new FileInputStream(file));
        terminalAccountManagerService.importAccountManager(multipartFile);
    }

    @Test
    public void test10() throws IOException {
        /*TerminalAccountManagerAdminReq req = new TerminalAccountManagerAdminReq();
        req.setCompanyId(50);
        List<TerminalAccountManagerByStaffAdminResp> selectList = terminalAccountManagerService.selectTerminalAccountManagerByStaffAdmin(req,1,10);
*/
        DisplayResultReq req = new DisplayResultReq();
        req.setMonthYear("2023-11");
        req.setQualifyFlag(1);
       /* List<DisplayResultResp> list = displayResultService.selectResultList(req, 1, 10);
        log.info("有:{}", list.size());*/
        // terminalShopService.bindTerminalShopAndAccountExecutive(45140,249,66);

        displayResultService.export(req);


    }

    @Test
    public void test11() {
    }

    @Test
    public void test13() {
        /*TerminalShopNodeReq req = new TerminalShopNodeReq();
        req.setUpdateUser(27);
        req.setUserType(0);
        req.setCompanyId(50);
        req.setItemType(6);
        List<TerminalShopNodeResp> list = terminalShopService.selectTerminalShopNode(req, 1, 10);
        System.out.println(list.size());*/

        OrderNodeResp orderNodeResp = terminalShopService.selectShOrderByNodeId(628);


    }


    @Test
    public void test16() throws Exception {
        TerminalAccountManagerModel accountManagerModel = terminalAccountManagerDao.selectById(8996);
        if (accountManagerModel.getType() == 2 && accountManagerModel.getType() == 3) {
            //只有经销商人员、业代、客户经理可看
            System.out.println("22112");
        }
        log.info("登录人信息accountManagerModel-terminalVisitRecord-getListByPage：{}", JSONObject.toJSONString(accountManagerModel));
        TerminalVisitRecordReq req = new TerminalVisitRecordReq();
        req.setPage(1);
        req.setLimit(10);
        req.setCreateDate("2023-08");
        req.setShopId(165602);
        req.setCompanyId(accountManagerModel.getCompanyId());
        req.setAccountId(accountManagerModel.getId());
        req.setAccountType(accountManagerModel.getType());
        req.setBelongTo(accountManagerModel.getBelongTo());
        if (req.getAccountType() == 0) {
            req.setAccountManagerId(accountManagerModel.getId());
        } else if (req.getAccountType() == 4 || req.getAccountType() == 5 || req.getAccountType() == 6) {
            req.setAccountManagerId(accountManagerModel.getParentId());
        }
        // PageInfo<TerminalVisitRecordResp> pageInfo = terminalVisitRecordService.selectList4App(req);

        // PageInfo<TerminalVisitShopResp> memberShopListByPage = terminalVisitRecordService.getMemberShopListByPage(req);

    }


}
