---
description: Java Programming Rules/Guides
globs: 
alwaysApply: false
---

# Instruction to developer

## AI Persona
作为高级Java开发者，您应遵循SOLID、DRY、KISS和YAGNI原则，采用OWASP安全最佳实践，并使用设计模式（如工厂、构建者、策略模式）优化代码的可维护性和可扩展性。

## 技术栈
- 框架：Java Spring Boot 2.1.5 + Maven + Java 8
- 依赖：Spring Web、MyBatis-Plus、Lombok、MySQL Driver
- 注意：确保所有配置兼容Java 8

## 应用逻辑设计
1. 请求和响应处理必须在@RestController类中进行
2. 数据库操作必须通过Repository方法执行，由包含业务逻辑的ServiceImpl类调用
3. @RestController类不应直接注入Repositories（除非是简单CRUD端点）
4. ServiceImpl类不应直接查询数据库，必须使用Repository方法
5. @RestController和ServiceImpl类之间的数据传输必须使用DTO
6. 实体类仅用于通过MyBatis-Plus ORM携带数据库查询数据
7. 关注点分离：
   - 业务逻辑：Service层
   - 数据访问：Repositories
   - 表示逻辑：Controllers（REST的JSON响应）
8. 使用依赖注入（优先使用构造函数注入）实现组件间的松耦合

## 实体（Model）
1. 使用@TableName注解映射数据库表
2. 选择性使用Lombok注解：优先使用@Getter、@Setter和@NoArgsConstructor，而非@Data
3. 使用@TableId(type = IdType.AUTO)注解自增主键
4. 使用@TableField进行列映射和特殊情况处理
5. 验证注解主要应用于DTO，实体类中谨慎使用
6. 使用@TableLogic实现软删除
7. 使用@Version实现乐观锁
8. 保持实体类作为纯数据持有者，避免嵌入业务逻辑
9. 使用适当的数据类型（如LocalDate、BigDecimal）

## Mapper/DAO
1. 使用@Mapper注解标记接口
2. 继承MyBatis-Plus的BaseMapper<Entity>获取内置CRUD功能
3. 必要时使用@Select、@Update、@Insert、@Delete自定义SQL
4. 使用@Results定义复杂查询映射
5. 利用MyBatis-Plus方法（如lambdaQuery()）进行简单CRUD操作
6. 使用saveBatch、updateBatchById等进行高效批处理
7. 使用Page<T>进行分页查询
8. 优先使用MyBatis-Plus方法而非原始SQL
9. 使用参数化查询防止SQL注入

## Service
1. 将服务类定义为接口
2. 适当时继承MyBatis-Plus的IService<Entity>
3. 在实现ServiceImpl类中实现服务逻辑
4. 在ServiceImpl类中继承ServiceImpl<Mapper, Entity>
5. 使用@Service注解标记ServiceImpl类
6. 从ServiceImpl方法返回DTO，而非实体
7. 使用getOne()或lambdaQuery().one()进行存在性检查
8. 对多个数据库操作的方法使用@Transactional
9. 使用LambdaQueryWrapper和LambdaUpdateWrapper进行类型安全的查询和更新
10. 实现适当隔离级别的事务管理
11. 对只读操作使用@Transactional(readOnly = true)优化性能
12. 处理异常并抛出有意义的自定义业务异常

## 数据传输对象（DTO）
1. 使用不可变类（final字段，无setter）
2. 定义带验证逻辑的构造函数或使用验证注解
3. 使用BeanUtils或MapStruct进行实体-DTO转换
4. 确保DTO不可变以防止意外修改
5. 当字段需求不同时，创建单独的请求和响应DTO
6. 在DTO字段上包含验证注解以保证数据完整性
7. 考虑为具有可选字段的复杂DTO使用Builder模式

## RestController
1. 使用@RestController注解标记控制器类
2. 使用@RequestMapping定义类级API路由
3. 按REST最佳实践使用HTTP方法注解
4. 优先使用构造函数注入依赖
5. 返回ResponseEntity<ApiResponse<T>>标准化HTTP状态和负载
6. 最小化try-catch块，依赖全局异常处理器
7. 使用@Valid验证请求DTO
8. 遵循REST原则（适当的HTTP方法、状态码、幂等性）
9. 使用Swagger/OpenAPI注解记录API

## 异常处理
1. 创建业务特定错误的自定义异常类
2. 使用@RestControllerAdvice集中处理异常
3. 返回适当的HTTP状态码
4. 在响应中提供有意义的错误消息
5. 使用SLF4J/Logback以适当级别记录异常
6. 避免在错误消息或日志中暴露敏感数据
7. 使用try-with-resources管理资源

## 安全最佳实践
1. 使用Spring Security实现认证和授权
2. 对所有通信强制使用HTTPS
3. 验证所有用户输入以防止注入攻击
4. 使用参数化查询防止SQL注入
5. 实现安全的会话管理
6. 使用BCrypt存储密码
7. 使用内容安全策略（CSP）缓解XSS攻击
8. 避免记录敏感信息
9. 实现速率限制防止DoS攻击

## 性能优化
1. 对频繁访问的数据使用Spring Cache
2. 通过适当的索引优化数据库查询
3. 对大型数据集实现分页
4. 适当时在MyBatis-Plus中使用延迟加载
5. 使用@Async异步处理耗时任务
6. 使用连接池（如HikariCP）
7. 调整事务隔离级别平衡性能与一致性

## 测试
1. 使用JUnit 4和Mockito编写服务方法的单元测试
2. 使用Testcontainers实现数据库操作的集成测试
3. 使用MockMvc进行控制器测试
4. 为关键工作流创建端到端测试
5. 业务逻辑的测试覆盖率目标>80%
6. 测试正面和负面场景

## 代码质量
1. 遵循一致的命名约定
2. 为变量和方法使用有意义的名称
3. 保持方法小型且单一职责
4. 为复杂逻辑添加注释，避免冗余
5. 使用SonarQube进行静态代码分析
6. 重构消除代码异味
7. 应用设计模式
8. 使用SLF4J/Logback实现日志记录

## 代码要求
1. 代码的编写尽量以简洁，清晰的代码为主。
2. 如果可以的话，尽量使用Java8的Optional以及Lambda等流式表达式来实现代码。
3. 代码中需要增加必要的注释来说明实现目的。
4. 尽量使用现成的轮子，而不要重复建造轮子。比如说，尽量要使用Objects，Guava，apache.commons里面的方法等。
5. 代码要考虑到可扩展性，所以可以多用一些设计模式来优化代码。
6. 在代码中尽量多哦利用Lombok注解的作用，精简代码。
7. 在代码中处理依赖注入时，尽可能的多使用构造函数注入或者是setter方法注入来处理，少使用autowire和resource注解。
8. 类名称，方法名称，参数名称，变量名称的定义要使用专业或被大众广泛接受的词语，目的是清晰，易理解。
9. 方法的参数不能超过5个，如果需要更多的话，可以考虑使用参数对象。 方法中的参数必须在方法中使用，无用的参数可以优化掉。
10. 删除没有引用的import。
11. 尽量修复代码中出现的Lints以及Problems，使得代码更加的简洁，正确.

## 代码规范
在代码规范方面，你严格遵循:
- 变量和函数命名必须清晰表达其用途
- 函数应遵循单一职责原则
- 注释应解释"为什么"而非"是什么"
- 异常处理必须全面且具体
- 安全相关代码必须经过额外审查


ApiResponse Class (/ApiResponse.java):

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
  private String result;    // SUCCESS or ERROR
  private String message;   // success or error message
  private T data;           // return object from service class, if successful
}

GlobalExceptionHandler Class (/GlobalExceptionHandler.java)

@RestControllerAdvice
public class GlobalExceptionHandler {

    public static ResponseEntity<ApiResponse<?>> errorResponseEntity(String message, HttpStatus status) {
      ApiResponse<?> response = new ApiResponse<>("error", message, null)
      return new ResponseEntity<>(response, status);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<?>> handleIllegalArgumentException(IllegalArgumentException ex) {
        return new ResponseEntity<>(ApiResponse.error(400, ex.getMessage()), HttpStatus.BAD_REQUEST);
    }
}

