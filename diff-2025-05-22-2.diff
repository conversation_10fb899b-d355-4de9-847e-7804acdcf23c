diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/ActivityRewardRecordServiceImpl.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/ActivityRewardRecordServiceImpl.java
index 55acf56d81..e005655667 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/ActivityRewardRecordServiceImpl.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/ActivityRewardRecordServiceImpl.java
@@ -867,26 +867,23 @@ public class ActivityRewardRecordServiceImpl extends ServiceImpl<ActivityRewardR
         if (!req.getStatus().equals("1") && !req.getStatus().equals("2")) {
             throw new RuntimeException("状态异常，与定义数据不匹配");
         }
-        ActivityRewardRecordModel model = activityRewardRecordDao.selectOne(new QueryWrapper<ActivityRewardRecordModel>()
+
+        //记录回调记录
+        recordZtCallBackInfo(req);
+
+        ActivityRewardRecordModel model = Optional.ofNullable(this.getOne(new QueryWrapper<ActivityRewardRecordModel>()
                 .lambda()
                 .and(query -> query.eq(ActivityRewardRecordModel::getBusinessId, req.getBusiness_id()).or().eq(ActivityRewardRecordModel::getId, req.getBusiness_id()))
-                .last("limit 1"));
+                .orderByDesc(ActivityRewardRecordModel::getId), false))
+                .orElseThrow(() -> {
+                    log.info("根据回调数据business_id:{}查询到的奖励记录为空", req.getBusiness_id());
+                    return new BusinessException(String.format("根据回调数据business_id:%s查询到的奖励记录为空", req.getBusiness_id()));
+                });
+
+        log.info("根据回调数据business_id:{}查询到的奖励记录:{}", req.getBusiness_id(), model);
         if (SysState.SEND_SUCCESS.getCode().equals(model.getSysState())
                 || SysState.ZT_HANDLE_FAIL.getCode().equals(model.getSysState())
                 || SysState.SEND_FAIL.getCode().equals(model.getSysState())) {
-            //处理回调记录
-            ZtBookRewardRecordModel ztBookRewardRecordModel = ztBookRewardRecordService.getOne(new QueryWrapper<ZtBookRewardRecordModel>().lambda().eq(ZtBookRewardRecordModel::getBusinessId, req.getBusiness_id()).eq(ZtBookRewardRecordModel::getIsDelete, 0).orderByDesc(ZtBookRewardRecordModel::getCreateTime).last("limit 1"));
-            if (Objects.nonNull(ztBookRewardRecordModel)) {
-                if (!req.getStatus().equals("1")) {// 预算失败
-                    ztBookRewardRecordModel.setSysState(SysState.ZT_HANDLE_FAIL.getCode());
-                } else {
-                    ztBookRewardRecordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
-                }
-                ztBookRewardRecordModel.setCallState(1);
-                ztBookRewardRecordModel.setCallbackJson(new Gson().toJson(req));
-                ztBookRewardRecordModel.setCallbackTime(new Date());
-                ztBookRewardRecordService.updateById(ztBookRewardRecordModel);
-            }
             if (SysState.ZT_HANDLE_FAIL.getCode().equals(model.getSysState()) || SysState.SEND_FAIL.getCode().equals(model.getSysState())) { //重复回调失败数据
                 model.setSysState(SysState.SEND_SUCCESS.getCode());
             }
@@ -900,6 +897,7 @@ public class ActivityRewardRecordServiceImpl extends ServiceImpl<ActivityRewardR
                     && Objects.equals(model.getRewardType(), RewardType.SEND_SCORE_TYPE.getCode())
                     && Objects.equals(model.getSysState(), SysState.SEND_SUCCESS.getCode())) {
                 //动销奖励
+                log.info("动销奖励积分回调，开始处理");
                 asyncActivityRewardRecordService.handleDxReward(model);
             } else if (Objects.equals(model.getActivityType(), ActivityType.OPEN_BOTTLE_REWARD.getCode())
                     && Objects.equals(model.getRewardType(), RewardType.SEND_SCORE_TYPE.getCode())
@@ -924,6 +922,7 @@ public class ActivityRewardRecordServiceImpl extends ServiceImpl<ActivityRewardR
                     || Objects.equals(model.getEventType(), EventType.DEALER_SALE_REWARD_OPERATIONAL_DEDUCT.getCode()))) {
                 //开瓶奖励
                 if (model.getId() != 691667 && model.getId() != 691708)  //这两条数据特殊处理
+                    log.info("开瓶奖励积分回调，开始处理");
                     asyncActivityRewardRecordService.handleOpenReward(model);
             }
         }
@@ -3731,6 +3730,26 @@ public class ActivityRewardRecordServiceImpl extends ServiceImpl<ActivityRewardR
          }
      }
 
+    /**
+     * 更新中台账本奖励记录
+     *
+     * @param req 请求参数
+     */
+    private void recordZtCallBackInfo(ZTActivityRewardRecordReq req) {
+        ZtBookRewardRecordModel ztBookRewardRecordModel = ztBookRewardRecordService.getOne(new QueryWrapper<ZtBookRewardRecordModel>().lambda().eq(ZtBookRewardRecordModel::getBusinessId, req.getBusiness_id()).eq(ZtBookRewardRecordModel::getIsDelete, 0).orderByDesc(ZtBookRewardRecordModel::getCreateTime), false);
+        if (Objects.nonNull(ztBookRewardRecordModel)) {
+            if (!req.getStatus().equals("1")) {// 预算失败
+                ztBookRewardRecordModel.setSysState(SysState.ZT_HANDLE_FAIL.getCode());
+            } else {
+                ztBookRewardRecordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
+            }
+            ztBookRewardRecordModel.setCallState(1);
+            ztBookRewardRecordModel.setCallbackJson(new Gson().toJson(req));
+            ztBookRewardRecordModel.setCallbackTime(new Date());
+            ztBookRewardRecordService.updateById(ztBookRewardRecordModel);
+        }
+    }
+
 
     private List<ActivityRewardRecordModel> getReissuedDealerOpenBottleRewards(List<String> qrCodes) {
         LambdaQueryWrapper<ActivityRewardRecordModel> queryWrapper = new LambdaQueryWrapper<>();
diff --git a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/AsyncActivityRewardRecordServiceImpl.java b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/AsyncActivityRewardRecordServiceImpl.java
index dccced41e0..9003efde44 100644
--- a/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/AsyncActivityRewardRecordServiceImpl.java
+++ b/shop-common/src/main/java/com/intelliquor/cloud/shop/common/service/impl/AsyncActivityRewardRecordServiceImpl.java
@@ -97,14 +97,19 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
         ExtendDataBean extendDataBean = JSONObject.parseObject(activityRewardRecordModel.getExtendData(), ExtendDataBean.class);
         //奖励重复处理判断
         String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_BOTTLE_KEY, activityRewardRecordModel.getId());
-        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, activityRewardRecordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
-        if (!aBoolean) {
-            return;
-        }
-
-        TerminalScanDetailModel terminalScanDetailModel = terminalScanDetailDao.selectById(extendDataBean.getScanDetailId());
-
+        Boolean lockAcquired = false;
+        
         try {
+            log.info("开瓶奖励回调，奖励重复处理判断, redis的key为:{}", key);
+            lockAcquired = redisTemplate.opsForValue().setIfAbsent(key, activityRewardRecordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
+            if (Boolean.FALSE.equals(lockAcquired)) {
+                log.info("开瓶奖励回调，已处理过奖励，不能重复处理, redis的key为:{}", key);
+                return;
+            }
+
+            TerminalScanDetailModel terminalScanDetailModel = terminalScanDetailDao.selectById(extendDataBean.getScanDetailId());
+            log.info("开瓶奖励回调，查询终端扫码记录:{}", terminalScanDetailModel);
+            
             if (activityRewardRecordModel.getEventType().equals(EventType.DEALER_OPEN_BOTTLE_REWARD.getCode())
                     || activityRewardRecordModel.getEventType().equals(EventType.DEALER_FENCE_VIOLATE_OPEN_BOTTLE_REWARD.getCode())
                     || activityRewardRecordModel.getEventType().equals(EventType.DEALER_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
@@ -131,7 +136,19 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
             }
         } catch (Exception e) {
             e.printStackTrace();
-            log.error("处理开瓶奖励失败:" + e.getMessage(), e);
+            log.error("处理开瓶奖励失败:{}", e.getMessage(), e);
+        } finally {
+            // 根据业务需求决定是否释放锁
+            // 当前设计中保留了过期时间作为兜底措施，不主动释放锁
+            // 如果需要主动释放锁（例如失败后需人工干预的场景），可以取消下面注释
+            if (Boolean.TRUE.equals(lockAcquired)) {
+                try {
+                    redisTemplate.delete(key);
+                    log.debug("释放Redis锁: {}", key);
+                } catch (Exception e) {
+                    log.warn("释放Redis锁失败: {}", key, e);
+                }
+            }
         }
     }
 
@@ -143,6 +160,7 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
     @Async
     @Transactional(rollbackFor = Exception.class)
     public void handleDxReward(ActivityRewardRecordModel model) {
+        log.info("动销奖励回调，处理动销奖励");
         if (model.getEventType().equals(EventType.DEALER_SALE_REWARD.getCode())
                 || model.getEventType().equals(EventType.DEALER_VIOLATE_SALE_PUNISH.getCode())
                 || model.getEventType().equals(EventType.DEALER_VIOLATE_SALE_REFUND.getCode())
@@ -226,6 +244,7 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
 
     @Transactional
     public void handleDealerBottleReward(ActivityRewardRecordModel recordModel, TerminalScanDetailModel detail) {
+        log.info("开瓶奖励回调，处理经销商的开瓶奖励");
         //码信息重复奖励判断
         LambdaQueryWrapper<CloudDealerRewardRecordModel> clqw = Wrappers.lambdaQuery();
         clqw.eq(CloudDealerRewardRecordModel::getOrderCode, recordModel.getOrderCode());
@@ -246,6 +265,7 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
 
             // 3. 如果存在重复记录，更新当前记录状态并返回
             if (alreadyExisted) {
+                log.info("开瓶奖励回调，处理经销商的开瓶奖励，此码重复发放奖励");
                 recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
                 String currentTime = DateUtils.convert2StringYYYYMMddHHmmss(new Date());
                 recordModel.setSendMsg(recordModel.getSendMsg() + ",此码重复发放奖励:" + currentTime);
@@ -282,18 +302,21 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
         insertData.setDetailId(recordModel.getOriginId());
         insertData.setStatus(1);
         cloudDealerRewardRecordDao.insert(insertData);
+        log.info("开瓶奖励回调，处理经销商的开瓶奖励，插入经销商奖励记录成功");
         recordModel.setSendStatus(SendStatus.SUCCESS_TYPE.getCode());
         recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
         recordModel.setSendMsg("发放奖励成功:" + DateUtils.convert2StringYYYYMMddHHmmss(new Date()));
         extendDataBean.setOldRewardId(insertData.getId());
         recordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
         activityRewardRecordDao.updateById(recordModel);
+        log.info("开瓶奖励回调，处理经销商的开瓶奖励，更新活动记录成功");
 
         ZtBookRewardRecordModel ztBookRewardRecordModel = ztBookRewardRecordService.getOne(new QueryWrapper<ZtBookRewardRecordModel>().lambda().eq(ZtBookRewardRecordModel::getBusinessId, recordModel.getBusinessId()).eq(ZtBookRewardRecordModel::getCallState, 1).eq(ZtBookRewardRecordModel::getIsDelete, 0).orderByDesc(ZtBookRewardRecordModel::getCreateTime).last("limit 1"));
         if (Objects.nonNull(ztBookRewardRecordModel)) {
             ztBookRewardRecordModel.setCallState(2);
             ztBookRewardRecordModel.setUpdateTime(new Date());
             ztBookRewardRecordService.updateById(ztBookRewardRecordModel);
+            log.info("开瓶奖励回调，处理经销商的开瓶奖励，更新中台请求记录成功");
         }
     }
 
@@ -314,21 +337,20 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
     }
 
     private void handleTerminalBottleReward(ActivityRewardRecordModel recordModel, TerminalScanDetailModel detail) {
-
+        log.info("开瓶奖励回调，处理终端的开瓶奖励");
         TerminalScanDetailModel updateDetail = new TerminalScanDetailModel();
         updateDetail.setId(detail.getId());
         updateDetail.setVirtualAmount(recordModel.getIntegral());
         updateDetail.setActivityId(recordModel.getActivityId());
         terminalScanDetailPlusDao.updateById(updateDetail);
 
-        recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
-        recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
-        recordModel.setUpdateTime(new Date());
-        activityRewardRecordDao.updateById(recordModel);
+        log.info("开瓶奖励回调，处理终端的开瓶奖励，更新终端扫码记录");
+        // 使用通用方法更新记录状态并同步
+        ActivityRewardRecordModel latestRecord = updateRecordAndFetchLatest(recordModel, SysState.ZT_HANDLE_SUCCESS, SendStatus.DOING_TYPE);
 
         // 发送消息通知终端账户处理积分
-        sendTerminalAccountScoreMessage(recordModel);
-
+        log.info("开瓶奖励回调，处理终端的开瓶奖励，发送消息通知终端账户处理积分");
+        sendTerminalAccountScoreMessage(latestRecord);
     }
 
     private TerminalRewardRecordSourceEnum getTerminalRewardRecordSourceEnum(ActivityRewardRecordModel recordModel) {
@@ -350,18 +372,16 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
     @Transactional(rollbackFor = Exception.class)
     public void handleDistributorBottleReward(ActivityRewardRecordModel recordModel, TerminalScanDetailModel
             detail) {
+        log.info("开瓶奖励回调，处理分销商的开瓶奖励");
+        // 使用通用方法更新记录状态并同步
+        ActivityRewardRecordModel latestRecord = updateRecordAndFetchLatest(recordModel, SysState.ZT_HANDLE_SUCCESS, SendStatus.DOING_TYPE);
 
-        recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
-        recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
-        recordModel.setUpdateTime(new Date());
-        activityRewardRecordDao.updateById(recordModel);
-
-        sendTerminalAccountScoreMessage(recordModel);
-
+        sendTerminalAccountScoreMessage(latestRecord);
     }
 
     @Transactional
     public void handleDealerSaleReward(List<ActivityRewardRecordModel> dealerRecordList) {
+        log.info("动销奖励回调，处理经销商动销奖励");
         if (!CollectionUtils.isEmpty(dealerRecordList)) {
             for (ActivityRewardRecordModel recordModel : dealerRecordList) {
                 String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_SALE_DEALER_KEY, recordModel.getId());
@@ -494,40 +514,40 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
 
     @Transactional
     public void handleDistributorSaleReward(List<ActivityRewardRecordModel> distributorRecordList) {
+        log.info("动销奖励回调，处理分销商动销奖励");
         if (!CollectionUtils.isEmpty(distributorRecordList)) {
             for (ActivityRewardRecordModel recordModel : distributorRecordList) {
                 String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_SALE_DISTRIBUTOR_KEY, recordModel.getId());
                 Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, recordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
                 if (!aBoolean) {
-                    log.info("已处理过奖励，不能重复处理:{}", JSONObject.toJSONString(recordModel));
+                    log.info("动销奖励回调，已处理过奖励，不能重复处理, redis的key为:{}", key);
                     return;
                 }
 
-                recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
-                recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
-                recordModel.setUpdateTime(new Date());
-                activityRewardRecordDao.updateById(recordModel);
+                log.info("动销奖励回调，处理分销商动销奖励, 更新活动奖励记录状态");
+                // 使用通用方法更新记录状态并同步
+                ActivityRewardRecordModel latestRecord = updateRecordAndFetchLatest(recordModel, SysState.ZT_HANDLE_SUCCESS, SendStatus.DOING_TYPE);
 
-                sendTerminalAccountScoreMessage(recordModel);
+                sendTerminalAccountScoreMessage(latestRecord);
             }
         }
     }
 
     @Transactional
     public void handleTerminalSaleReward(List<ActivityRewardRecordModel> terminalRecordList, Boolean isMember) {
+        log.info("动销奖励回调，处理{}动销奖励", isMember ? "会员" : "终端");
         if (!CollectionUtils.isEmpty(terminalRecordList)) {
             for (ActivityRewardRecordModel recordModel : terminalRecordList) {
                 String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_SALE_TERMINAL_KEY, recordModel.getId());
                 Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, recordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
                 if (!aBoolean) {
-                    log.info("已处理过奖励，不能重复处理:{}", JSONObject.toJSONString(recordModel));
+                    log.info("动销奖励回调，已处理过奖励，不能重复处理, redis的key为:{}", key);
                     return;
                 }
 
-
                 if(recordModel.getEventType().equals(EventType.DEALER_SALE_REWARD.getCode())){
                     TerminalScanDetailModel detail = terminalScanDetailPlusDao.selectById(recordModel.getOriginId());
-
+                    log.info("动销奖励回调，处理{}动销奖励，更新终端扫码记录", isMember ? "会员" : "终端");
                     TerminalScanDetailModel updateDetail = new TerminalScanDetailModel();
                     updateDetail.setId(detail.getId());
                     updateDetail.setVirtualAmount(recordModel.getIntegral());
@@ -535,14 +555,11 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
                     terminalScanDetailPlusDao.updateById(updateDetail);
                 }
 
-//                shopDao.addVirtualAmount(model.getAmount(), model.getShopId());
+                log.info("动销奖励回调，处理{}动销奖励，更新活动奖励记录状态", isMember ? "会员" : "终端");
+                // 使用通用方法更新记录状态并同步
+                ActivityRewardRecordModel latestRecord = updateRecordAndFetchLatest(recordModel, SysState.ZT_HANDLE_SUCCESS, SendStatus.DOING_TYPE);
 
-                recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
-                recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
-                recordModel.setUpdateTime(new Date());
-                activityRewardRecordDao.updateById(recordModel);
-
-                sendTerminalAccountScoreMessage(recordModel);
+                sendTerminalAccountScoreMessage(latestRecord);
             }
         }
     }
@@ -644,6 +661,7 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
         updateModel.setId(recordModel.getId());
         updateModel.setSendStatus(SendStatus.FAIL_TYPE.getCode());
         updateModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
+        updateModel.setUpdateTime(new Date());
         String sendMsg = "发放奖励失败:" + reason + ":" + DateUtils.convert2StringYYYYMMddHHmmss(new Date());
         updateModel.setSendMsg(sendMsg.substring(0, Math.min(sendMsg.length(), 250)));
 
@@ -698,4 +716,22 @@ public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityReward
             throw e;
         }
     }
+
+    // 新增私有方法用于更新记录状态并从数据库查询最新记录
+    private ActivityRewardRecordModel updateRecordAndFetchLatest(ActivityRewardRecordModel recordModel,
+                                           SysState sysState,
+                                           SendStatus sendStatus) {
+        log.info("更新活动奖励记录状态，记录ID: {}", recordModel.getId());
+        ActivityRewardRecordModel updateModel = new ActivityRewardRecordModel();
+        updateModel.setId(recordModel.getId());
+        updateModel.setSysState(sysState.getCode());
+        updateModel.setSendStatus(sendStatus.getCode());
+        updateModel.setUpdateTime(new Date());
+        activityRewardRecordDao.updateById(updateModel);
+
+        // 从数据库重新查询最新记录
+        ActivityRewardRecordModel latestRecord = activityRewardRecordDao.selectById(recordModel.getId());
+        log.info("活动奖励记录状态更新并查询最新记录成功，记录ID: {}, sysState: {}, sendStatus: {}", latestRecord.getId(), latestRecord.getSysState(), latestRecord.getSendStatus());
+        return latestRecord;
+    }
 }
